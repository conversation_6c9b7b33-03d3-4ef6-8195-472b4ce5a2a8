.container {
  width: 350px;
  height: 100px;
  border: 1px solid lightgray;
  color: gray;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;

  &:hover {
    border: 1px solid gray;
    color: #5b5a5a;
  }
}
.headerTitle {
  display: flex;
  height: 40px;
  justify-content: space-between;
  align-items: center;
  background: #f0f2f5;
  padding: 0 15px;

  > span {
    margin-right: 8px;
  }
}

.part1 {
  display: flex;
  justify-content: flex-start;
}

.part1_p1 {
  display: -webkit-box;
  width: 100%;
  margin: 0;
  overflow: hidden;
  line-height: 1.5 !important;
  text-align: left;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.part1_p2 {
  margin: 0;
  color: #8e97a2;
  text-align: left;
}
$ERROR_COLOR: #f33;
$WARNING_COLOR: #f90;
$SUCCESS_COLOR: #0b6;
$INFO_COLOR: #39f;
.table-status-WARNING_COLOR {
  // 黄色字体
  color: $WARNING_COLOR;
}

.table-status-INFO_COLOR {
  // 蓝色字体
  color: $INFO_COLOR;
}

.table-status-SUCCESS_COLOR {
  // 绿色字体
  color: $SUCCESS_COLOR;
}

