import { Radio, Form, Button, NumberPicker, Input, Dialog, Grid, Field, Message, Icon } from '@alifd/next';
import React, { useState, useReducer, useEffect } from 'react';
import Plan from './components/Plan';
import active from '../assets/active.png';
import notActive from '../assets/not-active.png';
import delIcon from '../assets/del-icon.png';
import styles from './index.module.scss';
import LzImageSelector from '@/components/LzImageSelector';
import format from '@/utils/format';
import { prizeFormLayout } from '@/components/ChoosePrize';
import { activityEditDisabled, deepCopy } from '@/utils';

interface ComponentProps {
  [propName: string]: any;
}

// eslint-disable-next-line complexity
const PropertyJdCoupon = ({
  editValue,
  onChange,
  onCancel,
  hasProbability = true,
  hasLimit = true,
  hasOrderPrice = false,
  planList = [],
  width,
  height,
  prizeNameLength,
  formData,
  sendTotalCountMax = 999999999,
}: ComponentProps) => {
  const equityImg = '//img10.360buyimg.com/imgzone/jfs/t1/215393/22/4731/33329/61946651E535ea01f/5cee5951d6bd1612.png';
  // const planImg = require('../assets/3.jpg');
  const defaultValue = {
    ifPlan: 1,
    prizeKey: '',
    prizeType: 1,
    dayLimitType: 1,
    dayLimit: 1,
    prizeImg: equityImg,
    // couponList: [],
  };
  const [prizeData, setPrizeData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, editValue || defaultValue);
  const field = Field.useField();

  const [winJdShow, setWinJdShow] = useState(false); // 京豆详情
  // 五家店铺的优惠券数据
  const [diffShopCouponList, setDiffShopCouponList] = useState([
    {
      shopId: 1000384206,
      shopName: '波司登京东自营旗舰店',
      planName: '',
      rangeType: 1,
      couponType: '',
      couponQuota: 0,
      couponDiscount: 0,
      numPerSending: 0,
      quantityRemain: 0,
      startTime: '',
      endTime: '',
      planStatus: '',
    },
    {
      shopId: 44892,
      shopName: '波司登官方旗舰店',
      planName: '',
      rangeType: 1,
      couponType: '',
      couponQuota: 0,
      couponDiscount: 0,
      numPerSending: 0,
      quantityRemain: 0,
      startTime: '',
      endTime: '',
      planStatus: 0,
    },
    {
      shopId: 12622397,
      shopName: '波司登户外官方旗舰店',
      planName: '',
      rangeType: 1,
      couponType: '',
      couponQuota: 0,
      couponDiscount: 0,
      numPerSending: 0,
      quantityRemain: 0,
      startTime: '',
      endTime: '',
      planStatus: 0,
    },
    {
      shopId: 12558108,
      shopName: '波司登奥特莱斯旗舰店',
      planName: '',
      rangeType: 1,
      couponType: '',
      couponQuota: 0,
      couponDiscount: 0,
      numPerSending: 0,
      quantityRemain: 0,
      startTime: '',
      endTime: '',
      planStatus: 0,
    },
    {
      shopId: 82047,
      shopName: '波司登服饰官方旗舰店',
      planName: '',
      rangeType: 1,
      couponType: '',
      couponQuota: 0,
      couponDiscount: 0,
      numPerSending: 0,
      quantityRemain: 0,
      startTime: '',
      endTime: '',
      planStatus: 0,
    },
  ]);
  const [actIndex, setActIndex] = useState(0);
  const [currentActShopId, SetCurrentActShopId] = useState(0);
  // 发放份数/单次发放量
  const setData = (data: any) => {
    setPrizeData({
      ...prizeData,
      ...data,
    });
  };
  // 填写完资产点击确定
  const submit = (values: any, errors: any): boolean | void => {
    let selectCouponNum = false;
    // 剩余数量 / 每份发放份数
    for (let i = 0; i < diffShopCouponList.length; i++) {
      if (diffShopCouponList[i].planName) {
        selectCouponNum = true;
      }
      const item = diffShopCouponList[i];
      const dis = item.quantityRemain / item.numPerSending;
      if (prizeData.sendTotalCount > dis) {
        Message.error(`发放份数不能大于剩余最小份数`);
        return false;
      }
    }
    if (!selectCouponNum) {
      Message.error(`请选择优惠券`);
      return false;
    }
    if (hasProbability && prizeData.dayLimitType === 2 && prizeData.sendTotalCount < prizeData.dayLimit) {
      Message.error(`每日发放限额份数不能大于发放总份数，请重新设置`);
      return false;
    }

    if (hasProbability && +prizeData.probability + +formData.totalProbability >= 100) {
      Message.error(`中奖总概率不能超过100`);
      return false;
    }

    !errors && onChange({ ...prizeData, couponPrizeList: diffShopCouponList });
  };
  // 删除优惠券
  const delPlan = async (index) => {
    const couponDataList = deepCopy(diffShopCouponList);
    // 删除计划
    if (planList.indexOf(couponDataList[index].planId) > -1) {
      planList.splice(planList.indexOf(couponDataList[index].planId), 1);
    }
    const storageShopName = couponDataList[index].shopName;
    const storageShopId = couponDataList[index].shopId;
    couponDataList[index] = {
      shopName: storageShopName,
      shopId: storageShopId,
      planName: '',
      rangeType: 1,
      couponType: '',
      couponQuota: 0,
      couponDiscount: 0,
      numPerSending: 0,
      quantityRemain: 0,
      startTime: '',
      endTime: '',
    };
    setDiffShopCouponList(couponDataList);
  };
  // 确定选择优惠券
  const onSubmit = (resource: any): any => {
    console.log('resource', resource);
    if (!resource) {
      return;
    }
    // 选择计划判断
    if (planList.indexOf(resource.planId) === -1) {
      // 添加计划记录
      planList.push(resource.planId);
      resource.prizeKey = resource.planId.toString();
      resource.prizeName = resource.planName;
      resource.prizeType = 1;
      const newData = deepCopy(diffShopCouponList);
      // newData[actIndex].couponInfo = resource;
      newData[actIndex] = { ...newData[actIndex], ...resource };
      console.log('newData优惠券信息', newData);
      setDiffShopCouponList(newData);
      setWinJdShow(false);
      field.setErrors({ prizeKey: '' });
    } else {
      Message.error('该计划已被选择，请重新选择');
      return false;
    }
  };
  // 打开优惠券选择弹窗保存当前操作的店铺id和操作的当前的优惠券对象下标
  const handleSetWindowShow = (index, shopId) => {
    SetCurrentActShopId(shopId);
    setActIndex(index);
    setWinJdShow(true);
  };
  // 选好优惠券重新打开回显数据
  useEffect(() => {
    if (prizeData.couponPrizeList?.length > 0) {
      setDiffShopCouponList(prizeData.couponPrizeList);
    }
    // setDiffShopCouponList(prizeData.couponPrizeList);
  }, []);
  return (
    <div className={styles.PropertyJdCoupon}>
      <Form field={field} {...prizeFormLayout}>
        <Form.Item required style={{ paddingTop: '15px' }}>
          {diffShopCouponList.map((item, index) => {
            if (item.planName) {
              return (
                <div className={prizeData.planStatus === 2 ? styles.beanPrizeBg : styles.beanPrizeBgy}>
                  <img className={styles.prizeBg} src={item.planStatus === 2 ? active : notActive} alt="" />
                  <div
                    onClick={() => delPlan(index)}
                    style={{ backgroundImage: `url(${delIcon})` }}
                    className={styles.delIcon}
                  />
                  <div style={{ width: 210 }}>
                    <p>优惠券名称：{item.planName}</p>
                    <p>优惠券类型：{item.rangeType == 1 ? '店铺券' : '商品券'}</p>
                    <p style={{ whiteSpace: 'nowrap' }}>
                      优惠券信息：
                      {item.couponType ? `东券,满${item.couponQuota}元减${item.couponDiscount}元` : '京券'}
                    </p>
                    <p>每份数量：{item.numPerSending || 1}张</p>
                    <p>店铺名称：{item.shopName}</p>
                  </div>
                  <div style={{ paddingLeft: 60 }}>
                    <p>
                      领取时间：{format.formatDateTimeDayjs(item.startTime)}至{format.formatDateTimeDayjs(item.endTime)}
                    </p>
                    <p>剩余张数：{item.quantityRemain}张</p>
                    <p>剩余份数：{item.quantityRemain / (item.numPerSending || 1)}份</p>
                  </div>
                </div>
              );
            } else {
              return (
                <div
                  className={styles.selectActivity}
                  style={{ marginTop: 10 }}
                  onClick={() => handleSetWindowShow(index, item.shopId)}
                >
                  {item.shopName}
                  <Button type="primary">
                    <Icon type="add" />
                    选择优惠券
                  </Button>
                </div>
              );
            }
          })}
        </Form.Item>
        <Form.Item label="单份价值" required requiredMessage="请输入单份价值">
          <NumberPicker
            onChange={(unitPrice: any) => setData({ unitPrice, unitCount: 1 })}
            className={styles.formNumberPicker}
            type="inline"
            name="unitPrice"
            min={0}
            precision={2}
            max={9999999}
            value={prizeData.unitPrice}
          />
          元
        </Form.Item>
        {hasOrderPrice && (
          <Form.Item label="获奖消费额度" required requiredMessage="请输入获奖消费额度">
            <NumberPicker
              className={styles.formNumberPicker}
              onChange={(orderPrice: any) => setData({ orderPrice })}
              name="orderPrice"
              type="inline"
              min={0}
              max={9999999}
              precision={2}
              value={prizeData.orderPrice}
            />
            元
          </Form.Item>
        )}
        <Form.Item label="发放份数" required requiredMessage="请输入发放份数">
          <NumberPicker
            className={styles.formNumberPicker}
            type="inline"
            min={1}
            max={sendTotalCountMax}
            step={1}
            name="sendTotalCount"
            value={prizeData.sendTotalCount}
            onChange={(sendTotalCount) => setData({ sendTotalCount })}
            disabled={activityEditDisabled()}
          />
          份<div className="crm-warning">注：发放份数指本次投入活动中优惠券份数</div>
        </Form.Item>
        {hasProbability && (
          <Form.Item label="中奖概率" required requiredMessage="请输入中奖概率">
            <Input
              name="probability"
              value={prizeData.probability}
              addonTextAfter="%"
              onChange={(probability) => {
                if (probability) {
                  const regex = /^(\d|[1-9]\d|100)(\.\d{0,3})?$/;
                  if (regex.test(probability)) {
                    setData({ probability });
                  }
                } else {
                  setData({ probability: '' });
                }
              }}
              className={styles.formInputCtrl}
            />

            <div style={{ marginTop: 5 }}>中奖概率不建议为0%，易引发客诉，请慎重</div>
          </Form.Item>
        )}
        {hasLimit && (
          <Form.Item label="每日限额" required requiredMessage="请输入每日限额">
            {prizeData.dayLimitType === 2 && <Input htmlType="hidden" name="dayLimit" value={prizeData.dayLimit} />}
            <div>
              <Radio.Group defaultValue={prizeData.dayLimitType} onChange={(dayLimitType) => setData({ dayLimitType })}>
                <Radio id="1" value={1}>
                  不限制
                </Radio>
                <Radio id="2" value={2}>
                  限制
                </Radio>
              </Radio.Group>
              {prizeData.dayLimitType === 2 && (
                <div className={styles.panel}>
                  <NumberPicker
                    value={prizeData.dayLimit}
                    onChange={(dayLimit) => {
                      setData({ dayLimit });
                      field.setErrors({ dayLimit: '' });
                    }}
                    type="inline"
                    min={1}
                    max={9999999}
                    className={styles.number}
                  />
                  份
                </div>
              )}
            </div>
          </Form.Item>
        )}
        <Form.Item label="奖品名称" required requiredMessage="请输入奖品名称">
          <Input
            className={styles.formInputCtrl}
            maxLength={prizeNameLength}
            showLimitHint
            placeholder="请输入奖品名称"
            name="prizeName"
            value={prizeData.prizeName}
            onChange={(prizeName) => setData({ prizeName })}
          />
        </Form.Item>
        <Form.Item label="奖品图片">
          <Grid.Row>
            <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
              <LzImageSelector
                width={width}
                height={height}
                value={prizeData.prizeImg}
                onChange={(prizeImg) => {
                  setData({ prizeImg });
                }}
              />
            </Form.Item>
            <Form.Item style={{ marginBottom: 0 }}>
              <div className={styles.tip}>
                <p>
                  图片尺寸：{width}px*{height || '任意'}px
                </p>
                <p>图片大小：不超过1M</p>
                <p>图片格式：JPG、JPEG、PNG</p>
              </div>
              <div>
                <Button
                  type="primary"
                  text
                  onClick={() => {
                    setData({ prizeImg: equityImg });
                  }}
                >
                  重置
                </Button>
              </div>
            </Form.Item>
          </Grid.Row>
        </Form.Item>
        <Form.Item>
          <Grid.Row>
            <Grid.Col style={{ textAlign: 'right' }}>
              <Form.Submit className="form-btn" validate type="primary" onClick={submit}>
                确认
              </Form.Submit>
              <Form.Reset className="form-btn" onClick={onCancel}>
                取消
              </Form.Reset>
            </Grid.Col>
          </Grid.Row>
        </Form.Item>
      </Form>
      <Dialog
        title="选择优惠券"
        footer={false}
        shouldUpdatePosition
        visible={winJdShow}
        onClose={() => setWinJdShow(false)}
      >
        <Plan onSubmit={onSubmit} shopId={currentActShopId} />
      </Dialog>
    </div>
  );
};

export default PropertyJdCoupon;
