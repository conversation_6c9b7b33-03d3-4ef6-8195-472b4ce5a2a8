import { useEffect, useState } from 'react';
import * as React from 'react';
import { Table, Pagination, Dialog, Button, Balloon } from '@alifd/next';
import styles from '../index.module.scss';
import SearchForm from './SearchForm';
import format from '@/utils/format';
import { getHongbaoPageList } from '@/api/prize';
import CreatePlan from './CreatePlan';

const defaultPage = { pageSize: 10, pageNum: 1, total: 0 };
export default ({ onSubmit }: any) => {
  const [pageInfo, setPageInfo] = useState({ ...defaultPage });
  const [searchData, setSearchData] = useState({});
  const [datalist, setList] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [planDialog, setPlanDialog] = useState(false);

  // 加载列表
  const loadPageList = (page) => {
    setLoading(true);
    getHongbaoPageList({ ...searchData, ...page })
      .then((res) => {
        setList(res.records || []);
        pageInfo.total = res.total as any;
        pageInfo.pageNum = res.current as any;
        pageInfo.pageSize = res.size as any;
        setPageInfo(pageInfo);
        setLoading(false);
      })
      .catch(() => {
        setLoading(false);
      });
  };

  // 分页事件
  const pageChangeHandler = (pageNum: number) => {
    pageInfo.pageNum = pageNum;
    loadPageList(pageInfo);
  };
  const handlePageSizeChange = (pageSize: number) => {
    pageInfo.pageSize = pageSize;
    pageInfo.pageNum = 1;
    loadPageList(pageInfo);
  };

  // 当选中行的时候
  const onRowSelectionChange = (selectKey: string[]) => {
    const selectRow = datalist.find((o: any) => selectKey.some((p: any) => p === o.planId));
    onSubmit(selectRow);
  };

  // 选择器
  const rowSelection: any = {
    mode: 'single',
    onChange: (selectKey: string[]) => onRowSelectionChange(selectKey),
    getProps: (record: { planStatus: number; planId: string }) => {
      return {
        disabled: false || record.planStatus === 3 || record.planStatus === 4,
      };
    },
  };

  // 当行点击的时候
  const onRowClick = (record: object) => {
    // 预留，如果要求点击行就选择的话，就解开这行
    // onSubmit(record);
  };
  const onSearch = (formData: any) => {
    setSearchData(formData);
    loadPageList({ ...formData, ...defaultPage });
  };
  const toRedBagList = () => {
    setPlanDialog(true);
  };
  const parsePointResListStatus = (status: number) => {
    switch (status) {
      case 1:
        return '待生效';
      case 2:
        return '已生效';
      case 3:
        return '已过期';
      default:
        return '已过期';
    }
  };
  const createPlanSubmit = () => {
    loadPageList(defaultPage);
    setPlanDialog(false);
  };
  // const toRedBagList = () => {
  //   setPlanDialog(true);
  // };
  const parsePointResListStatusColor = (status: number) => {
    switch (status) {
      case 1:
        return 'table-status-WARNING_COLOR';
      case 2:
        return 'table-status-SUCCESS_COLOR';
      default:
        return 'table-status-LIGHT_GRAY';
    }
  };
  useEffect(() => {
    loadPageList(defaultPage);
  }, []);

  return (
    <div className={styles.PropertyJdBeanPlan}>
      <SearchForm onSearch={onSearch} />
      <div className={styles.reminderBox}>
        <div />
        <Button className="table-cell-btn" style={{ marginLeft: '0' }} type="primary" text onClick={toRedBagList}>
          新建红包计划 &gt;
        </Button>
      </div>
      <Table.StickyLock
        dataSource={datalist}
        primaryKey="planId"
        fixedHeader
        maxBodyHeight={500}
        loading={loading}
        onRowClick={onRowClick}
        rowSelection={rowSelection}
      >
        <Table.Column width={180} align="left" title="红包计划名称" dataIndex="planName" />
        <Table.Column width={180} align="left" title="红包计划ID" dataIndex="planId" />
        <Table.Column
          width={180}
          align="left"
          title="单份红包金额(元)"
          dataIndex="aveAmount"
          cell={(value, index, data) => <div>{(data.aveAmount / 100).toFixed(2)}</div>}
        />
        <Table.Column
          width={160}
          align="left"
          title="创建时间"
          cell={(value, index, data) => <div>{format.formatDateTimeDayjs(new Date(data.createTime))}</div>}
        />
        <Table.Column
          width={200}
          align="left"
          title="计划时间"
          cell={(value, index, data) => (
            <div style={{ fontSize: 10 }}>
              <div>起：{format.formatDateTimeDayjs(new Date(data.startDate))}</div>
              <div>止：{format.formatDateTimeDayjs(new Date(data.endDate))}</div>
            </div>
          )}
        />
        <Table.Column
          width={200}
          align="left"
          title="有效期"
          cell={(value, index, data) => (
            <div style={{ fontSize: 10 }}>
              {data.hongBaoExpireType === 1 && (
                <div>
                  <div>起：{format.formatDateTimeDayjs(new Date(data.packetDataTime[0]))}</div>
                  <div>止：{format.formatDateTimeDayjs(new Date(data.packetDataTime[1]))}</div>
                </div>
              )}
              {data.hongBaoExpireType === 2 && (
                <div>
                  红包领取后&nbsp;
                  {data.hongBaoExpireDay}
                  &nbsp; 天内使用，逾期失效
                </div>
              )}
            </div>
          )}
        />
        <Table.Column
          width={180}
          title="领取限制"
          cell={(value, index, data) => (
            <div>
              <span>每人每天限领{data.userDayCount || '-'}次</span>
              <br />
              <span>每人计划期间限领{data.userActivityCount || '-'}次</span>
            </div>
          )}
        />
        <Table.Column
          width={180}
          align="left"
          title="计划描述"
          dataIndex="planContent"
          cell={(value, index, data) => (
            <div>
              {data.planContent ? (
                <Balloon
                  v2
                  align="t"
                  trigger={
                    <div className={styles.prize_name_column} style={{ cursor: 'pointer' }}>
                      <span className="prize_name_column_title">{data.planContent}</span>
                    </div>
                  }
                  closable={false}
                >
                  <span>{data.planContent}</span>
                </Balloon>
              ) : (
                '-'
              )}
            </div>
          )}
        />
        <Table.Column width={100} align="left" title="剩余数量" dataIndex="quantityRemain" />
        <Table.Column
          width={100}
          align="left"
          title="时效状态"
          cell={(value, index, data) => (
            <span className={styles[parsePointResListStatusColor(data.planStatus)]}>
              {parsePointResListStatus(data.planStatus)}
            </span>
          )}
        />
      </Table.StickyLock>
      <Pagination
        shape="arrow-only"
        pageSizeSelector="dropdown"
        pageSizePosition="end"
        total={pageInfo.total}
        current={pageInfo.pageNum}
        pageSize={pageInfo.pageSize}
        pageSizeList={[5, 10, 30, 50, 100]}
        totalRender={(total) => `共${total}条`}
        onPageSizeChange={handlePageSizeChange}
        onChange={pageChangeHandler}
        className={styles.pagination}
      />
      <Dialog
        title="创建红包计划"
        footer={false}
        shouldUpdatePosition
        visible={planDialog}
        onClose={() => setPlanDialog(false)}
      >
        <CreatePlan handleCancel={() => setPlanDialog(false)} handleSubmit={createPlanSubmit} />
      </Dialog>
    </div>
  );
};
