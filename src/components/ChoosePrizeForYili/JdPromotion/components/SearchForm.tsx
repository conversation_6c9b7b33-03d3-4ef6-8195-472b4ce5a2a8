import { useState } from 'react';
import * as React from 'react';
import { Button, Form, Input, Select } from '@alifd/next';

const initBeanSearchData = {
  name: '',
  tokenId: '',
  planStatus: '',
};

const STATUS = [
  { label: '全部', value: '' },
  { label: '待生效', value: 1 },
  { label: '已生效', value: 2 },
  // { label: '已过期', value: 3 },
];

export default ({ onSearch }: any) => {
  const [beanSearchData, setBeanSearchData] = useState(initBeanSearchData);

  /**
   * 京豆筛选框赋值
   * @param {*} value 筛选值
   */
  const beanSelectData = (value: any) => {
    setBeanSearchData({
      ...beanSearchData,
      ...value,
    });
  };

  const onSearchClick = () => {
    onSearch(beanSearchData);
  };

  const onResetClick = () => {
    setBeanSearchData(initBeanSearchData);
    onSearch(initBeanSearchData);
  };

  return (
    <div>
      <Form inline>
        <Form.Item className="item" label="令牌名称：">
          <Input
            value={beanSearchData.name}
            className="dialog-search-ctrl"
            placeholder="请输入令牌名称"
            onChange={(name) => beanSelectData({ name })}
          />
        </Form.Item>
        <Form.Item className="item" label="令牌ID：">
          <Input
            value={beanSearchData.tokenId}
            className="dialog-search-ctrl"
            placeholder="请输入令牌ID"
            onChange={(tokenId) => {
              // 令牌ID只保留数字英文
              tokenId = tokenId.replace(/[^\w\d]/g, '');
              beanSelectData({ tokenId });
            }}
          />
        </Form.Item>
        <Form.Item name="planStatus" label="时效状态">
          <Select
            followTrigger
            mode="single"
            showSearch
            dataSource={STATUS}
            value={beanSearchData.planStatus}
            defaultValue=""
            onChange={(planStatus) => beanSelectData({ planStatus })}
          />
        </Form.Item>
        <Form.Item className="item" style={{ textAlign: 'right' }}>
          <Button type="primary" onClick={onSearchClick}>
            查询
          </Button>
          <Button style={{ marginLeft: 10 }} type="normal" onClick={onResetClick}>
            重置
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
};
