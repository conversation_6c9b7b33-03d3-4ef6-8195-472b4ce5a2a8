import { useEffect, useState } from 'react';
import * as React from 'react';
import { Table, Pagination, Dialog, Button } from '@alifd/next';
import styles from '../index.module.scss';
import SearchForm from './SearchForm';
import format from '@/utils/format';
import { getIQiYiPageList } from '@/api/prize';
import { IPagePrizeIQiYiPageResponse, PrizeIQiYiPageResponse } from '@/api/types';
import CreatePlan from './CreatePlan';

interface PlanProps {
  onSubmit: (data: any) => void;
}

interface PageInfo {
  pageSize: number;
  pageNum: number;
  total: number;
}
interface FormData {
  planName: string;
  planId: string;
}

const defaultPage: PageInfo = { pageSize: 10, pageNum: 1, total: 0 };
export default ({ onSubmit }: PlanProps) => {
  const [pageInfo, setPageInfo] = useState<PageInfo>({ ...defaultPage });
  const [searchData, setSearchData] = useState({});
  const [datalist, setList] = useState<PrizeIQiYiPageResponse[]>([]);
  const [loading, setLoading] = useState(false);
  const [planDialog, setPlanDialog] = useState(false);

  // 加载列表
  const loadPageList = (page) => {
    setLoading(true);
    getIQiYiPageList({ ...searchData, ...page })
      .then((res: IPagePrizeIQiYiPageResponse) => {
        setList(res.records || []);
        pageInfo.pageNum = res.current as any;
        pageInfo.pageSize = res.size as any;
        pageInfo.total = +res.total!;
        setPageInfo(pageInfo);
        setLoading(false);
      })
      .catch(() => {
        setLoading(false);
      });
  };

  // 分页事件
  const pageChangeHandler = (pageNum: number) => {
    pageInfo.pageNum = pageNum;
    loadPageList(pageInfo);
  };
  const handlePageSizeChange = (pageSize: number) => {
    pageInfo.pageSize = pageSize;
    pageInfo.pageNum = 1;
    loadPageList(pageInfo);
  };

  // 当选中行的时候
  const onRowSelectionChange = (selectKey: string[]): void => {
    const selectRow = datalist.find((o: any) => selectKey.some((p: any) => p === o.planId));
    onSubmit(selectRow);
  };

  // 选择器
  const rowSelection: {
    mode: 'single' | 'multiple' | undefined;
    onChange: (selectKey: string[]) => void;
    getProps: (record: PrizeIQiYiPageResponse) => { disabled: boolean };
  } = {
    mode: 'single',
    onChange: (selectKey: string[]) => onRowSelectionChange(selectKey),
    getProps: (record: { planId: string }) => {
      return {
        disabled: false,
      };
    },
  };

  // 当行点击的时候
  const onRowClick = (record: PrizeIQiYiPageResponse): void => {
    // 预留，如果要求点击行就选择的话，就解开这行
    // onSubmit(record);
  };
  const onSearch = (formData: FormData): void => {
    setSearchData(formData);
    loadPageList({ ...formData, ...defaultPage });
  };
  const parsePointResListStatus = (status: number): string => {
    switch (status) {
      case 1:
        return '待生效';
      case 2:
        return '已生效';
      case 3:
        return '已过期';
      default:
        return '已过期';
    }
  };
  const parsePointResListStatusColor = (status: number): string => {
    switch (status) {
      case 1:
        return 'table-status-WARNING_COLOR';
      case 2:
        return 'table-status-SUCCESS_COLOR';
      default:
        return 'table-status-LIGHT_GRAY';
    }
  };

  const valueType = {
    8: '月卡',
    12: '季卡',
    13: '半年卡',
    14: '年卡',
  };
  const createPlanSubmit = () => {
    loadPageList(defaultPage);
    setPlanDialog(false);
  };
  const toECardList = () => {
    setPlanDialog(true);
  };
  useEffect(() => {
    loadPageList(defaultPage);
  }, []);

  return (
    <div className={styles.PropertyJdBeanPlan}>
      <SearchForm onSearch={onSearch} />
      <div className={styles.reminderBox}>
        <div />
        <Button className="table-cell-btn" style={{ marginLeft: '0' }} type="primary" text onClick={toECardList}>
          新建爱奇艺会员 &gt;
        </Button>
      </div>
      <Table.StickyLock
        dataSource={datalist}
        primaryKey="planId"
        fixedHeader
        maxBodyHeight={500}
        loading={loading}
        onRowClick={onRowClick}
        rowSelection={rowSelection}
      >
        <Table.Column width={120} align="left" title="爱奇艺会员计划名称" dataIndex="planName" />
        <Table.Column align="left" title="爱奇艺会员计划ID" dataIndex="planId" />
        <Table.Column
          align="left"
          title="价值(元)"
          cell={(value, index, data) => (
            <div>
              {data.amount / 100} ( {valueType[data.itemId]})
            </div>
          )}
        />
        <Table.Column
          width={160}
          align="left"
          title="创建时间"
          cell={(value, index, data) => <div>{format.formatDateTimeDayjs(new Date(data.createTime))}</div>}
        />
        <Table.Column
          width={170}
          align="left"
          title="有效期"
          cell={(value, index, data) => (
            <div>
              <div>起：{format.formatDateTimeDayjs(new Date(data.startDate))}</div>
              <div>止：{format.formatDateTimeDayjs(new Date(data.endDate))}</div>
            </div>
          )}
        />
        <Table.Column align="left" title="剩余数量" dataIndex="quantityRemain" />
        <Table.Column
          align="left"
          title="时效状态"
          cell={(value, index, data) => (
            <span className={styles[parsePointResListStatusColor(data.planStatus)]}>
              {parsePointResListStatus(data.planStatus)}
            </span>
          )}
        />
      </Table.StickyLock>
      <Pagination
        shape="arrow-only"
        pageSizeSelector="dropdown"
        pageSizePosition="end"
        total={pageInfo.total}
        current={pageInfo.pageNum}
        pageSize={pageInfo.pageSize}
        pageSizeList={[5, 10, 30, 50, 100]}
        totalRender={(total) => `共${total}条`}
        onPageSizeChange={handlePageSizeChange}
        onChange={pageChangeHandler}
        className={styles.pagination}
      />
      <Dialog
        title="新建爱奇艺会员计划"
        footer={false}
        shouldUpdatePosition
        visible={planDialog}
        onClose={() => setPlanDialog(false)}
      >
        <CreatePlan handleCancel={() => setPlanDialog(false)} handleSubmit={createPlanSubmit} />
      </Dialog>
    </div>
  );
};
