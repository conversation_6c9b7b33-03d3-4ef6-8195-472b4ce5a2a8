import { useEffect, useState } from 'react';
import * as React from 'react';
import { Table, Pagination, Dialog, Button } from '@alifd/next';
import styles from '../index.module.scss';
import SearchForm from './SearchForm';
import format from '@/utils/format';
import { getPhysicalCommodityPageList } from '@/api/prize';
import CreatePlan from './CreatePlan';

const defaultPage = { pageSize: 10, pageNum: 1, total: 0 };
export default ({ onSubmit }: any) => {
  const [pageInfo, setPageInfo] = useState({ ...defaultPage });
  const [searchData, setSearchData] = useState({});
  const [datalist, setList] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [planDialog, setPlanDialog] = useState(false);

  // 加载列表
  const loadPageList = (page) => {
    setLoading(true);
    getPhysicalCommodityPageList({ ...searchData, ...page })
      .then((res) => {
        setList(res.records || []);
        pageInfo.total = res.total as any;
        pageInfo.pageNum = res.current as any;
        pageInfo.pageSize = res.size as any;
        setPageInfo(pageInfo);
        setLoading(false);
      })
      .catch(() => {
        setLoading(false);
      });
  };

  // 分页事件
  const pageChangeHandler = (pageNum: number) => {
    pageInfo.pageNum = pageNum;
    loadPageList(pageInfo);
  };
  const handlePageSizeChange = (pageSize: number) => {
    pageInfo.pageSize = pageSize;
    pageInfo.pageNum = 1;
    loadPageList(pageInfo);
  };

  // 当选中行的时候
  const onRowSelectionChange = (selectKey: string[]) => {
    const selectRow = datalist.find((o: any) => selectKey.some((p: any) => p === o.skuCode));
    onSubmit(selectRow);
  };

  // 选择器
  const rowSelection: any = {
    mode: 'single',
    onChange: (selectKey: string[]) => onRowSelectionChange(selectKey),
    getProps: (record: { skuCode: string; quantityAvailable: number }) => {
      return {
        disabled: false || record.quantityAvailable <= 0,
      };
    },
  };

  // 当行点击的时候
  const onRowClick = (record: object) => {
    // 预留，如果要求点击行就选择的话，就解开这行
    // onSubmit(record);
  };
  const onSearch = (formData: any) => {
    setSearchData(formData);
    loadPageList({ ...formData, ...defaultPage });
  };
  const createPlanSubmit = () => {
    loadPageList(defaultPage);
    setPlanDialog(false);
  };
  const toProductList = () => {
    setPlanDialog(true);
  };

  useEffect(() => {
    loadPageList(defaultPage);
  }, []);

  return (
    <div className={styles.PropertyJdBeanPlan}>
      <SearchForm onSearch={onSearch} />
      <div className={styles.reminderBox}>
        <div />
        <Button className="table-cell-btn" style={{ marginLeft: '0' }} type="primary" text onClick={toProductList}>
          新建实物 &gt;
        </Button>
      </div>
      <Table.StickyLock
        dataSource={datalist}
        primaryKey="skuCode"
        fixedHeader
        maxBodyHeight={500}
        loading={loading}
        onRowClick={onRowClick}
        rowSelection={rowSelection}
      >
        <Table.Column align="left" title="商品名称" dataIndex="skuName" />
        <Table.Column
          align="left"
          title="商品图片"
          cell={(value, index, data) => {
            return (
              <>{data.skuMainPicture ? <img style={{ width: '80px' }} src={data.skuMainPicture} alt=" " /> : '-'}</>
            );
          }}
        />
        <Table.Column align="left" title="商品编号" dataIndex="skuCode" />
        <Table.Column align="left" title="商家内部编号" dataIndex="wmsCode" />
        <Table.Column
          align="left"
          title="创建时间"
          cell={(value, index, data) => <div>{format.formatDateTimeDayjs(new Date(data.createTime))}</div>}
        />
        <Table.Column align="left" title="可用数量" dataIndex="quantityAvailable" />
      </Table.StickyLock>
      <Pagination
        shape="arrow-only"
        pageSizeSelector="dropdown"
        pageSizePosition="end"
        total={pageInfo.total}
        current={pageInfo.pageNum}
        pageSize={pageInfo.pageSize}
        pageSizeList={[5, 10, 30, 50, 100]}
        totalRender={(total) => `共${total}条`}
        onPageSizeChange={handlePageSizeChange}
        onChange={pageChangeHandler}
        className={styles.pagination}
      />
      <Dialog
        title="新建实物"
        footer={false}
        shouldUpdatePosition
        visible={planDialog}
        onClose={() => setPlanDialog(false)}
      >
        <CreatePlan handleCancel={() => setPlanDialog(false)} handleSubmit={createPlanSubmit} />
      </Dialog>
    </div>
  );
};
