import React, { useEffect, useState } from 'react';
import { But<PERSON>, Loading, Tab } from '@alifd/next';
import styles from './index.module.scss';
import { getActivityInfoAllOrigin, getActivityPageList } from '@/api/common';
import { getShop } from '@/utils/shopUtil';
import { config } from 'ice';
import LzPanel from '../LzPanel';
import BasicComponent from './components/BasicComponent';
import FreePublicDomainPush from './components/FreePublicDomainPush';
import { getActivityParams } from '@/utils';

/**
 * @param activityId 活动id
 * @param activityUrl 活动链接（长链）
 */
function ChannelPromotion(props) {
  const { activityId } = props;
  const [activityType, code] = getActivityParams();
  const { shopId } = getShop();
  const [activeKey, setActiveKey] = useState('1');
  const [activityBaseInfo, setActivityBaseInfo] = useState({});
  const [pageLoading, setPageLoading] = useState(false);
  const activityUrl = `${config.previewUrl}/${activityType}/${code}/?shopId=${shopId}&activityId=${activityId}`;
  const jumpUrl = [
    {
      label: '短信群发',
      url: '../../../market/private/smsManage',
    },
    {
      label: '发富媒体短信',
      url: '../../../market/private/task/rich/message',
    },
    {
      label: '优惠促销',
      url: '../../../market/private/promotion',
    },
    {
      label: '咚咚群聊',
      url: '../../../market/private/dedicated/intelligent/group/chat',
    },
    {
      label: '咚咚客服消息',
      url: '../../../market/private/dedicated/waiter/message/second',
    },
    {
      label: '卡片短信',
      url: '../../../market/private/task/place',
    },
  ];
  const tabData = [
    {
      key: '2',
      title: '店铺下拉二楼',
      des: (
        <>
          <div>店铺阁楼位于店铺首页下拉空间，是平台官方提供给品牌的店铺首页下拉位置的活动推广入口</div>
          <div>推广优势：具备丰富延展性的空间，为商家提供了一个开放性入口，进行玩法赋能。</div>
        </>
      ),
      stepTwoTitle: '去配置店铺阁楼',
      setUrl: 'https://ware.shop.jd.com/rest/shop/loft/manage',
      operateUrl: 'https://joyspace.jd.com/pages/eJKg5cBXamx6XMC1444D',
      customModule: (
        <div className={styles.customModule}>
          更多审核要求、操作手册，请查看：
          <Button
            type="primary"
            text
            onClick={() => {
              window.open(
                'https://mtt.jd.com/article/articleView/0ea4bd6a-ad1e-43c0-933f-56b6aea4b15c.action',
                '_blank',
              );
            }}
          >
            京东APP端店铺阁楼内容审核&配置规范更新通知
          </Button>
        </div>
      ),
      imgUrl: '//img10.360buyimg.com/imgzone/jfs/t1/292854/30/534/53315/680df007Fbb895164/bc17233e774befd1.png',
      imgWidth: '30%',
      imgHeight: '',
    },
    {
      key: '3',
      title: '商详页推广',
      des: <div>商详页推广可提升商详信息展示效率与商详整体转化效率</div>,
      stepTwoTitle: '详情装修',
      setUrl: 'https://jshopx.jd.com/zhuangba/associateManage?projectType=6',
      operateUrl: 'https://joyspace.jd.com/pages/beJIw7t2YO9AiHWdORVB',
      customModule: '',
      imgUrl: '//img10.360buyimg.com/imgzone/jfs/t1/280186/1/26637/113950/6809efe8F36dc8798/98493689f2722195.png',
      imgWidth: '50%',
      imgHeight: '270px',
    },
    {
      key: '4',
      title: '店铺悬浮图标',
      des: <div>活动推广悬浮图标之后，活动将在店铺首页悬浮图标中展示</div>,
      stepTwoTitle: '详情装修',
      setUrl: '../../../interaction/floatButton',
      operateUrl: 'https://joyspace.jd.com/pages/gcNYVW8jOOpBivQrEmrx',
      customModule: '',
      imgUrl: '//img10.360buyimg.com/imgzone/jfs/t1/291683/38/639/42276/680df008Fc796f740/5fa9a61b32293030.png',
      imgWidth: '50%',
      imgHeight: '270px',
    },
    {
      key: '5',
      title: '营销触达',
      des: (
        <div>
          活动创建后，可通过短信、富媒体短信、优惠促销、咚咚群聊、咚咚客服消息、卡片短信的形式，多渠道组合触达消费者
        </div>
      ),
      stepTwoTitle: '创建营销任务',
      setUrl: '',
      operateUrl: '',
      customModule: (
        <div>
          {jumpUrl.map((item, index) => {
            return (
              <Button
                style={{ marginRight: '10px' }}
                key={index}
                onClick={() => {
                  window.open(item.url, '_blank');
                }}
              >
                {item.label}
              </Button>
            );
          })}
        </div>
      ),
      imgUrl: '//img10.360buyimg.com/imgzone/jfs/t1/278828/10/26597/98495/6809efe6F84ead1b0/1cdbbf950d6e2f65.png',
      imgWidth: '30%',
      imgHeight: '',
    },
    {
      key: '6',
      title: '会员中心',
      des: <div>可在会员中心自定义配置活动入口，进行活动曝光和会员权益展示，打造专属会员阵地</div>,
      stepTwoTitle: '去配置会员中心页',
      setUrl: '../../../interact2/decorateNew',
      operateUrl: 'https://joyspace.jd.com/pages/sZC6KVkngI8ADMpvc1rp',
      customModule: '',
      imgUrl: '//img10.360buyimg.com/imgzone/jfs/t1/274427/2/28581/43206/680defbaFa831aaa0/f9e8c40ec70460a2.png',
      imgWidth: '50%',
      imgHeight: '270px',
    },
  ];

  const loadData = () => {
    try {
      setPageLoading(true);
      let newActivityBaseInfo = {};
      getActivityPageList({
        pageNum: 1,
        pageSize: 20,
        status: 0,
        activityName: activityId,
        activityType: [],
        dateRange: [],
      })
        .then((result) => {
          newActivityBaseInfo =
            result?.records && result?.records?.length > 0 ? { ...newActivityBaseInfo, ...result.records[0] } : {};
          setActivityBaseInfo(newActivityBaseInfo);
          setPageLoading(false);
        })
        .catch((err) => {
          console.log(err);
          setPageLoading(false);
        });
      getActivityInfoAllOrigin({ id: activityId, type: 'view' })
        .then((res) => {
          const activityData = JSON.parse(res.activityData!);
          newActivityBaseInfo = { ...newActivityBaseInfo, ...activityData };
          setActivityBaseInfo(newActivityBaseInfo);
          setPageLoading(false);
        })
        .catch((e) => {
          console.log(e);
          setPageLoading(false);
        });
    } catch (e) {
      console.log(e);
      setPageLoading(false);
    }
  };
  useEffect(() => {
    if (activityId) {
      loadData();
    }
  }, []);
  return (
    <div className={styles.channelPromotion}>
      <LzPanel title="立即推广，引爆流量增长">
        <Loading visible={pageLoading} style={{ width: '100%' }}>
          <div>
            <Tab activeKey={activeKey} onChange={(key) => setActiveKey(key)} shape="wrapped">
              <Tab.Item title="免费公域投放" key="1">
                <FreePublicDomainPush activityId={activityId} activityBaseInfo={activityBaseInfo} />
              </Tab.Item>
              {tabData.map((item, index) => {
                return (
                  <Tab.Item title={item.title} key={`${item.key}-${index}`}>
                    <BasicComponent
                      currentType={item.key}
                      des={item.des}
                      activityUrl={activityUrl}
                      stepTwoTitle={item.stepTwoTitle}
                      setUrl={item.setUrl}
                      operateUrl={item.operateUrl}
                      customModule={item.customModule}
                      imgUrl={item.imgUrl}
                      imgWidth={item.imgWidth}
                      imgHeight={item.imgHeight}
                      activityBaseInfo={activityBaseInfo}
                    />
                  </Tab.Item>
                );
              })}
            </Tab>
          </div>
        </Loading>
      </LzPanel>
    </div>
  );
}

export default ChannelPromotion;
