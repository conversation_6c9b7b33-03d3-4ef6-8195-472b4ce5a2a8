import { Button, Input, Message } from '@alifd/next';
import React from 'react';
import styles from '../index.module.scss';
import Utils from '@/utils';

/**
 * @param currentType 当前tab类型
 * @param des 描述
 * @param activityUrl 活动链接（长链）
 * @param stepTwoTitle 第二步的标题
 * @param setUrl 配置链接
 * @param operateUrl 操作手册链接
 * @param customModule 自定义模块
 * @param imgUrl 右侧示例图链接
 * @param imgWidth 右侧示例图宽度
 * @param imgHeight 右侧示例图高度
 * @param activityBaseInfo 活动基础信息
 */
function BasicComponent(props) {
  const {
    currentType,
    des,
    activityUrl,
    stepTwoTitle,
    setUrl,
    operateUrl,
    customModule,
    imgUrl,
    imgWidth,
    imgHeight,
    activityBaseInfo,
  } = props;
  return (
    <div className={styles.basicComponent}>
      <div className={styles.leftBox}>
        <div className={styles.des}>{des}</div>
        <div>
          <div className={styles.step}>第一步：复制活动链接</div>
          {currentType === '5' && (
            <div style={{ marginBottom: '10px' }}>
              <Input value={activityBaseInfo?.shortUrl} readOnly style={{ marginRight: '10px' }} />
              <Button
                type="primary"
                onClick={async () => {
                  await Utils.copyText(activityBaseInfo?.shortUrl);
                  Message.success('链接已经复制到剪切板');
                }}
              >
                复制短链接
              </Button>
            </div>
          )}
          <div>
            <Input value={activityUrl} readOnly style={{ marginRight: '10px' }} />
            <Button
              type="primary"
              onClick={async () => {
                await Utils.copyText(activityUrl);
                Message.success('链接已经复制到剪切板');
              }}
            >
              {currentType === '5' ? '复制长链接' : '复制活动链接'}
            </Button>
          </div>
        </div>
        <div>
          <div className={styles.step}>第二步：{stepTwoTitle}</div>
          {setUrl && operateUrl && (
            <div>
              <Button
                type="primary"
                style={{ marginRight: '10px' }}
                onClick={() => {
                  window.open(setUrl, '_blank');
                }}
              >
                去配置
              </Button>
              <Button
                type="primary"
                text
                onClick={() => {
                  window.open(operateUrl, '_blank');
                }}
              >
                操作手册
              </Button>
            </div>
          )}
          {customModule || ''}
        </div>
      </div>
      <div
        className={styles.rightBox}
        style={{ backgroundImage: `url(${imgUrl})`, width: imgWidth, height: imgHeight }}
      />
    </div>
  );
}

export default BasicComponent;
