import { Button, Checkbox, Message, Switch } from '@alifd/next';
import React, { useState } from 'react';
import styles from '../index.module.scss';
import LzDialog from '@/components/LzDialog';
import { updateOpenStatus } from '@/api/common';

/**
 * @param activityId 活动id
 * @param activityBaseInfo 活动基础信息
 */
function FreePublicDomainPush(props) {
  const { activityId, activityBaseInfo } = props;
  const [visible, setVisible] = useState(false);
  const [promotionTerms, setPromotionTerms] = useState(false);
  const [openBtn, setOpenBtn] = useState(false);
  const [agreeBtn, setAgreeBtn] = useState(true);

  const handleCheckedChange = async (checked) => {
    try {
      await updateOpenStatus({
        activityId,
        openStatus: checked ? 1 : 2,
      });
      setOpenBtn(checked);
      Message.success('操作成功');
    } catch (e) {
      Message.error(e.message);
    }
  };

  return (
    <div className={styles.freePublicDomainPush}>
      <div className={styles.leftBox} />
      <div>
        <div className={styles.flexBox1}>
          是否开启公域引流:
          <Switch
            checked={openBtn}
            size={'small'}
            autoWidth
            checkedChildren={'开'}
            unCheckedChildren={'关'}
            onChange={(v) => {
              if (agreeBtn) {
                handleCheckedChange(v);
              } else {
                Message.error('请确认推广须知');
              }
            }}
          />
          <Button
            type="primary"
            text
            onClick={() => {
              window.open('../../interactCity', '_blank');
            }}
          >
            投放活动管理&gt;
          </Button>
          <Button
            type="primary"
            text
            onClick={() => {
              setVisible(true);
            }}
          >
            <span className="iconfont icon-icon4-37" />
            了解免费公域流量池-超级互动城&gt;
          </Button>
        </div>
        <div className={styles.flexBox1} style={{ fontSize: '20px', fontWeight: 600 }}>
          0成本公域投放,与大牌共享百万流量曝光设置
        </div>
        <div className={styles.flexBox1}>
          活动标题:<span>{activityBaseInfo?.activityName || '-'}</span>
        </div>
        <div className={styles.flexBox1}>
          活动介绍:<span>{activityBaseInfo?.shareTitle || '-'}</span>
        </div>
        <div className={styles.flexBox2}>
          活动图片:
          <img src={activityBaseInfo?.h5Img} width={150} alt="暂无图片" />
        </div>
        <div className={styles.flexBox}>
          <Checkbox
            checked={agreeBtn}
            onChange={(checked) => {
              setAgreeBtn(checked);
              setOpenBtn(false);
            }}
          >
            确认即同意
          </Checkbox>
          <Button
            type="primary"
            text
            style={{ marginTop: '2px' }}
            onClick={() => {
              setPromotionTerms(true);
            }}
          >
            推广须知
          </Button>
        </div>
      </div>
      {visible && (
        <LzDialog
          title={false}
          visible={visible}
          footer={false}
          onClose={() => setVisible(false)}
          style={{ width: '670px' }}
        >
          <video
            controls
            autoPlay
            src="https://jvod.300hu.com/vod/product/058f373a-665b-4034-9512-d67ac8b967c2/92f499d7407c47a89ed5f76d3a8eada2.mp4"
          />
        </LzDialog>
      )}
      {promotionTerms && (
        <LzDialog title={false} visible={promotionTerms} footer={false} onClose={() => setPromotionTerms(false)}>
          <div className={styles.promotionTips} />
        </LzDialog>
      )}
    </div>
  );
}

export default FreePublicDomainPush;
