@import "@alifd/next/variables";

.channelPromotion {
  margin-top: 15px;

  .customModule {
    margin-top: 20px;
    display: flex;
    align-items: center;
  }
}

.basicComponent {
  display: flex;
  padding: 15px 10px;
  flex: 2 1;

  .leftBox {
    margin-right: 20px;
    min-width: 480px;
    display: grid;
    grid-template-rows: 0.5fr 0.8fr 0.8fr;
  }

  .rightBox {
    max-width: 500px;
    background-size: contain;
    background-repeat: no-repeat;
  }

  .des {
    font-size: 14px;
    line-height: 22px;
    color: #333333;
  }

  .step {
    margin: 15px 0;
    font-size: 14px;
  }
}

.freePublicDomainPush {
  display: flex;
  padding: 15px 10px;
  font-size: 14px;

  .leftBox {
    background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/127256/20/20770/310370/616e3c15Ee2de3f5a/cfbf6a36887bc4c8.png');
    width: 268px;
    height: 360px;
    background-repeat: no-repeat;
    background-size: contain;
  }

  .flexBox,
  .flexBox1 {
    margin-bottom: 20px;
    display: flex;
    align-items: center;
  }

  .flexBox1 {
    gap: 20px;
  }

  .flexBox2 {
    margin-bottom: 20px;
    display: flex;
    gap: 20px;
  }

}

.promotionTips {
  background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/157879/27/16944/31248/606d7d10E457d7c94/baaa7e86a6e3172b.png');
  background-size: contain;
  background-repeat: no-repeat;
  width: 600px;
  height: 440px;
}

.channelPromotion :global {
  .next-tabs-wrapped.next-tabs-top>.next-tabs-bar .next-tabs-tab {
    margin-right: 5px;
    color: #2F77FF;
    background-color: rgba(255, 255, 255, 1);
    border-color: rgba(47, 119, 255, 1);
    border-radius: 10px 10px 0 0;
  }

  .next-tabs-wrapped.next-tabs-top>.next-tabs-bar .next-tabs-tab.active {
    color: #FFFFFF;
    background-color: rgba(47, 119, 255, 1);
    border-color: #dcdfe6 #dcdfe6 #fff;
    border-radius: 10px 10px 0 0;
  }

  .next-tabs-wrapped.next-tabs-top>.next-tabs-bar .next-tabs-tab.active:before {
    width: calc(100% - 17px);
    left: 8px;
  }

  .next-tabs-wrapped.next-tabs-top>.next-tabs-bar:before {
    width: 0%;
  }

}