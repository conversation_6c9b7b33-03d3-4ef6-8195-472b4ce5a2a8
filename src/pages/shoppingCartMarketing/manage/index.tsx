import React, { useEffect, useState } from 'react';
import {
  Button,
  DatePicker2,
  Dialog,
  Field,
  Form,
  Input,
  Loading,
  Message,
  Select,
  Table,
  Drawer,
  Tab,
} from '@alifd/next';
import styles from './index.module.scss';
import LzPanel from '@/components/LzPanel';
import { deepCopy, downloadExcel } from '@/utils';
import format from '@/utils/format';
import LzPagination from '@/components/LzPagination';
import ActivityPreview from './components/ActivityPreview';
import {
  deletedActivityInfo,
  endActivityInfo,
  exportActivityInfoExport,
  getActivityInfo,
  getSkuInfo,
  manualAward,
} from '@/api/getCartUserPackage';
import constant from '@/utils/constant';
// import { useStateWithStorage } from '@/hooks/useStateWithStorage';
import LzDialog from '@/components/LzDialog';
import dayjs from 'dayjs';
import { appHistory } from '@ice/stark-app';
import { RouteComponentProps } from 'react-router-dom';
// import { initData } from '@/models/pager';
// import store from '@/store';

const { RangePicker } = DatePicker2;

const defaultPage = {
  pageNum: 1,
  pageSize: 10,
  total: 0,
};
export default (props: RouteComponentProps) => {
  const { history } = props;
  const field = Field.useField();
  const [loading, setLoading] = useState(false);
  const [loadingDialog, setLoadingDialog] = useState(false);

  const [pageInfo, setPage] = useState(deepCopy(defaultPage));
  const [tableData, setTableData] = useState<any[]>([]);
  // const [activeKey, setActiveKey] = useStateWithStorage<string>('2');
  // 是否展示详情抽屉
  const [drawerVisible, setDrawerVisible] = useState(false);
  // 详情抽屉tabIndex
  const [drawerTabIndex, setDrawerTabIndex] = useState('1');
  // 获取v2活动的原始数据
  const [activityInfo, setActivityInfo] = useState<any>({});

  // 加购商品
  const [skuListData, setSkuListData] = useState<any[]>([]);
  const [taskId, setTaskId] = useState(null);
  const [showGoodDialog, setShowGoodDialog] = useState(false);
  const [skuListPageInfo, setSkuListPageInfo] = useState(deepCopy(defaultPage));
  const defaultRangeVal = [];
  const loadData = (query: any) => {
    // query.actType = query.actType ? query.actType : activeKey;
    console.log(query, '请求数据');
    setLoading(true);
    getActivityInfo(query)
      .then((res: any): void => {
        setTableData(res.records as any[]);
        pageInfo.total = +res.total!;
        pageInfo.pageSize = +res.size!;
        pageInfo.pageNum = +res.current!;
        setPage(pageInfo);
        setLoading(false);
      })
      .catch((e) => {
        setLoading(false);
      });
  };
  useEffect(() => {
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  }, []);
  const handleSubmit = (e) => {
    e.preventDefault();
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  };
  // 分页
  const handlePage = ({ pageSize, pageNum }) => {
    // setPage({
    //   ...pageInfo,
    //   pageSize,
    //   pageNum,
    // });
    const formValue: any = field.getValues();
    console.log(formValue, '分页formValue');
    loadData({ ...formValue, pageSize, pageNum });
  };

  const IS_WIN = [
    { label: '全部', value: null },
    { label: '未开始', value: 1 },
    { label: '进行中', value: 2 },
    { label: '已结束', value: 3 },
  ];
  // 列表tab改变
  // const handleTabChange = (tabIndex): boolean | void => {
  //   setActiveKey(tabIndex);
  //   const formValue: any = field.getValues();
  //   loadData({ ...formValue, ...defaultPage, actType: tabIndex });
  // };
  // 根据版本使用不同的router
  const autoPush = (link: string) => {
    const router = link.startsWith('/custom') ? appHistory : history;
    router.push(link);
  };
  // 按钮事件列表
  const events = {
    // 编辑活动按钮点击事件
    onEditButtonClick: (value, index, record) => {
      const link = `/shoppingCartMarketing?id=${record.taskId}${record.actType}&status=${record.actType}&type=edit`; // record.buttons.filter((e) => e.key === 'edit')[0];
      // const { link } = record.buttons.filter((e) => e.key === 'edit')[0];
      autoPush(link);
    },
  };
  // 查看任务
  const queryTask = (index, itemData) => {
    console.log(index, itemData, '删除任务');
    setActivityInfo({
      shopId: localStorage.getItem('LZ_CURRENT_SHOP')
        ? JSON.parse(localStorage.getItem('LZ_CURRENT_SHOP') as string).shopId
        : '734259',
      // 活动名称
      taskName: `测试购物车营销-${dayjs().format('YYYY-MM-DD')}`,
      // 日期区间（不提交）
      rangeDate: [
        (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
        (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
      ],
      // 活动开始时间
      startTime: (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
      // 活动结束时间
      endTime: (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
      day: 1,
      orderThreshold: 0, // 订单门槛 0 新用户 1回流用户 2 品牌一年新
      // 限制订单时间
      orderRestrainRangeData: [
        (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
        (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
      ],
      // 限制订单开始时间
      orderStartTime: (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
      // 限制订单结束时间
      orderEndTime: (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
      // 活动门槛（不提交）
      threshold: 1,
      // 支持的会员等级(-1:无门槛；1,2,3,4,5,-12以逗号做分割(-12为对应等级及以上))
      memberLevels: '1,2,3,4,5',
      memberType: 1, // 0 未选择 1选择 是否选择对应等级及以上
      orderSkuisExposure: 1,
      skuList: [
        {
          jdPrice: '239.00',
          seq: 0,
          skuId: 10097246839573,
          skuMainPicture:
            'http://img10.360buyimg.com/n0/jfs/t1/207637/6/48883/83020/6732f47eF34ff0bdc/e52ac6323d267311.jpg',
          skuName: '广博联名酷企鹅A6手帐本礼盒套装手账本送礼笔记本子礼盒文具生日礼物 A6手帐套装-酷企鹅',
        },
      ],
      // 奖品列表
      prizeList: [
        {
          // 奖品名称
          prizeName: '预览测试',
          // 奖品图
          prizeImg: '',
          // 奖品类型
          prizeType: 1,
          // 中奖概率
          probability: 0,
          // 单位数量
          unitCount: 2,
          // 单位价值
          unitPrice: 2,
          // 发放份数
          sendTotalCount: 10,
          // 每日发放限额
          dayLimit: 0,
          // 每日发放限额类型 1 不限制 2限制
          dayLimitType: 1,
          ifPlan: 0,
        },
      ],
      // 限制入会时间 0：不限制；1：入会时间
      joinTimeLimit: 0,

      templateCode: '',
      gradeLabel: ['一星会员', '二星会员', '三星会员', '四星会员', '五星会员'],
      crowdBag: null,
      channel: 1, // 0 短信不投放 1短信投放
      content: '短信投放短信投放短信投放短信投放', // 短信内容
      sendDongDong: 1, // 是否发送咚咚消息 0 不发送 1发送
      isPushMainImage: 1, // 是否推送咚咚消息主图 0 不推送 1推送
      pushMainImage: '',
      isMessageContent: 1, // 是否推送咚咚消息内容 0 不推送 1推送
      messageContent: '咚咚消息内容咚咚消息内容咚咚消息内容',
    });
    setDrawerVisible(true);
  };
  // 查看任务
  const editTask = (value, index, record) => {
    console.log(value, index, record, '编辑任务');
    const eventName = 'onEditButtonClick';
    if (Reflect.has(events, eventName)) {
      console.log('按钮操作2222222222', events[eventName]);
      Reflect.apply(events[eventName], null, [value, index, record]);
    } else {
      console.error(`method ${eventName} not found`);
    }
  };
  // 删除任务
  const deleteTask = (index, itemData) => {
    console.log(index, itemData, '删除任务');
    const formValue: any = field.getValues();
    Dialog.confirm({
      v2: true,
      title: '提示',
      centered: true,
      content: '是否确认删除该任务?',
      onOk: async (): Promise<void> => {
        setLoading(true);
        deletedActivityInfo({
          ...itemData,
          ...formValue,
        }).then((): void => {
          Message.success('操作成功');
          loadData({ ...formValue, ...defaultPage });
          setLoading(false);
        });
      },
    } as any);
  };
  const endTask = (index, itemData) => {
    console.log(index, itemData, '结束任务');
    const formValue: any = field.getValues();
    Dialog.confirm({
      v2: true,
      title: '提示',
      centered: true,
      content: '是否确认停止任务?',
      onOk: async (): Promise<void> => {
        setLoading(true);
        endActivityInfo({
          ...itemData,
          ...formValue,
        }).then((): void => {
          Message.success('操作成功');
          loadData({ ...formValue, ...defaultPage });
          setLoading(false);
        });
      },
    } as any);
  };
  // 发奖
  const sendPrizeTask = (index, itemData) => {
    const formValue: any = field.getValues();
    setLoading(true);
    manualAward({
      ...itemData,
    }).then((): void => {
      Message.success('发奖完成');
      loadData({ ...formValue, ...defaultPage });
      setLoading(false);
    });
  };
  // 获取加购商品数据
  const loadSkuListData = (query: any) => {
    console.log(query, '获取加购商品数据');
    setSkuListData([]);
    setLoadingDialog(true);
    getSkuInfo(query)
      .then((res: any): void => {
        setSkuListData(res.records as any[]);
        skuListPageInfo.total = +res.total!;
        skuListPageInfo.pageSize = +res.size!;
        skuListPageInfo.pageNum = +res.current!;
        setSkuListPageInfo(skuListPageInfo);
        setLoadingDialog(false);
      })
      .catch((e) => {
        setLoadingDialog(false);
      });
  };
  // 查看商品详情
  const skuListChange = (itemData) => {
    console.log('查看商品详情');
    setShowGoodDialog(true);
    setTaskId(itemData.taskId);
    const formValue: any = field.getValues();
    console.log(formValue, '加购商品弹窗分页');
    loadSkuListData({ taskId: itemData.taskId, ...formValue, ...defaultPage });
  };
  // 加购商品弹窗分页
  const handleSkuListPage = ({ pageSize, pageNum }) => {
    // setSkuListPageInfo({
    //   ...skuListPageInfo,
    //   pageSize,
    //   pageNum,
    // });
    const formValue: any = field.getValues();
    console.log(formValue, '加购商品弹窗分页');
    loadSkuListData({ taskId, ...formValue, pageSize, pageNum });
  };
  const exportData = () => {
    const formValue: any = field.getValues();
    // formValue.activityId = getParams('id');
    exportActivityInfoExport(formValue).then((data: any) => downloadExcel(data, '任务记录'));
  };
  return (
    <div className={styles.inCartManage}>
      <Loading visible={loading} style={{ width: '100%' }}>
        <LzPanel title="购物车营销">
          {/* <Tab activeKey={activeKey} defaultActiveKey="2" onChange={handleTabChange}> */}
          {/*  <Tab.Item title="未开始任务" key="1" /> */}
          {/*  <Tab.Item title="进行中任务" key="2" /> */}
          {/*  <Tab.Item title="已结束任务" key="3" /> */}
          {/* </Tab> */}
          <div>
            <Form className="lz-query-criteria" inline onSubmit={handleSubmit} field={field}>
              <Form.Item name="taskName" label="任务名称">
                <Input maxLength={20} placeholder="请输入任务名称" />
              </Form.Item>
              <Form.Item name="actType" label="任务状态" requiredMessage="请选择任务状态">
                <Select
                  followTrigger
                  mode="single"
                  showSearch
                  hasClear
                  style={{ marginRight: 8 }}
                  defaultValue={null}
                  dataSource={IS_WIN}
                />
              </Form.Item>
              <Form.Item name="rangeDate" label="创建时间">
                <RangePicker
                  showTime
                  hasClear={false}
                  defaultValue={defaultRangeVal}
                  format={constant.DATE_FORMAT_TEMPLATE}
                />
              </Form.Item>
              {/* <Form.Item name="planId" label="计划ID/优惠券ID"> */}
              {/*  <Input maxLength={20} placeholder="请输入计划ID/优惠券ID" /> */}
              {/* </Form.Item> */}
              <Form.Item colon={false}>
                <Button
                  type="primary"
                  onClick={() => {
                    history.push('/shoppingCartMarketing');
                  }}
                >
                  创建
                </Button>
                <Form.Submit type="primary" htmlType="submit">
                  查询
                </Form.Submit>
                <Form.Reset
                  toDefault
                  onClick={() => {
                    const formValue: any = field.getValues();
                    loadData({ ...formValue, ...defaultPage });
                  }}
                >
                  重置
                </Form.Reset>
                <Button onClick={() => exportData()}>导出</Button>
              </Form.Item>
            </Form>
            {/* <div style={{ margin: '10px 0', textAlign: 'right' }}>
              <Button onClick={() => exportData()}>导出</Button>
            </div> */}
            <Table.StickyLock style={{ marginTop: '20px' }} dataSource={tableData}>
              <Table.Column title="任务名称" lock="left" dataIndex="taskName" width={120} />
              <Table.Column title="任务ID" dataIndex="taskId" width={120} />
              <Table.Column title="已加购天数" dataIndex="day" width={60} />
              {/* <Table.Column */}
              {/*  title="是否发送短信" */}
              {/*  dataIndex="sendMessage" */}
              {/*  cell={(value, index, data) => <div>{data.sendMessage === 1 ? '发送' : '不发送'}</div>} */}
              {/* /> */}
              <Table.Column
                title="是否已执行发奖"
                width={80}
                dataIndex="taskState"
                cell={(value, index, data) => <div>{data.taskState === 1 ? '已发奖' : '未发奖'}</div>}
              />
              <Table.Column
                title="需发奖人群包总人数"
                dataIndex="taskState"
                width={80}
                cell={(value, index, data) => (
                  <div>
                    {data.taskState === 1 ? (data.sendTotalCount ? data.sendTotalCount : 0) : '--'}
                    {/* {data.sendTotalCount > 0 && ( */}
                    {/*  <div style={{ color: '#409eff', marginLeft: '10px' }} onClick={() => crowdDetailChange(0)}> */}
                    {/*    查看详情 */}
                    {/*  </div> */}
                    {/* )} */}
                  </div>
                )}
              />
              <Table.Column
                title="发奖成功人数"
                width={80}
                dataIndex="taskState"
                cell={(value, index, data) => (
                  <div>
                    {data.taskState === 1 ? (data.sendSuccessCount ? data.sendSuccessCount : 0) : '--'}
                    {/* {data.sendSuccessCount > 0 && ( */}
                    {/*  <div style={{ color: '#409eff', marginLeft: '10px' }} onClick={() => crowdDetailChange(1)}> */}
                    {/*    查看详情 */}
                    {/*  </div> */}
                    {/* )} */}
                  </div>
                )}
              />
              <Table.Column
                title="发奖失败人数"
                dataIndex="taskState"
                width={80}
                cell={(value, index, data) => (
                  <div>
                    {data.taskState === 1 ? (data.sendFailCount ? data.sendFailCount : 0) : '--'}
                    {/* {data.sendFailCount > 0 && ( */}
                    {/*  <div style={{ color: '#409eff', marginLeft: '10px' }} onClick={() => crowdDetailChange(2)}> */}
                    {/*    查看详情 */}
                    {/*  </div> */}
                    {/* )} */}
                  </div>
                )}
              />
              <Table.Column
                title="加购商品详情"
                dataIndex="skuNum"
                width={100}
                cell={(value, index, data) => (
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    {data.skuNum ? data.skuNum : '--'}
                    {data.skuNum > 0 && (
                      <Button style={{ marginLeft: '4px' }} text type="primary" onClick={() => skuListChange(data)}>
                        查看详情
                      </Button>
                    )}
                  </div>
                )}
              />
              <Table.Column
                title="任务时间"
                dataIndex="startTime"
                width={200}
                cell={(value, index, data) => (
                  <div>
                    <div>起：{format.formatDateTimeDayjs(data.startTime)}</div>
                    <div>止：{format.formatDateTimeDayjs(data.endTime)}</div>
                  </div>
                )}
              />
              <Table.Column
                title="创建时间"
                dataIndex="createTime"
                width={180}
                cell={(value, index, data) => <div>{format.formatDateTimeDayjs(data.createTime)}</div>}
              />
              <Table.Column
                title="操作"
                width={150}
                cell={(value, index, row) => (
                  <div>
                    {row.taskState === 0 && row.actType === 2 && (
                      <Button
                        style={{ marginRight: '10px' }}
                        text
                        type="primary"
                        onClick={() => sendPrizeTask(index, row)}
                      >
                        发奖
                      </Button>
                    )}
                    {(row.actType === 1 || row.actType === 2) && (
                      <Button text type="primary" onClick={() => endTask(index, row)}>
                        结束
                      </Button>
                    )}
                    {row.actType === 3 && (
                      <Button text type="primary" onClick={() => deleteTask(index, row)}>
                        删除
                      </Button>
                    )}
                    {/* {row.actType === 1 && ( */}
                    {/* <Button */}
                    {/*  style={{ marginLeft: '10px' }} */}
                    {/*  text */}
                    {/*  type="primary" */}
                    {/*  onClick={() => editTask(value, index, row)} */}
                    {/* > */}
                    {/*  编辑 */}
                    {/* </Button> */}
                    {/* )} */}
                    {/* <Button style={{ marginLeft: '10px' }} text type="primary" onClick={() => queryTask(index, row)}> */}
                    {/*  查看 */}
                    {/* </Button> */}
                  </div>
                )}
              />
            </Table.StickyLock>
            <LzPagination
              pageNum={pageInfo.pageNum}
              pageSize={pageInfo.pageSize}
              total={pageInfo.total}
              onChange={handlePage}
            />
          </div>
        </LzPanel>
        <Drawer
          title={
            <Tab
              navStyle={{ margin: '-13px -15px', width: '90%', border: 'none' }}
              onChange={setDrawerTabIndex}
              activeKey={drawerTabIndex}
            >
              <Tab.Item title="活动详情" key="1" />
            </Tab>
          }
          placement="right"
          visible={drawerVisible}
          width={750}
          bodyStyle={{
            height: 'calc(100% - 42px)',
            overflow: 'auto',
          }}
          closeMode={['close', 'esc', 'mask']}
          onClose={() => setDrawerVisible(false)}
        >
          {drawerTabIndex === '1' && <ActivityPreview data={activityInfo} />}
        </Drawer>
      </Loading>

      <div>
        <LzDialog
          title="加购商品"
          className="lz-dialog-mini"
          visible={showGoodDialog}
          footer={false}
          onCancel={() => setShowGoodDialog(false)}
          onClose={() => setShowGoodDialog(false)}
        >
          <Table dataSource={skuListData} loading={loadingDialog}>
            <Table.Column title="商品ID" dataIndex="skuId" />
          </Table>
          <LzPagination
            pageNum={skuListPageInfo.pageNum}
            pageSize={skuListPageInfo.pageSize}
            total={skuListPageInfo.total}
            onChange={handleSkuListPage}
          />
        </LzDialog>
      </div>
    </div>
  );
};
