import React, { useEffect, useState } from 'react';
import { Form, Input, DatePicker2, Field, Table, Button } from '@alifd/next';
import constant from '@/utils/constant';
import LzPagination from '@/components/LzPagination';
import { dataGiveLog, dataGiveLogExport } from '@/api/v10021';
import { IPageActivity10021UserGiveLogResponse } from '@/api/types';
import { deepCopy, downloadExcel, getParams } from '@/utils';
import dayJs from 'dayjs';
import format from '@/utils/format';

const FormItem = Form.Item;
const { RangePicker } = DatePicker2;

const defaultPage = {
  pageNum: 1,
  pageSize: 10,
  total: 0,
};

export default (props) => {
  const field = Field.useField();
  const [pageInfo, setPage] = useState(deepCopy(defaultPage));
  const [tableData, setTableData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const defaultRangeVal = [
    dayJs(new Date(getParams('startTime'))).format('YYYY-MM-DD 00:00:00'),
    dayJs(new Date(getParams('endTime'))).format('YYYY-MM-DD 23:59:59'),
  ];

  const handleSubmit = (e) => {
    e.preventDefault();
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  };

  const onRowSelectionChange = (selectKey: string[]): void => {
    console.log(selectKey);
  };
  const rowSelection: {
    mode: 'single' | 'multiple' | undefined;
    onChange: (selectKey: string[]) => void;
  } = {
    mode: 'multiple',
    onChange: (selectKey: string[]) => onRowSelectionChange(selectKey),
  };
  const loadData = (query: any): void => {
    setLoading(true);
    query.activityId = getParams('id');
    dataGiveLog(query)
      .then((res: IPageActivity10021UserGiveLogResponse): void => {
        setTableData(res.records as any[]);
        pageInfo.total = +res.total!;
        pageInfo.pageSize = +res.size!;
        pageInfo.pageNum = +res.current!;
        setPage(pageInfo);
        setLoading(false);
      })
      .catch((e) => {
        setLoading(false);
      });
  };
  const handlePage = ({ pageSize, pageNum }) => {
    setPage({
      ...pageInfo,
      pageSize,
      pageNum,
    });
    const formValue: any = field.getValues();
    loadData({ ...formValue, pageSize, pageNum });
  };

  const exportData = () => {
    const formValue: any = field.getValues();
    formValue.activityId = getParams('id');
    dataGiveLogExport(formValue).then((data: any) => downloadExcel(data, `抽奖机会赠送记录`));
  };
  useEffect(() => {
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  }, []);

  return (
    <div>
      <Form className="lz-query-criteria" field={field} colon labelAlign={'top'} onSubmit={handleSubmit}>
        <Form.Item name="nickName" label="客户昵称">
          <Input maxLength={20} placeholder="请输入客户昵称" />
        </Form.Item>
        <Form.Item name="pin" label="用户PIN">
          <Input placeholder="请输入用户PIN" />
        </Form.Item>
        <FormItem name="dateRange" label="赠送时间">
          <RangePicker
            showTime
            hasClear={false}
            defaultValue={defaultRangeVal}
            format={constant.DATE_FORMAT_TEMPLATE}
          />
        </FormItem>
        <FormItem colon={false}>
          <Form.Submit type="primary" htmlType="submit">
            查询
          </Form.Submit>
          <Form.Reset
            toDefault
            onClick={() => {
              const formValue: any = field.getValues();
              loadData({ ...formValue, ...defaultPage });
            }}
          >
            重置
          </Form.Reset>
        </FormItem>
      </Form>
      <div style={{ margin: '10px 0', textAlign: 'right' }}>
        <Button onClick={exportData}>导出</Button>
      </div>
      <Table rowSelection={rowSelection} dataSource={tableData} loading={loading}>
        <Table.Column title="客户昵称" dataIndex="nickName" />
        <Table.Column
          title="赠送时间"
          dataIndex="createTime"
          cell={(value, index, data) => <div>{format.formatDateTimeDayjs(data.createTime)}</div>}
        />
        <Table.Column title="赠送次数" dataIndex="chanceNum" />
        <Table.Column title="赠送原因" dataIndex="reason" />
      </Table>
      <LzPagination
        pageNum={pageInfo.pageNum}
        pageSize={pageInfo.pageSize}
        total={pageInfo.total}
        onChange={handlePage}
      />
    </div>
  );
};
