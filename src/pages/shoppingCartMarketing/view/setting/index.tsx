/**
 * Author: z<PERSON><PERSON><PERSON>
 * Date: 2023-05-29 18:03
 * Description: 活动设置
 */
import React, { useImperativeHandle, useRef, useReducer, useState } from 'react';
import styles from './style.module.scss';
import { PageData } from '@/pages/shoppingCartMarketing/view/util';
// 基础信息
import BaseInfo from './components/Base';
// 人群设置
import CrowdSet from './components/CrowdSet';
// 活动门槛
import Sill from './components/Sill';
// 加购商品
import CartGood from './components/CartGood';
// 权益设置
import Prize from './components/Prizes';
// 投放渠道
import PutChannel from './components/PutChannel';

interface Props {
  onSettingChange: (activityInfo: PageData, type) => void;
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onSubmit: (resultListLength: number) => void;
}
interface SettingProps extends Pick<Props, 'defaultValue' | 'value'> {
  onChange: (formData: Required<PageData>) => void;
}
export default ({ onSettingChange, defaultValue, value, sRef, onSubmit }: Props) => {
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  console.log(formData, 'formData======1111111111');
  const [renderBase] = useState(true);

  const onChange = (activityInfo, type?: string): void => {
    console.log('活动设置信息更新:', activityInfo);
    // if (activityInfo.supportLevels) {
    //   setTimeout(() => {
    //     setRenderPrize(true);
    //   });
    // }
    setFormData(activityInfo);
    onSettingChange(activityInfo, type);
  };
  const settingProps: SettingProps = {
    onChange,
    defaultValue,
    value,
  };
  // 各Form Ref
  const baseRef = useRef<{ submit: () => void | null }>(null);
  const crowdSetRef = useRef<{ submit: () => void | null }>(null);
  const sillRef = useRef<{ submit: () => void | null }>(null);
  const cartGoodRef = useRef<{ submit: () => void | null }>(null);
  const prizeRef = useRef<{ submit: () => void | null }>(null);
  const putChannelRel = useRef<{ submit: () => void | null }>(null);
  // 传递Ref
  useImperativeHandle(sRef, (): { submit: () => void } => ({
    submit: (): void => {
      // 异常结果列表
      const resultList: unknown[] = [];
      // 收集所有Form的submit 验证方法
      const events: any[] = [
        baseRef.current!,
        crowdSetRef.current,
        sillRef.current,
        cartGoodRef.current,
        prizeRef.current,
        putChannelRel.current,
      ];
      // 批量执行submit方法
      // submit只会抛出异常，未有异常返回null
      events.every((item, index: number): boolean => {
        const result = events[index].submit();
        if (result) {
          resultList.push(result);
          return false;
        }
        return true;
      });
      onSubmit(resultList.length);
    },
  }));
  return (
    <div className={styles.setting}>
      {renderBase && <BaseInfo sRef={baseRef} {...settingProps} />}
      <CrowdSet sRef={crowdSetRef} {...settingProps} />
      <Sill sRef={sillRef} {...settingProps} />
      <CartGood sRef={cartGoodRef} {...settingProps} />
      <Prize sRef={prizeRef} {...settingProps} />
      <PutChannel sRef={putChannelRel} {...settingProps} />
    </div>
  );
};
