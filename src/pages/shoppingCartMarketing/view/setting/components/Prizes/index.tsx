/**
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * Date: 2023-06-08 14:58
 * Description:
 */
import React, { useReducer, useEffect, useImperativeHandle, useState } from 'react';
import { Form, Field, Table, Button, Dialog } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import { FormLayout, PageData, PRIZE_INFO, PRIZE_TYPE } from '../../../util';
import LzDialog from '@/components/LzDialog';
import ChoosePrize from '@/components/ChoosePrizeNew';
import {activityEditDisabled} from "@/utils";

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}

const FormItem = Form.Item;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

export default ({ sRef, onChange, defaultValue, value }: Props) => {
  const [visible, setVisible] = useState(false);

  // 当前编辑行数据
  const [editValue, setEditValue] = useState(null);
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  const field: Field = Field.useField();

  // 同步/更新数据
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);

  useImperativeHandle(sRef, (): { submit: () => void } => ({
    submit: () => {
      let err: object | null = null;
      field.validate((errors): void => {
        err = errors;
      });
      return err;
    },
  }));
  const onPrizeChange = (data) => {
    // console.log(data, '奖品数据');
    setVisible(false);
    formData.prizeList[0] = data;
    setData(formData);
    console.log(data, formData.prizeList, formData, '奖品数据');
  };
  return (
    <div>
      <LzPanel title="权益设置">
        <Form {...formItemLayout} field={field}>
          <FormItem label="权益设置" required>
            <Table dataSource={formData.prizeList} style={{ marginTop: 10 }}>
              <Table.Column
                title="权益名称"
                dataIndex="prizeName"
                cell={(_, index, row) => <div>{row.prizeName}</div>}
              />
              <Table.Column
                title="权益类型"
                cell={(_, index, row) => <div>{PRIZE_TYPE[row.prizeType]}</div>}
                dataIndex="prizeType"
              />
              <Table.Column
                title="单位数量"
                cell={(_, index, row) => {
                  if (row.prizeType === 1) {
                    return <div>{row.numPerSending ? `${row.numPerSending}张` : ''}</div>;
                  } else {
                    return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
                  }
                }}
              />
              <Table.Column
                title="单份价值(元)"
                cell={(_, index, row) => (
                  <div>
                    {PRIZE_TYPE[row.prizeType] && row.unitPrice === 0
                      ? row.unitPrice
                      : row.unitPrice
                      ? Number(row.unitPrice).toFixed(2)
                      : ''}
                  </div>
                )}
              />
              <Table.Column
                title="发放份数"
                cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>}
              />
              <Table.Column
                title="操作"
                width={130}
                cell={(val, index, _) => (
                  <div>
                    <Button
                      disabled={activityEditDisabled()}
                      text
                      type="primary"
                      onClick={() => {
                        let row = formData.prizeList[index];
                        if (row.prizeName === '') {
                          row = null;
                        }
                        setEditValue(row);
                        setVisible(true);
                      }}
                    >
                      <i className={`iconfont icon-chaojiyingxiaoicon-25`} />
                    </Button>
                    <Button
                      text
                      disabled={activityEditDisabled()}
                      type="primary"
                      onClick={() => {
                        Dialog.confirm({
                          v2: true,
                          title: '提示',
                          centered: true,
                          content: '确认清空该奖品？',
                          onOk: () => {
                            formData.prizeList.splice(index, 1, PRIZE_INFO);
                          },
                          onCancel: () => console.log('cancel'),
                        } as any);
                      }}
                    >
                      <i className={`iconfont icon-shanchu`} />
                    </Button>
                  </div>
                )}
              />
            </Table>
          </FormItem>
        </Form>
        <div>
          <LzDialog
            title={false}
            visible={visible}
            footer={false}
            onClose={() => setVisible(false)}
            style={{ width: '670px' }}
          >
            <ChoosePrize
              formData={formData}
              editValue={editValue}
              hasLimit={false}
              hasProbability={false}
              typeList={[1]}
              defaultTarget={1}
              onChange={onPrizeChange}
              onCancel={() => setVisible(false)}
            />
          </LzDialog>
        </div>
      </LzPanel>
    </div>
  );
};
