/**
 * Author: zhang<PERSON>e
 * Date: 2023-06-02 11:30
 * Description:
 */
import React, { useReducer, useImperativeHandle, useEffect, useState } from 'react';
import LzPanel from '@/components/LzPanel';
import { Form, Field, Radio, Balloon, Icon, DatePicker2 } from '@alifd/next';
import {activityEditDisabled, getShopOrderStartTime, validateActivityThreshold} from '@/utils';
import { FormLayout, PageData } from '../../../util';
import LzThreshold from './LzThreshold';
import dayjs from 'dayjs';
import constant from '@/utils/constant';
import format from '@/utils/format';
import styles from './style.module.scss';
// import LzThreshold from '@/components/LzThreshold';

const dateFormat: string = constant.DATE_FORMAT_TEMPLATE;
const { RangePicker } = DatePicker2;
const RadioGroup = Radio.Group;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}
export default ({ onChange, defaultValue, value, sRef }: Props) => {
  // 用于 Fusion Form 表单校验
  // const field: Field = Field.useField({ values: value || defaultValue });
  const field: Field = Field.useField();
  // 同装修收集数据
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  const [shopOrderInfo, setShopOrderInfo] = useState({
    shopOrderStartTime: dayjs(formData.endTime).subtract(180, 'days').startOf('day').valueOf(),
    longTermOrder: false,
    orderRetentionDays: 180,
  });
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  useEffect(() => {
    getShopOrderStartTime().then((res) => {
      if (res.longTermOrder) {
        setShopOrderInfo({
          shopOrderStartTime: res.shopOrderStartTime,
          longTermOrder: res.longTermOrder,
          orderRetentionDays: shopOrderInfo.orderRetentionDays,
        });
      } else {
        setShopOrderInfo({
          shopOrderStartTime: dayjs(formData.endTime).subtract(res.shopOrderStartTime, 'days').startOf('day').valueOf(),
          longTermOrder: res.longTermOrder,
          orderRetentionDays: res.shopOrderStartTime,
        });
      }
    });
    setTimeout(() => {
      field.validate('orderRestrainRangeData');
    }, 1000);
  }, [formData.endTime]);
  // 同步活动设置数据
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  // 通过Ref触发表单校验
  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: any = null;

      field.validate((errors): void => {
        err = errors;
      });
      return validateActivityThreshold(formData, err, field);
    },
  }));
  const MoveTarget = <Icon type="help" id="top" style={{ marginRight: '10px' }} />;
  const rule = {
    1: '从未在店铺下过单的用户',
    2: '历史有过订单且近180天未在店铺下过单的用户',
    3: '近一年来，从未在该品牌下过单的用户',
  };
  // 下单时间校验
  // eslint-disable-next-line @typescript-eslint/no-shadow
  const validateOrderTime = (rule, val, callback): void => {
    const orderStartTime = val[0];
    const orderEndTime = val[1];
    if (!orderStartTime || !orderEndTime) {
      callback('请选下单时间');
    } else if (!shopOrderInfo.longTermOrder && dayjs(orderStartTime).valueOf() < shopOrderInfo.shopOrderStartTime) {
      callback(`下单时间不能早于活动结束时间前${shopOrderInfo.orderRetentionDays}天`);
    } else if (dayjs(orderEndTime).startOf('s').isAfter(dayjs(formData.endTime))) {
      callback('下单时间不能晚于活动结束时间');
    } else {
      callback();
    }
  };
  // 日期改变，处理提交数据
  const onDataRangeChange = (orderRestrainRangeData): void => {
    setData({
      orderRestrainRangeData,
      orderStartTime: format.formatDateTimeDayjs(orderRestrainRangeData[0]),
      orderEndTime: format.formatDateTimeDayjs(orderRestrainRangeData[1]),
    });
  };
  return (
    <div>
      <LzPanel title="活动门槛">
        {/* <Form {...formItemLayout} field={field} disabled={activityEditDisabled()}> */}
        <Form {...formItemLayout} field={field} disabled={activityEditDisabled()}>
          <Form.Item label="会员门槛" required>
            <LzThreshold formData={formData} field={field} setData={setData} applyGrade={[-10]} />
          </Form.Item>
          <Form.Item label="订单门槛" required>
            <RadioGroup
              value={formData.orderThreshold}
              onChange={(orderThreshold1: number) => {
                setData({
                  orderThreshold: orderThreshold1,
                  orderRestrainRangeData: [
                    (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
                    (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
                  ],
                  orderStartTime: (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
                  orderEndTime: (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
                });
              }}
            >
              <Radio id="0" value={0}>
                新用户
              </Radio>
              <Balloon v2 align="t" trigger={MoveTarget} triggerType="hover">
                {rule['1']}
              </Balloon>
              <Radio id="1" value={1}>
                回流用户
              </Radio>
              <Balloon v2 align="t" trigger={MoveTarget} triggerType="hover">
                {rule['2']}
              </Balloon>
              {/* <Radio id="2" value={2}> */}
              {/*  品牌一年新 */}
              {/* </Radio> */}
              {/* <Balloon v2 align="t" trigger={MoveTarget} triggerType="hover"> */}
              {/*  {rule['3']} */}
              {/* </Balloon> */}
            </RadioGroup>
          </Form.Item>
          <Form.Item label="下单时间" required requiredMessage="请选下单时间" validator={validateOrderTime}>
            <RangePicker
              className="w-300"
              name="orderRestrainRangeData"
              inputReadOnly
              format={dateFormat}
              hasClear={false}
              showTime
              value={
                formData.orderRestrainRangeData || [new Date(formData.orderStartTime), new Date(formData.orderEndTime)]
              }
              onChange={onDataRangeChange}
              disabledDate={(date) => {
                return date.valueOf() < dayjs(shopOrderInfo.shopOrderStartTime).startOf('day').valueOf();
              }}
            />
            <div className={styles.tip1}>
              注：1、默认支持查询
              {shopOrderInfo.longTermOrder
                ? `${dayjs(shopOrderInfo.shopOrderStartTime).format('YYYY-MM-DD')}以后`
                : `活动结束时间前${shopOrderInfo.orderRetentionDays}天内`}
              的订单数据 ，如需查询更早的历史订单数据，请联系对应客户经理。
              <br />
              {/* 2、若希望预售消费者参与本次活动，请在设置活动下单时间时包含预售开始时间（注：预售消费者支付定金的时间即为下单时间）。 */}
            </div>
          </Form.Item>
        </Form>
      </LzPanel>
    </div>
  );
};
