import React, { useEffect } from 'react';
import { Checkbox, DatePicker2, Form, Radio } from '@alifd/next';
import LzGradeSelectorNew from '../LzGradeSelectorNew';
// import { activityEditDisabled } from '@/utils';
import LzCrowdBag from '@/components/LzCrowdBag';
import dayjs from 'dayjs';
import format from '@/utils/format';
import constant from '@/utils/constant';

const { RangePicker } = DatePicker2;
const FormItem = Form.Item;
const dateFormat: string = constant.DATE_FORMAT_TEMPLATE;
export default (props) => {
  const { formData, setData, applyGrade, memberStr, joinTimeLimit = true } = props;
  const [times, setTimes] = React.useState<number>(0);
  const checkJoinTime = (rule, val, callback) => {
    if (val[0] && val[1]) {
      if (val[1].valueOf() > dayjs(formData.endTime).valueOf()) {
        callback('限制入会时间不能超过活动结束时间');
      } else {
        callback();
      }
    } else {
      callback('请选择限制入会时间范围');
    }
  };
  // 入会时间改变，处理提交数据
  const onJoinTimeRangeChange = (joinTimeRange: any): void => {
    setData({
      joinTimeRange,
      joinStartTime: joinTimeRange[0] ? format.formatDateTimeDayjs(joinTimeRange[0]) : '',
      joinEndTime: joinTimeRange[1] ? format.formatDateTimeDayjs(joinTimeRange[1]) : '',
    });
  };
  useEffect(() => {
    if (formData.crowdBag) {
      const time = Math.ceil(formData.crowdBag.packNum * 0.003);
      setTimes(time);
    }
  }, [formData]);
  return (
    <>
      {formData.threshold === 1 && (
        <>
          <FormItem
            label={false}
            colon={false}
            asterisk={false}
            required
            requiredMessage="请选择会员门槛"
            name="memberLevels"
          >
            <LzGradeSelectorNew
              name="memberLevels"
              value={formData.memberLevels}
              // disabled={activityEditDisabled()}
              applyGrade={applyGrade}
              memberStr={memberStr}
              onChange={(memberLevels, gradeLabel) => {
                if (memberLevels?.split(',').some((e: any) => e > 0)) {
                  setData({
                    memberLevels,
                    gradeLabel,
                  });
                } else {
                  setData({
                    memberLevels,
                    gradeLabel,
                    joinTimeLimit: 0,
                    joinTimeRange: [],
                    joinStartTime: '',
                    joinEndTime: '',
                  });
                }
              }}
            />
          </FormItem>
          {joinTimeLimit && formData.memberLevels?.split(',').some((e: any) => e > 0) && (
            <FormItem label={false} colon={false}>
              <FormItem>
                <Checkbox
                  value={formData.memberType}
                  checked={formData.memberType}
                  onChange={(values) => {
                    // console.log(values, 'xxxxxxxxxxx');
                    setData({ memberType: values ? 1 : 0 });
                  }}
                >
                  对应等级及以上
                </Checkbox>
              </FormItem>
            </FormItem>
          )}
          {joinTimeLimit && formData.memberLevels?.split(',').some((e: any) => e > 0) && (
            <FormItem label={false} colon={false}>
              <FormItem>
                <Radio.Group
                  name="joinTimeLimit"
                  value={formData.joinTimeLimit ?? 0}
                  onChange={(joinTimeLimit1) =>
                    setData({ joinTimeLimit: joinTimeLimit1, joinTimeRange: [], joinStartTime: '', joinEndTime: '' })
                  }
                >
                  <Radio value={0}>不限制入会时间</Radio>
                  <Radio value={1}>限制入会时间</Radio>
                </Radio.Group>
              </FormItem>
              {formData.joinTimeLimit === 1 && (
                <FormItem required validator={checkJoinTime} requiredMessage="请选择限制入会时间">
                  <RangePicker
                    name="joinTimeRange"
                    inputReadOnly
                    value={formData.joinTimeRange}
                    onChange={onJoinTimeRangeChange}
                    format={dateFormat}
                    hasClear={false}
                    showTime
                    disabledDate={(date) => {
                      return date.valueOf() > dayjs(formData.endTime).valueOf();
                    }}
                  />
                </FormItem>
              )}
            </FormItem>
          )}
        </>
      )}
      {formData.threshold === 2 && (
        <FormItem
          extra={
            <div>
              {!!formData.crowdBag && !formData.crowdBag.tableFlag && (
                <div>
                  <div className="next-form-item-help" style={{ color: 'red' }}>
                    {`人群包预计${times > 60 ? `${Math.ceil(times / 60)}分钟` : `${times}秒`}后生成完成`}
                  </div>
                  <div className="next-form-item-help" style={{ color: 'red' }}>
                    如果活动已开始，但人群包未生成完毕，可能会导致用户无法进入活动，请合理配置活动开始时间
                  </div>
                </div>
              )}
              <div className="next-form-item-help">注：满足所选人群包内的用户可参加活动</div>
            </div>
          }
          label={false}
          colon={false}
          required
          asterisk={false}
          requiredMessage="请选择人群包"
          name="crowdBag"
        >
          <LzCrowdBag
            name="crowdBag"
            value={formData.crowdBag}
            // disabled={activityEditDisabled()}
            onChange={(crowdBag) => {
              setData({ crowdBag });
            }}
          />
        </FormItem>
      )}
    </>
  );
};
