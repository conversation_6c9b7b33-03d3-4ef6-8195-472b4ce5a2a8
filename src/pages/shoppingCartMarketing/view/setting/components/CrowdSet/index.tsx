/**
 * Author: z<PERSON><PERSON><PERSON>
 * Date: 2023-06-02 11:30
 * Description:
 */
import React, { useReducer, useImperativeHandle, useEffect } from 'react';
import LzPanel from '@/components/LzPanel';
import { Form, Field, Radio } from '@alifd/next';
import {activityEditDisabled, validateActivityThreshold} from '@/utils';
import { FormLayout, PageData } from '../../../util';
// import LzThreshold from '@/components/LzThreshold';

const RadioGroup = Radio.Group;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}
export default ({ onChange, defaultValue, value, sRef }: Props) => {
  // 用于 Fusion Form 表单校验
  // const field: Field = Field.useField({ values: value || defaultValue });
  const field: Field = Field.useField();
  // 同装修收集数据
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  // 同步活动设置数据
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  // 通过Ref触发表单校验
  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: any = null;

      field.validate((errors): void => {
        err = errors;
      });
      return validateActivityThreshold(formData, err, field);
    },
  }));
  return (
    <div>
      <LzPanel title="人群设置">
        {/* <Form {...formItemLayout} field={field} disabled={activityEditDisabled()}> */}
        <Form {...formItemLayout} field={field}>
          <Form.Item label="人群设置" required>
            <RadioGroup
              disabled={activityEditDisabled()}
              value={formData.day}
              onChange={(day: number) => {
                setData({ day });
              }}
            >
              <Radio id="1" value={1}>
                近1天加购买家
              </Radio>
              <Radio id="2" value={2}>
                近2天加购买家
              </Radio>
              <Radio id="3" value={3}>
                近3天加购买家
              </Radio>
            </RadioGroup>
          </Form.Item>
        </Form>
      </LzPanel>
    </div>
  );
};
