/**
 * Author: z<PERSON><PERSON><PERSON>
 * Date: 2023-06-02 11:30
 * Description:
 */
import React, { useReducer, useImperativeHandle, useEffect } from 'react';
import LzPanel from '@/components/LzPanel';
import { Form, Field, Radio, Input, Grid } from '@alifd/next';
import {activityEditDisabled, validateActivityThreshold} from '@/utils';
import { FormLayout, PageData } from '../../../util';
// import LzThreshold from '@/components/LzThreshold';
import ChooseGoods from '@/components/ChooseGoods';
import SkuList from '@/components/SkuList';
// import styles from './style.module.scss';

const RadioGroup = Radio.Group;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}
export default ({ onChange, defaultValue, value, sRef }: Props) => {
  // 用于 Fusion Form 表单校验
  // const field: Field = Field.useField({ values: value || defaultValue });
  const field: Field = Field.useField();
  // 同装修收集数据
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  // 同步活动设置数据
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  // 通过Ref触发表单校验
  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: any = null;

      field.validate((errors): void => {
        err = errors;
      });
      return validateActivityThreshold(formData, err, field);
    },
  }));
  const handleSkuChange = (data) => {
    setData({ skuList: data });
    field.setErrors({ skuList: '' });
  };
  return (
    <div>
      <LzPanel title="加购商品">
        {/* <Form {...formItemLayout} field={field} disabled={activityEditDisabled()}> */}
        <Form {...formItemLayout} field={field} disabled={activityEditDisabled()}>
          <Form.Item label="加购商品" required>
            <RadioGroup
              disabled={activityEditDisabled()}
              value={formData.orderSkuisExposure}
              onChange={(orderSkuisExposure: number) => {
                setData({
                  orderSkuisExposure,
                  skuList: [],
                });
              }}
            >
              {/* <Radio id="0" value={0}> */}
              {/*  全部商品 */}
              {/* </Radio> */}
              <Radio id="1" value={1}>
                指定商品
              </Radio>
              {/* <Radio id="2" value={2}> */}
              {/*  排除商品 */}
              {/* </Radio> */}
            </RadioGroup>
            <Grid.Row>
              {(formData.orderSkuisExposure === 1 || formData.orderSkuisExposure === 2) && (
                <Form.Item name="skuList" required requiredMessage={'请选择加购商品'} style={{ marginTop: '15px' }}>
                  <Input className="validateInput" name="skuList" value={formData.skuList.length ? 1 : ''} />
                  <ChooseGoods  value={formData.skuList} max={10} onChange={handleSkuChange} />
                  <SkuList skuList={formData.skuList} />
                  {/*<div className={styles.tip}>*/}
                  {/*  <div>注：如选择【指定商品】，上限10个；</div>*/}
                  {/*  /!*<div>1.如选择【指定商品】，上限10个；</div>*!/*/}
                  {/*  /!*<div>2.如选择【全部商品】，商品中将会保护低值商品或赠品在内的所有商品，请谨慎配置；</div>*!/*/}
                  {/*</div>*/}
                </Form.Item>
              )}
            </Grid.Row>
          </Form.Item>
        </Form>
      </LzPanel>
    </div>
  );
};
