.alertSignDiv{
  margin-left: 10px;
}
.textAreaDiv{
  :global {
    .next-input.next-input-textarea{
      height: 120px;
      outline: #d7dde4;
      overflow-y: auto;
      padding: 10px;
      width: 100%;
      word-break: break-all;
    }
    textarea{
      height: 90%;
    }
  }
  .tipsDivAll{
    background-color: rgb(245, 247, 249);
    margin-top: 15px;
    width: 100%;
    height: 80px;
    display: flex;
    line-height: 35px;
    white-space: nowrap;
    padding: 10px 20px;
    justify-content: space-between;
    .tips1{
      span{
        padding-right: 2px;
        color: red;
      }
    }
    .tips2{
      span{
        padding-right: 2px;
        color: red;
      }
    }
    .rightTipsDiv{
      display: flex;
      align-items: flex-end;
    }
  }
  .testPhoneDiv{
    display: flex;
  }
}
.dongdongDivAll{
  align-items: center;
  display: -ms-flexbox;
  display: flex;
  justify-content: center;
  .dongdongLeftDiv{
    background-color: #f5f7f9;
    -ms-flex: 0 0 290px;
    flex: 0 0 290px;
    height: 460px;
    margin: 0 100px 0 75px;
    padding: 15px;
  }
  .dongdongCenterDiv{
    flex: 1 1 0%;
  }
  .dongdongRightDiv{
    flex: 1 1 0%;
    img{
      width: 230px;
      height: 458px;
    }
  }
}
