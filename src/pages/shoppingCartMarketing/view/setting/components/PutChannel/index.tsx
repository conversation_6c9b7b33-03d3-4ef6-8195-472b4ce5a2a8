/**
 * Author: z<PERSON><PERSON><PERSON>
 * Date: 2023-06-02 11:30
 * Description:
 */
import React, { useReducer, useImperativeHandle, useEffect } from 'react';
import LzPanel from '@/components/LzPanel';
import { Form, Field, Checkbox, Input, Button, Grid } from '@alifd/next';
import {activityEditDisabled, validateActivityThreshold} from '@/utils';
import { FormLayout, PageData } from '../../../util';
import styles from './index.module.scss';
import LzImageSelector from '@/components/LzImageSelector';

const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}
export default ({ onChange, defaultValue, value, sRef }: Props) => {
  // 用于 Fusion Form 表单校验
  // const field: Field = Field.useField({ values: value || defaultValue });
  const field: Field = Field.useField();
  // 同装修收集数据
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  // 同步活动设置数据
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  // 通过Ref触发表单校验
  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: any = null;

      field.validate((errors): void => {
        err = errors;
      });
      return validateActivityThreshold(formData, err, field);
    },
  }));
  return (
    <div>
      <LzPanel title="投放渠道">
        {/* <Form {...formItemLayout} field={field} disabled={activityEditDisabled()}> */}
        <Form {...formItemLayout} field={field} disabled={activityEditDisabled()}>
          <Form.Item label="短信投放" required>
            <Checkbox
              value={formData.channel}
              checked={formData.channel}
              onChange={(values) => {
                setData({ channel: values ? 1 : 0 });
              }}
            >
              短信投放
            </Checkbox>
          </Form.Item>
          {formData.channel === 1 && (
            <Form.Item label="短信内容" required>
              <Form.Item
                className={styles.textAreaDiv}
                label=""
                colon={false}
                required
                requiredMessage="请设置短信内容"
              >
                <Input.TextArea
                  value={formData.content}
                  placeholder="请输入短信内容"
                  aria-label="TextArea"
                  showLimitHint
                  maxLength={200}
                  onChange={(data) => {
                    setData({ content: data });
                  }}
                />
              </Form.Item>
            </Form.Item>
          )}
          <Form.Item label="咚咚消息" required>
            <Checkbox
              value={formData.sendDongDong}
              checked={formData.sendDongDong}
              onChange={(values) => {
                setData({ sendDongDong: values ? 1 : 0 });
              }}
            >
              咚咚消息
            </Checkbox>
          </Form.Item>
          {formData.sendDongDong === 1 && (
            <Form.Item label=" " colon={false}>
              <Checkbox
                value={formData.isPushMainImage}
                checked={formData.isPushMainImage}
                onChange={(values) => {
                  setData({ isPushMainImage: values ? 1 : 0 });
                }}
              >
                添加图片
              </Checkbox>
            </Form.Item>
          )}
          {formData.sendDongDong === 1 && formData.isPushMainImage === 1 && (
            <Form.Item label=" " colon={false}>
              <Grid.Row>
                <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                  <LzImageSelector
                    disabled={activityEditDisabled()}
                    value={formData.pushMainImage}
                    onChange={(pushMainImage) => {
                      setData({ pushMainImage });
                    }}
                  />
                </Form.Item>
                <Form.Item style={{ marginBottom: 0 }}>
                  <div className={styles.tip}>
                    <p>图片大小：不超过1M</p>
                    <p>图片格式：JPG、JPEG、PNG</p>
                  </div>
                  <div>
                    <Button
                      disabled={activityEditDisabled()}
                      type="primary"
                      text
                      onClick={() => {
                        setData({ pushMainImage: '' });
                      }}
                    >
                      重置
                    </Button>
                  </div>
                </Form.Item>
              </Grid.Row>
            </Form.Item>
          )}
          {formData.sendDongDong === 1 && (
            <Form.Item label=" " colon={false}>
              <Checkbox
                value={formData.isMessageContent}
                checked={formData.isMessageContent}
                onChange={(values) => {
                  setData({ isMessageContent: values ? 1 : 0 });
                }}
              >
                添加文本
              </Checkbox>
            </Form.Item>
          )}
          {formData.sendDongDong === 1 && formData.isMessageContent === 1 && (
            <Form.Item className={styles.textAreaDiv} label=" " colon={false}>
              <Input.TextArea
                value={formData.messageContent}
                placeholder="请输入咚咚消息内容"
                showLimitHint
                maxLength={200}
                onChange={(data) => {
                  setData({ messageContent: data });
                }}
              />
            </Form.Item>
          )}
        </Form>
      </LzPanel>
    </div>
  );
};
