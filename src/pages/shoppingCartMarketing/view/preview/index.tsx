/**
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * Date: 2023-06-07 09:50
 * Description: 活动预览
 */
import format from '@/utils/format';
import { Form, Table } from '@alifd/next';
import React, { useReducer } from 'react';
import { PRIZE_TYPE, PageData, formItemLayout, generateMembershipString } from '../util';
import styles from './style.module.scss';
import SkuList from '@/components/SkuList';

const FormItem = Form.Item;

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  labelAlign?: 'left' | 'top';
}
export default ({ defaultValue, value, labelAlign = 'left' }: Props) => {
  const [formData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  console.log(formData, '预览数据=========');
  formItemLayout.labelAlign = labelAlign;
  return (
    <div className={styles.preview}>
      <Form {...formItemLayout} isPreview>
        <FormItem label="任务名称">{formData.taskName}</FormItem>
        <FormItem label="任务时间">{`${format.formatDateTimeDayjs(formData.startTime)}至${format.formatDateTimeDayjs(
          formData.endTime,
        )}`}</FormItem>
        <FormItem label="人群设置">
          {formData.day === 1 ? '近1天加购买家' : formData.day === 1 ? '近2天加购买家' : '近3天加购买家'}
        </FormItem>
        <FormItem label="会员门槛">{generateMembershipString(formData)}</FormItem>
        <FormItem label="订单门槛">{formData.orderThreshold === 1 ? '新用户' : '回流用户'}</FormItem>
        <FormItem label="下单时间">{`${format.formatDateTimeDayjs(
          formData.orderStartTime,
        )}至${format.formatDateTimeDayjs(formData.orderEndTime)}`}</FormItem>
        <FormItem label="指定商品">
          {(formData.orderSkuisExposure === 1 || formData.orderSkuisExposure === 2) && (
            <>
              {formData.orderSkuisExposure === 1 && <div>指定商品</div>}
              <SkuList skuList={formData.skuList} />
            </>
          )}
        </FormItem>
        <FormItem label="奖品列表" isPreview={false}>
          <Table dataSource={formData.prizeList.filter((e) => e.prizeType > 0)} style={{ marginTop: '15px' }}>
            <Table.Column title="权益名称" dataIndex="prizeName" />
            <Table.Column title="权益类型" cell={(_, index, row) => <div>{PRIZE_TYPE[row.prizeType]}</div>} />
            <Table.Column
              title="单位数量"
              cell={(_, index, row) => <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>}
            />
            <Table.Column
              title="单份价值(元)"
              cell={(_, index, row) => <div>{row.unitPrice ? Number(row.unitPrice).toFixed(2) : 0}</div>}
            />
            <Table.Column
              title="发放份数"
              cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>}
            />
          </Table>
        </FormItem>
        <FormItem label="短信投放">{formData.channel === 1 ? '投放' : '不投放'}</FormItem>
        {formData.channel === 1 && <FormItem label="短信内容">{formData.content}</FormItem>}
        <FormItem label="咚咚消息">{formData.sendDongDong === 1 ? '投放' : '不投放'}</FormItem>
        {formData.sendDongDong === 1 && formData.isPushMainImage === 1 && (
          <FormItem label="咚咚消息图片">
            <img src={formData.pushMainImage} style={{ width: '200px' }} alt="" />
          </FormItem>
        )}
        {formData.sendDongDong === 1 && formData.isMessageContent === 1 && (
          <FormItem label="咚咚消息图片">{formData.messageContent}</FormItem>
        )}
      </Form>
    </div>
  );
};
