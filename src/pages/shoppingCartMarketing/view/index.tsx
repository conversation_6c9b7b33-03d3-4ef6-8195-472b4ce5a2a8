/**
 * Author: z<PERSON><PERSON><PERSON>
 * Date: 2023-05-30 10:53
 * Description:
 */
import React, { useState, useEffect, useRef } from 'react';
import styles from './style.module.scss';
// 活动设置
import Setting from './setting';
// 活动预览
import Preview from './preview';
// 活动完成
import Complete from './complete';
import LzPanel from '@/components/LzPanel';
// 风险控制组件
import LzRiskConfirm from '@/components/LzRiskConfirm';
import { Button, Dialog, Loading, Message } from '@alifd/next';
import { INIT_PAGE_DATA, PageData, checkActivityData, PrizeInfo } from './util';
import { createActivity } from '@/api/getCartUserPackage';
import { getParams, deepCopy } from '@/utils';

export default (props) => {
  const { history } = props;
  const [loading, setLoading] = useState<boolean>(false);
  const [riskLoading, setRiskLoading] = useState<boolean>(false);
  // 步骤
  const [step, setStep] = useState<number>(2);
  // 风险控制dialog
  const [risk, setRisk] = useState<boolean>(false);
  // 活动设置数据
  const [activityInfo, setActivityInfo] = useState<Required<PageData> | undefined>();
  // 活动默认数据
  const [defaultActivityInfo] = useState<Required<PageData>>(deepCopy(INIT_PAGE_DATA())); // iframe DOM
  // 活动设置模块Ref 用于校验各组件必填
  const settingRef = useRef<{ submit: () => void | null }>(null);
  // 创建好的活动id
  const [activityId, setActivityId] = useState<string>('');
  // 当前操作类型
  const operationType: string = getParams('type');
  /**
   * 保存草稿箱
   */
  // const saveDraft = async (): Promise<void> => {
  //   await createDraft({
  //     activityType,
  //     code,
  //     activityData: JSON.stringify(activityInfo || defaultActivityInfo),
  //   });
  //   Message.success('保存草稿成功！');
  // };
  /**
   * 初始化获取活动数据, 发送数据
   */
  const fetchActivityData = (): void => {
    console.log('查询')
    // setLoading(true);
    // getActivityInfo({} as any)
    //   .then((res: any): void => {
    //     res = {
    //       activityData: {
    //         shopId: localStorage.getItem('LZ_CURRENT_SHOP')
    //           ? JSON.parse(localStorage.getItem('LZ_CURRENT_SHOP') as string).shopId
    //           : '734259',
    //         // 活动名称
    //         taskName: `测试购物车营销-${dayjs().format('YYYY-MM-DD')}`,
    //         // 日期区间（不提交）
    //         rangeDate: [
    //           (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
    //           (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    //         ],
    //         // 活动开始时间
    //         startTime: (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
    //         // 活动结束时间
    //         endTime: (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    //         day: 1,
    //         orderThreshold: 0, // 订单门槛 0 新用户 1回流用户 2 品牌一年新
    //         // 限制订单时间
    //         orderRestrainRangeData: [
    //           (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
    //           (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    //         ],
    //         // 限制订单开始时间
    //         orderStartTime: (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
    //         // 限制订单结束时间
    //         orderEndTime: (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    //         // 活动门槛（不提交）
    //         threshold: 1,
    //         // 支持的会员等级(-1:无门槛；1,2,3,4,5,-12以逗号做分割(-12为对应等级及以上))
    //         memberLevels: '1,2,3,4,5',
    //         memberType: 1, // 0 未选择 1选择 是否选择对应等级及以上
    //         orderSkuisExposure: 1,
    //         skuList: [
    //           {
    //             jdPrice: '239.00',
    //             seq: 0,
    //             skuId: 10097246839573,
    //             skuMainPicture:
    //               'http://img10.360buyimg.com/n0/jfs/t1/207637/6/48883/83020/6732f47eF34ff0bdc/e52ac6323d267311.jpg',
    //             skuName: '广博联名酷企鹅A6手帐本礼盒套装手账本送礼笔记本子礼盒文具生日礼物 A6手帐套装-酷企鹅',
    //           },
    //         ],
    //         // 奖品列表
    //         prizeList: [
    //           {
    //             // 奖品名称
    //             prizeName: '预览测试',
    //             // 奖品图
    //             prizeImg: '',
    //             // 奖品类型
    //             prizeType: 1,
    //             // 中奖概率
    //             probability: 0,
    //             // 单位数量
    //             unitCount: 2,
    //             // 单位价值
    //             unitPrice: 2,
    //             // 发放份数
    //             sendTotalCount: 10,
    //             // 每日发放限额
    //             dayLimit: 0,
    //             // 每日发放限额类型 1 不限制 2限制
    //             dayLimitType: 1,
    //             ifPlan: 0,
    //           },
    //         ],
    //         // 限制入会时间 0：不限制；1：入会时间
    //         joinTimeLimit: 0,
    //
    //         templateCode: '',
    //         gradeLabel: ['一星会员', '二星会员', '三星会员', '四星会员', '五星会员'],
    //         crowdBag: null,
    //         channel: 1, // 0 短信不投放 1短信投放
    //         content: '短信投放短信投放短信投放短信投放', // 短信内容
    //         sendDongDong: 1, // 是否发送咚咚消息 0 不发送 1发送
    //         isPushMainImage: 1, // 是否推送咚咚消息主图 0 不推送 1推送
    //         pushMainImage: '',
    //         isMessageContent: 1, // 是否推送咚咚消息内容 0 不推送 1推送
    //         messageContent: '咚咚消息内容咚咚消息内容咚咚消息内容',
    //       },
    //     };
    //     if (res.activityData) {
    //       const { activityData } = res; // JSON.parse(res.activityData as string);
    //       setDefaultActivityInfo(deepCopy(activityData));
    //       setActivityInfo(activityData as any);
    //       console.log(activityData, activityInfo, defaultActivityInfo, '编辑活动数据=========');
    //     }
    //     setLoading(false);
    //   })
    //   .catch((e) => {
    //    const res = {
    //       activityData: {
    //         shopId: localStorage.getItem('LZ_CURRENT_SHOP')
    //           ? JSON.parse(localStorage.getItem('LZ_CURRENT_SHOP') as string).shopId
    //           : '734259',
    //         // 活动名称
    //         taskName: `测试购物车营销-${dayjs().format('YYYY-MM-DD')}`,
    //         // 日期区间（不提交）
    //         rangeDate: [
    //           (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
    //           (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    //         ],
    //         // 活动开始时间
    //         startTime: (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
    //         // 活动结束时间
    //         endTime: (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    //         day: 1,
    //         orderThreshold: 0, // 订单门槛 0 新用户 1回流用户 2 品牌一年新
    //         // 限制订单时间
    //         orderRestrainRangeData: [
    //           (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
    //           (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    //         ],
    //         // 限制订单开始时间
    //         orderStartTime: (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
    //         // 限制订单结束时间
    //         orderEndTime: (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    //         // 活动门槛（不提交）
    //         threshold: 1,
    //         // 支持的会员等级(-1:无门槛；1,2,3,4,5,-12以逗号做分割(-12为对应等级及以上))
    //         memberLevels: '1,2,3,4,5',
    //         memberType: 1, // 0 未选择 1选择 是否选择对应等级及以上
    //         orderSkuisExposure: 1,
    //         skuList: [
    //           {
    //             jdPrice: '239.00',
    //             seq: 0,
    //             skuId: 10097246839573,
    //             skuMainPicture:
    //               'http://img10.360buyimg.com/n0/jfs/t1/207637/6/48883/83020/6732f47eF34ff0bdc/e52ac6323d267311.jpg',
    //             skuName: '广博联名酷企鹅A6手帐本礼盒套装手账本送礼笔记本子礼盒文具生日礼物 A6手帐套装-酷企鹅',
    //           },
    //         ],
    //         // 奖品列表
    //         prizeList: [
    //           {
    //             // 奖品名称
    //             prizeName: '预览测试',
    //             // 奖品图
    //             prizeImg: '',
    //             // 奖品类型
    //             prizeType: 1,
    //             // 中奖概率
    //             probability: 0,
    //             // 单位数量
    //             unitCount: 2,
    //             // 单位价值
    //             unitPrice: 2,
    //             // 发放份数
    //             sendTotalCount: 10,
    //             // 每日发放限额
    //             dayLimit: 0,
    //             // 每日发放限额类型 1 不限制 2限制
    //             dayLimitType: 1,
    //             ifPlan: 0,
    //           },
    //         ],
    //         // 限制入会时间 0：不限制；1：入会时间
    //         joinTimeLimit: 0,
    //
    //         templateCode: '',
    //         gradeLabel: ['一星会员', '二星会员', '三星会员', '四星会员', '五星会员'],
    //         crowdBag: null,
    //         channel: 1, // 0 短信不投放 1短信投放
    //         content: '短信投放短信投放短信投放短信投放', // 短信内容
    //         sendDongDong: 1, // 是否发送咚咚消息 0 不发送 1发送
    //         isPushMainImage: 1, // 是否推送咚咚消息主图 0 不推送 1推送
    //         pushMainImage: '',
    //         isMessageContent: 1, // 是否推送咚咚消息内容 0 不推送 1推送
    //         messageContent: '咚咚消息内容咚咚消息内容咚咚消息内容',
    //       },
    //     };
    //     if (res.activityData) {
    //       const { activityData } = res; // JSON.parse(res.activityData as string);
    //       setDefaultActivityInfo(deepCopy(activityData));
    //       setActivityInfo(activityData as any);
    //       console.log(activityData, activityInfo, defaultActivityInfo, '编辑活动数据=========');
    //     }
    //     setLoading(false);
    //   });
    // setLoading(false);
  };
  /**
   * 活动设置内容
   * @param data 活动配置数据
   * @param type 用于c端弹出任务列表
   */
  const handleSettingChange = (data, type?: string): void => {
    setActivityInfo(data);
  };
  /**
   * 设置成功的回调
   * @param errors 活动设置各Form校验后返回的异常
   */
  const handleSubmitSetting = (errors): void => {
    if (!errors) {
      // 判断奖品列表是否存在有效奖品;
      const prizeList = activityInfo!.prizeList.filter(
        (e: Pick<PrizeInfo, 'prizeName'>): boolean => e.prizeName !== '谢谢参与',
      );
      const isValidateData: boolean = checkActivityData(prizeList, activityInfo!);
      if (!isValidateData) {
        return;
      }
      console.log('保存预览数据', activityInfo);
      // setLoading(true);
      Dialog.confirm({
        v2: true,
        title: '提示',
        centered: true,
        content: '是否确认创建该任务?',
        onOk: async (): Promise<void> => {
          try {
            setLoading(true);
            createActivity({
              activityData: { ...(activityInfo || defaultActivityInfo) } as any,
            })
              .then((res: { activityId: string }): void => {
                setActivityId(res.activityId);
                setLoading(false);
                Message.success(`活动创建成功`);
                setStep(4);
              })
              .catch((err) => {
                setLoading(false);
                Message.error(err.message);
              });
          } catch (e) {
            Message.error(e.message);
          }
        },
      } as any);
    }
  };
  /**
   * 风险提示确认回调
   */
  const handleRiskConfirm = (): void => {
    setRiskLoading(true);
    const isEdit: boolean = operationType === 'edit';
    // const events: [Function, Function] = [createActivity];
    createActivity({
      activityData: { ...(activityInfo || defaultActivityInfo), activityId: getParams('id') } as any,
    })
      .then((res: { activityId: string }): void => {
        setActivityId(res.activityId);
        setRisk(false);
        setRiskLoading(false);
        Message.success(`活动${isEdit ? '编辑' : '创建'}成功`);
        setStep(step + 1);
      })
      .catch((err) => {
        setRiskLoading(false);
        Message.error(err.message);
      });
  };
  // 监听步骤条，开启选中态
  useEffect((): void => {
    fetchActivityData();
  }, [step]);
  // 设置模板按钮
  const StepOneBtn = () => (
    <div className="crm-footer">
      {/* <Button onClick={saveDraft}>保存草稿箱</Button> */}
      <Button
        type="primary"
        onClick={() => {
          setStep(step + 1);
        }}
        disabled={loading}
      >
        下一步1
      </Button>
    </div>
  );
  // 活动设置按钮
  const StepTwoBtn = () => (
    <div className="crm-footer">
      {/* <Button onClick={saveDraft}>保存草稿箱</Button> */}
      {/* <Button onClick={() => setStep(step - 1)}>上一步</Button> */}
      <Button
        type="primary"
        onClick={() => {
          settingRef.current!.submit();
        }}
      >
        创建任务
      </Button>
    </div>
  );
  // 活动预览按钮
  const StepThreeBtn = () => (
    <div className="crm-footer">
      <Button onClick={() => setStep(step - 1)}>上一步</Button>
      <Button type="primary" onClick={() => setRisk(true)}>
        完成3
      </Button>
    </div>
  );
  // 渲染步骤按钮
  const renderStep = () => {
    const stepMap = {
      1: <StepOneBtn />,
      2: <StepTwoBtn />,
      3: <StepThreeBtn />,
    };
    return stepMap[step];
  };
  return (
    <div className="crm-container">
      <Loading visible={loading} inline={false}>
        <div className={styles.container}>
          <div className={styles.operation}>
            {step === 2 && (
              <Setting
                sRef={settingRef}
                onSettingChange={handleSettingChange}
                defaultValue={defaultActivityInfo}
                value={activityInfo}
                onSubmit={handleSubmitSetting}
              />
            )}
            {step === 3 && (
              <LzPanel title="活动预览">
                <Preview defaultValue={defaultActivityInfo} value={activityInfo} />
              </LzPanel>
            )}
            {step === 4 && (
              <Complete
                history={history}
                activityId={activityId}
                supDate={props.location.state && props.location.state.supDate ? props.location.state.supDate : ''}
                taskId={props.location.state && props.location.state.taskId ? props.location.state.taskId : ''}
              />
            )}
          </div>
        </div>
        {renderStep()}
      </Loading>
      <LzRiskConfirm
        loading={riskLoading}
        riskVisible={risk}
        onClose={() => setRisk(false)}
        onSubmit={handleRiskConfirm}
      />
    </div>
  );
};
