/**
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * Date: 2023-06-07 11:37
 * Description:
 */
import React from 'react';
import dayjs, { Dayjs } from 'dayjs';
import { Message } from '@alifd/next';
import { getParams } from '@/utils';
import Preview from '@/components/LzCrowdBag/preview';

export interface FormLayout {
  labelCol: {
    span?: number;
    fixedSpan?: number;
  };
  wrapperCol: {
    span: number;
  };
  labelAlign: 'left' | 'top';
  colon: boolean;
}

export interface PrizeInfo {
  prizeName: string;
  prizeImg: string;
  prizeType: number;
  probability: string | number;
  unitCount: number;
  unitPrice: number;
  sendTotalCount: number;
  dayLimit: number;
  dayLimitType: number;
  ifPlan: number;
  startTime?: string;
  endTime?: string;
  startDate?: string;
  endDate?: string;
}
export interface PageData {
  taskName: string;
  rangeDate: [Dayjs, Dayjs];
  startTime: string;
  endTime: string;
  day: number;
  orderThreshold: number;
  threshold: number;
  memberLevels: string;
  memberType: number;
  orderSkuisExposure: number;
  skuList: any[];
  // 限制入会时间 0：不限制；1：入会时间
  joinTimeLimit: number;
  joinTimeRange: [string, string] | [Dayjs, Dayjs];
  joinStartTime: string;
  joinEndTime: string;
  crowdBag: any;
  prizeList: PrizeInfo[]; // 根据实际情况，可能需要定义奖品的类型
  templateCode: string;
  gradeLabel: string[];
  channel: number;
  content: string;
  sendDongDong: number;
  isPushMainImage: number;
  pushMainImage: string;
  isMessageContent: number;
  messageContent: string;
  orderRestrainRangeData: [Dayjs, Dayjs];
  orderStartTime: string;
  orderEndTime: string;
  shopId?: number | string;
}

export const INIT_PAGE_DATA = (): PageData => {
  return {
    shopId: localStorage.getItem('LZ_CURRENT_SHOP')
      ? JSON.parse(localStorage.getItem('LZ_CURRENT_SHOP') as string).shopId
      : '734259',
    // 活动名称
    taskName: `购物车营销-${dayjs().format('YYYY-MM-DD')}`,
    // 日期区间（不提交）
    rangeDate: [
      (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
      (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    ],
    // 活动开始时间
    startTime: (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
    // 活动结束时间
    endTime: (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    day: 1,
    orderThreshold: 0, // 订单门槛 0 新用户 1回流用户 2 品牌一年新
    // 限制订单时间
    orderRestrainRangeData: [
      (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
      (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    ],
    // 限制订单开始时间
    orderStartTime: (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
    // 限制订单结束时间
    orderEndTime: (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    // 活动门槛（不提交）
    threshold: 1,
    // 支持的会员等级(-1:无门槛；1,2,3,4,5,-12以逗号做分割(-12为对应等级及以上))
    memberLevels: '1,2,3,4,5',
    memberType: 1, // 0 未选择 1选择 是否选择对应等级及以上
    orderSkuisExposure: 1,
    skuList: [],
    // 奖品列表
    prizeList: [
      {
        // 奖品名称
        prizeName: '',
        // 奖品图
        prizeImg: '',
        // 奖品类型
        prizeType: 0,
        // 中奖概率
        probability: 0,
        // 单位数量
        unitCount: 0,
        // 单位价值
        unitPrice: 0,
        // 发放份数
        sendTotalCount: 0,
        // 每日发放限额
        dayLimit: 0,
        // 每日发放限额类型 1 不限制 2限制
        dayLimitType: 1,
        ifPlan: 0,
      },
    ],
    // 限制入会时间 0：不限制；1：入会时间
    joinTimeLimit: 0,
    // 入会时间
    joinTimeRange: [] as any,
    joinStartTime: '',
    joinEndTime: '',

    templateCode: '',
    gradeLabel: [],
    crowdBag: null,
    channel: 1, // 0 短信不投放 1短信投放
    content: '', // 短信内容
    sendDongDong: 1, // 是否发送咚咚消息 0 不发送 1发送
    isPushMainImage: 1, // 是否推送咚咚消息主图 0 不推送 1推送
    pushMainImage: '',
    isMessageContent: 1, // 是否推送咚咚消息内容 0 不推送 1推送
    messageContent: '',
  };
};

interface PrizeType {
  [key: number]: string;
}
export const PRIZE_TYPE: PrizeType = {
  12: '京元宝权益',
  2: '京豆',
  1: '优惠券',
  3: '实物',
  4: '积分',
  6: '红包',
  7: '礼品卡',
  8: '京东E卡',
  9: 'PLUS会员卡',
  10: '爱奇艺会员卡',
};
// form格式
export const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

export const PRIZE_INFO: PrizeInfo = {
  // 奖品名称
  prizeName: '谢谢参与',
  // 奖品图
  prizeImg: '',
  // 奖品类型
  prizeType: 0,
  // 中奖概率
  probability: 0,
  // 单位数量
  unitCount: 0,
  // 单位价值
  unitPrice: 0,
  // 发放份数
  sendTotalCount: 0,
  // 每日发放限额
  dayLimit: 0,
  // 每日发放限额类型 1 不限制 2限制
  dayLimitType: 1,
  ifPlan: 0,
};
// 预览二维码大小
export const QRCODE_SIZE = 74;
// 预览二维码配置项
export const QRCODE_SETTING = {
  src: require('@/assets/images/jd-logo.webp'),
  height: QRCODE_SIZE / 7.4,
  width: QRCODE_SIZE / 7.4,
  excavate: false,
};

// 生成会员等级字符串
export const generateMembershipString = (formData: PageData, type?: string) => {
  if (formData.threshold === 1) {
    const levels = formData.memberLevels.split(',');
    const LEVEL_MAP = formData.gradeLabel;
    const memberLevelsList = levels.filter((e) => Number(e) > 0);
    const membershipLevels = memberLevelsList.map((l, index) => LEVEL_MAP[index]).join('，');
    const membershipString = memberLevelsList.length ? `店铺会员（${membershipLevels}）` : '';
    const extraLevelsString = levels
      .filter((e) => Number(e) < 0)
      .map((l, index) => LEVEL_MAP[index + memberLevelsList.length])
      .join('、');

    const isMemberType = formData.memberType === 1 ? '对应等级及以上' : '';
    const joinTimeLimitString =
      formData.joinTimeLimit === 1 ? `。限制入会时间：${formData.joinStartTime}至${formData.joinEndTime}` : '';

    return (
      membershipString +
      (extraLevelsString && `${memberLevelsList.length > 0 ? '、' : ''}${extraLevelsString}`) + isMemberType +
      joinTimeLimitString
    );
  }
  if (formData.threshold === 2) {
    return !type ? <Preview value={formData.crowdBag} /> : `人群包<${formData.crowdBag.packName}>`;
  }
  return '不限制';
};
// 是否未编辑
const isProcessingEditType = (): boolean => {
  return getParams('type') === 'edit';
};
// 校验开始时间是否符合规则
const isStartTimeValid = (startTime: string): boolean => {
  // const isLessThanTenMinutes: boolean = dayjs(startTime).isAfter(dayjs().subtract(10, 'minute'));
  // if (!isLessThanTenMinutes) {
  //   Message.error('活动开始时间应大于当前时间');
  //   return false;
  // }
  // return true;
  const isLessThanTenMinutes: boolean = dayjs(startTime).isBefore(dayjs().startOf('day'));
  if (isLessThanTenMinutes) {
    Message.error('任务开始时间应大于当天时间');
    return false;
  }
  return true;
};
// 校验结束时间是否符合规则
const isEndTimeValid = (startTime: string, endTime: string): boolean => {
  const isGreaterThanNow: boolean = dayjs(endTime).isAfter(dayjs());
  const isGreaterThanStart: boolean = dayjs(endTime).isAfter(dayjs(startTime));

  if (!isGreaterThanStart) {
    Message.error('任务结束时间应大于任务开始时间');
    return false;
  }
  if (!isGreaterThanNow) {
    Message.error('任务结束时间应大于当前时间');
    return false;
  }
  return true;
};
// 校验奖品时间是否符合规则
const isPrizeValid = (prize: PrizeInfo, formData: PageData): boolean => {
  const type = prize.prizeType;
  // 0-谢谢参与 3-实物 4-积分 没有时间直接通过
  if (type === 0 || type === 3 || type === 4) {
    return true;
  }
  // 开始时间
  const start = prize.startTime || prize.startDate;
  // 结束时间
  const end = prize.endTime || prize.endDate;

  if (start && end) {
    // 奖品的开始时间间是否小于活动的开始时间
    const isStart: boolean = dayjs(formData.startTime).isAfter(dayjs(start));
    if (!isStart) {
      Message.error(`权益${PRIZE_TYPE[prize.prizeType]}开始时间应小于任务开始时间`);
      return false;
    }
    // 奖品的结束时间间是否大于活动的结束时间
    const isEnd: boolean = dayjs(formData.endTime).isBefore(dayjs(end));
    if (!isEnd) {
      Message.error(`权益${PRIZE_TYPE[prize.prizeType]}结束时间应大于任务结束时间`);
      return false;
    }
  }
  return true;
};
// 校验奖品时间是否符合规则
const arePrizesValid = (prizeList: PrizeInfo[], formData: PageData): boolean => {
  for (let i = 0; i < prizeList.length; i++) {
    if (!isPrizeValid(prizeList[i], formData)) {
      return false;
    }
  }
  return true;
};
const hasPrize = (prizeList: PrizeInfo[]): boolean => {
  if (!prizeList.length) {
    Message.error('请设置奖品');
    return false;
  }
  const prizeListNew1 = prizeList.filter((e) => e.prizeType > 0);
  if (prizeListNew1.length <= 0) {
    Message.error('请设置奖品');
    return false;
  }
  return true;
};
const checkChannel = (formData: PageData) => {
  if (formData.channel === 1) {
    if (!formData.content) {
      Message.error('请输入短信内容');
      return false;
    }
  }
  if (formData.sendDongDong === 1) {
    if (formData.isPushMainImage === 1) {
      if (!formData.pushMainImage) {
        Message.error('请选择咚咚消息图片');
        return false;
      }
    }
    if (formData.isMessageContent === 1) {
      if (!formData.messageContent) {
        Message.error('请选择咚咚消息内容');
        return false;
      }
    }
  }
  return true;
};
export const checkActivityData = (prizeList: PrizeInfo[], formData: PageData): boolean => {
  console.log('活动校验', formData);
  // 编辑直接通过，不走校验
  if (isProcessingEditType()) {
    return true;
  }

  // 没有选择奖品
  if (!hasPrize(prizeList)) {
    return false;
  }
  if (!checkChannel(formData)) {
    return false;
  }
  // 开始时间异常
  if (!isStartTimeValid(formData.startTime)) {
    return false;
  }
  // 结束时间异常
  if (!isEndTimeValid(formData.startTime, formData.endTime)) {
    return false;
  }
  // 奖品时间与活动时间冲突
  if (!arePrizesValid(prizeList, formData)) {
    return false;
  }
  return true;
};
