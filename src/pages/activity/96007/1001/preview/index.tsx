/**
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * Date: 2023-06-07 09:50
 * Description: 活动预览
 */
import React, { useReducer } from 'react';
import { Form, Table, Input } from '@alifd/next';
import { formItemLayout, generateMembershipString, PageData, PRIZE_TYPE, evaluateGradeList } from '../util';
import styles from './style.module.scss';
import { goToPropertyList } from '@/utils';
import format from '@/utils/format';
import SkuList from '@/components/SkuList';

const FormItem = Form.Item;

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  labelAlign?: 'left' | 'top';
}

// eslint-disable-next-line complexity
export default ({ defaultValue, value, labelAlign = 'left' }: Props) => {
  const [formData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);

  formItemLayout.labelAlign = labelAlign;
  return (
    <div className={styles.preview}>
      <Form {...formItemLayout} isPreview>
        <FormItem label="活动标题">{formData.activityName}</FormItem>
        <FormItem label="活动时间">{`${format.formatDateTimeDayjs(formData.rangeDate[0])}至${format.formatDateTimeDayjs(
          formData.rangeDate[1],
        )}`}</FormItem>
        <FormItem label="活动门槛">{generateMembershipString(formData)}</FormItem>
        <FormItem label="评价时间">
          {format.formatDateTimeDayjs(formData.evaluateTime[0])}至{format.formatDateTimeDayjs(formData.evaluateTime[1])}
        </FormItem>
        <FormItem label="评价等级">
          {evaluateGradeList.find((item) => item.value === formData.evaluateGrade)?.label}
        </FormItem>
        <FormItem label="评价字数">
          {formData.evaluateNumberType === 1 && '不限制'}
          {formData.evaluateNumberType === 2 && `${formData.evaluateNumber}`}
        </FormItem>
        <FormItem label="负面词过滤">
          {formData.evaluateFilterateType === 1 && '不限制'}
          {formData.evaluateFilterateType === 2 && `${formData.evaluateFilterate}`}
        </FormItem>
        <FormItem label="评价图片数量">
          {formData.evaluateImgType === 1 && '不限制'}
          {formData.evaluateImgType === 2 && `${formData.evaluateImg}`}
        </FormItem>
        <FormItem label="评价视频">
          {formData.evaluateVideoType === 1 && '不限制'}
          {formData.evaluateVideoType === 2 && '评论必须包含一个以上视频'}
        </FormItem>
        <FormItem label="评价商品">
          {formData.isEvaluateSku === 1 && <div>全部商品</div>}
          {formData.isEvaluateSku === 0 && (
            <div>
              指定商品
              <SkuList skuList={formData.evaluateSkuList} />
            </div>
          )}
          {formData.isEvaluateSku === 2 && (
            <div>
              排除商品
              <SkuList skuList={formData.evaluateSkuList} />
            </div>
          )}
        </FormItem>

        <FormItem label="奖品列表" isPreview={false}>
          <Table
            dataSource={formData.prizeList.filter((e) => e.prizeName !== '谢谢参与')}
            style={{ marginTop: '15px' }}
          >
            <Table.Column title="奖项" cell={(_, index) => <div>{index + 1}</div>} />
            <Table.Column title="奖项名称" dataIndex="prizeName" />
            <Table.Column
              title="奖项类型"
              cell={(_, index, row) => (
                <div style={{ cursor: 'pointer' }} onClick={() => goToPropertyList(row.prizeType)}>
                  {PRIZE_TYPE[row.prizeType]}
                </div>
              )}
            />
            <Table.Column
              title="单位数量"
              cell={(_, index, row) => {
                if (row.prizeType === 1) {
                  return <div>{row.numPerSending ? `${row.numPerSending}张` : ''}</div>;
                } else {
                  return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
                }
              }}
            />
            <Table.Column
              title="发放份数"
              cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>}
            />
            <Table.Column
              title="单份价值(元)"
              cell={(_, index, row) => <div>{row.unitPrice ? Number(row.unitPrice).toFixed(2) : ''}</div>}
            />
            <Table.Column
              title="中奖概率(%)"
              cell={(_, index, row) => <div>{row.probability ? row.probability : ''}</div>}
            />
            <Table.Column
              title="每日发放限额"
              cell={(_, index, row) => <div>{row.dayLimitType === 2 ? row.dayLimit : '不限'}</div>}
            />
            <Table.Column
              title="奖品图"
              cell={(_, index, row) => <img style={{ width: '30px' }} src={row.prizeImg} alt="" />}
            />
          </Table>
        </FormItem>
        <FormItem label="每人每天最多获奖次数">
          {formData.winLotteryDayType === 1 && '不限制'}
          {formData.winLotteryDayType === 2 && `限制每天内用户最多中奖${formData.winLotteryDayCounts}次`}
        </FormItem>
        <FormItem label="每人累计最多中奖次数">
          {formData.winLotteryTotalType === 1 && '不限制'}
          {formData.winLotteryTotalType === 2 && `限制活动周期内用户最多中奖${formData.winLotteryTotalCounts}次`}
        </FormItem>

        <FormItem label="是否添加曝光商品">
          {formData.isExposure === 0 && <div>否</div>}
          {formData.isExposure === 1 && <SkuList skuList={formData.skuList} />}
        </FormItem>

        <FormItem label="活动分享">{formData.shareStatus === 0 ? '关闭' : '开启'}</FormItem>
        {formData.shareStatus !== 0 && (
          <>
            <FormItem label="分享标题">{formData.shareTitle}</FormItem>
            <FormItem label="图文分享图片">
              <img src={formData.h5Img} style={{ width: '200px' }} alt="" />
            </FormItem>
            <FormItem label="京口令分享图片">
              <img src={formData.cmdImg} style={{ width: '200px' }} alt="" />
            </FormItem>
            <FormItem label="小程序分享图片">
              <img src={formData.mpImg} style={{ width: '200px' }} alt="" />
            </FormItem>
          </>
        )}
        <FormItem label="规则内容">
          <Input.TextArea className="rule-word-break" value={formData.rules} />
        </FormItem>
      </Form>
    </div>
  );
};
