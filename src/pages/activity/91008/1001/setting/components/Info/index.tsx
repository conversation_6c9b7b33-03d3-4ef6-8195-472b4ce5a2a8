import { Form, Checkbox, Field, Grid, Message } from '@alifd/next';
import { FormLayout, PageData } from '../../../util';
import React, { useEffect, useImperativeHandle, useReducer, useState } from 'react';
import { activityEditDisabled, validateActivityThreshold } from '@/utils';
import LzPanel from '../../../../../../../components/LzPanel';
// import styles from '@/pages/activity/91008/1001/complete/index.module.scss';

const { Row, Col } = Grid;
const FormItem = Form.Item;

const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}

const options = [
  {
    personName: '姓名',
    disabled: true,
  },
  {
    personName: '生日',
    disabled: true,
  },
  {
    personName: '性别',
    disabled: true,
  },
  {
    personName: '手机号',
    disabled: false,
  },
  {
    personName: '邮箱',
    disabled: false,
  },
  {
    personName: '地址',
    disabled: false,
  },
  // '姓名', '生日', '手机号', '性别', '邮箱', '地址'
];

export default ({ onChange, defaultValue, value, sRef }: Props) => {
  // 用于 Fusion Form 表单校验
  const field: Field = Field.useField();
  // 同装修收集数据
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  // 同步活动设置数据
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  const [selectedOptions, setSelectedOptions] = useState<string[]>([]); // 用于跟踪已选中的选项

  // const handleAddCustomInput = () => {
  //   if (activityEditDisabled()) {
  //     Message.notice('进行中的活动不允许修改信息条目');
  //     return;
  //   }
  //   const updatedpersonRequests = [
  //     ...formData.personRequests,
  //     {
  //       num: '',
  //       title: '',
  //       type: 1,
  //     },
  //   ];
  //   let num = 7;
  //   updatedpersonRequests.forEach((item) => {
  //     if (item.type === 1) {
  //       item.num = (num++).toString();
  //     }
  //   });
  //   if (formData.personRequests.length >= 20) {
  //     Message.error('最多添加20个完善信息条目');
  //     return;
  //   }
  //   setData({ personRequests: [...updatedpersonRequests] });
  // };
  // const changeUserInput = (val, index) => {
  //   const personRequests = JSON.parse(JSON.stringify(formData.personRequests));
  //   personRequests[index].title = val;
  //   setData({ personRequests });
  // };
  const handleCheckboxChange = (selectedValues) => {
    if (formData.personRequests.length >= 20) {
      if (selectedValues.length > selectedOptions.length) {
        Message.error('最多添加20个完善信息条目');
        return;
      }
    }
    setSelectedOptions(selectedValues);
    console.log(selectedValues, 'selectedValues=====');
    // selectedValues.forEach((item) => {
    //   const opt = {
    //     personName: item
    //   };
    //   updatedpersonRequests.push(opt);
    // });
    const updatedpersonRequests = [
      ...selectedValues.map((item) => {
        // const opt = options.find((option) => option.title === item);
        // console.log(opt, 'opt==========');
        return {
          personName: item,
        };
        // return opt;
      }),
      ...formData.personRequests.filter((item) => item.type === 1),
    ];
    console.log(updatedpersonRequests, 'updatedpersonRequests===========');
    setData({ personRequests: [...updatedpersonRequests] });
  };
  // // 删除自定义输入框
  // const deleteCustomInput = (index) => () => {
  //   if (activityEditDisabled()) {
  //     Message.notice('进行中的活动不允许修改信息条目');
  //     return;
  //   }
  //   // 从formData.personRequests中删除
  //   const personRequests = JSON.parse(JSON.stringify(formData.personRequests));
  //   personRequests.splice(index, 1);
  //   let num = 7;
  //   personRequests.forEach((item) => {
  //     if (item.type === 1) {
  //       item.num = (num++).toString();
  //     }
  //   });
  //   setData({ personRequests });
  // };
  // 通过Ref触发表单校验
  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: any = null;

      field.validate((errors): void => {
        err = errors;
      });
      return validateActivityThreshold(formData, err, field);
    },
  }));
  useEffect(() => {
    const newSelectedOptions: any[] = [];
    const newUserInputs: any[] = [];
    formData.personRequests.forEach((item: any) => {
      if (!item.disabled) {
        newSelectedOptions.push(item.personName);
      } else {
        newUserInputs.push(item.personName);
      }
    });
    setSelectedOptions(newSelectedOptions);
  }, []);
  return (
    <div>
      <LzPanel title="完善信息设置">
        <Form {...formItemLayout} field={field} disabled={activityEditDisabled()}>
          <FormItem name="options">
            <Row>
              <Col span="2" />
              <Col span="20">
                <Checkbox.Group onChange={handleCheckboxChange} value={selectedOptions}>
                  {options.map((option: any, index) => (
                    <Checkbox
                      key={index}
                      value={option.personName}
                      disabled={activityEditDisabled() || option.disabled}
                    >
                      {option.personName}
                    </Checkbox>
                  ))}
                </Checkbox.Group>
                {/* <Button type="primary" onClick={handleAddCustomInput}> */}
                {/*  +自定义 */}
                {/* </Button> */}
              </Col>
            </Row>
            {/* <div className={styles.red}>说明：最多添加20个完善信息条目</div> */}
          </FormItem>
          {/* {formData.personRequests.map((item, index) => ( */}
          {/*  <div key={index} className={styles.itemRow}> */}
          {/*    <div className={styles.num}>{index + 1}：</div> */}
          {/*    <div> */}
          {/*      <Form.Item */}
          {/*        disabled={!item.type} */}
          {/*        label={item.type ? `自定义` : item.title} */}
          {/*        required */}
          {/*        requiredMessage="请输入搜集信息标题" */}
          {/*        labelCol={{ span: 4 }} */}
          {/*        wrapperCol={{ span: 13 }} */}
          {/*      > */}
          {/*        <Input */}
          {/*          value={item.title} */}
          {/*          label="题目标题" */}
          {/*          placeholder="请输入搜集信息标题" */}
          {/*          maxLength={8} */}
          {/*          showLimitHint */}
          {/*          style={{ width: '300px' }} */}
          {/*          disabled={activityEditDisabled()} */}
          {/*          name={`allInfo${index}`} */}
          {/*          onChange={(val) => changeUserInput(val, index)} */}
          {/*        /> */}
          {/*      </Form.Item> */}
          {/*    </div> */}
          {/*    <div> */}
          {/*      {!!item.type && <Icon className={styles.delete} type="ashbin" onClick={deleteCustomInput(index)} />} */}
          {/*    </div> */}
          {/*  </div> */}
          {/* ))} */}
        </Form>
      </LzPanel>
    </div>
  );
};
