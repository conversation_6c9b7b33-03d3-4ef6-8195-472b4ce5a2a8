/**
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * Date: 2023-06-08 14:58
 * Description:
 */
import React, { useReducer, useState, useEffect, useImperativeHandle } from 'react';
import { Form, Field, Table, Button, Dialog } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import LzDialog from '@/components/LzDialog';
import ChoosePrize from '@/components/ChoosePrize';
import { activityEditDisabled, deepCopy } from '@/utils';
import { FormLayout, PageData, PRIZE_INFO, PRIZE_TYPE } from '../../../util';

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}

const FormItem = Form.Item;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

export default ({ sRef, onChange, defaultValue, value }: Props) => {
  // 使用类型化的 useReducer
  const [formData, setFormData] = useReducer<React.Reducer<PageData, PageData>>((prevState, action) => {
    return { ...prevState, ...action };
  }, value || defaultValue);
  const field: Field = Field.useField();

  // 选择奖品
  const [visible, setVisible] = useState(false);
  // 当前编辑行数据
  const [editValue, setEditValue] = useState(null);
  const [prizeIndex, setPrizeIndex] = useState(-1); // 系列下标
  // 同步/更新数据
  const setData = (data: PageData): void => {
    setFormData(data);
    // 使用深拷贝确保所有嵌套属性都被正确更新
    onChange(JSON.parse(JSON.stringify(data)));
  };
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);

  useEffect((): void => {
    if (formData.prizeList.length <= 0) {
      formData.prizeList.push({ ...deepCopy(PRIZE_INFO), specs: 1, sortId: 1 });
      setData(formData);
    }
  }, []);

  useImperativeHandle(sRef, (): { submit: () => void } => ({
    submit: () => {
      let err: object | null = null;
      field.validate((errors): void => {
        err = errors;
      });
      return err;
    },
  }));
  const onCancel = (): void => {
    setVisible(false);
  };
  const onPrizeChange = (data: any): boolean | void => {
    // 更新奖品数据
    formData.prizeList[prizeIndex] = {
      ...data,
      sortId: prizeIndex + 1,
    };
    setData(formData);
    setVisible(false);
  };
  return (
    <div>
      <LzPanel title="奖品配置">
        <Form {...formItemLayout} field={field}>
          <FormItem label="奖品信息" required>
            <Table dataSource={formData.prizeList} style={{ marginTop: '15px' }}>
              <Table.Column title="奖项名称" dataIndex="prizeName" />
              <Table.Column
                title="奖项类型"
                cell={(_, index, row) => <div>{PRIZE_TYPE[row.prizeType]}</div>}
                dataIndex="prizeType"
              />
              <Table.Column
                title="单位数量"
                cell={(_, index, row) => {
                  if (row.prizeType === 1) {
                    return <div>{row.numPerSending ? `${row.numPerSending}张` : ''}</div>;
                  } else {
                    return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
                  }
                }}
              />
              <Table.Column
                title="发放份数"
                cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>}
              />
              <Table.Column
                title="单份价值(元)"
                cell={(_, index, row) => (
                  <div>
                    {PRIZE_TYPE[row.prizeType] && row.unitPrice === 0
                      ? row.unitPrice
                      : row.unitPrice
                      ? Number(row.unitPrice).toFixed(2)
                      : ''}
                  </div>
                )}
              />
              <Table.Column
                title="奖品图"
                cell={(_, index, row) => <img style={{ width: '30px' }} src={row.prizeImg} alt="" />}
              />
              {!activityEditDisabled() && (
                <Table.Column
                  title="操作"
                  width={130}
                  cell={(val, index, row) => (
                    <div>
                      <Button
                        text
                        type="primary"
                        onClick={() => {
                          setEditValue(row.prizeType && row.prizeType > 0 ? row : null);
                          setPrizeIndex(index);
                          setVisible(true);
                        }}
                      >
                        <i className={`iconfont icon-chaojiyingxiaoicon-25`} />
                      </Button>
                      {row.prizeType > 0 && (
                        <Button
                          text
                          type="primary"
                          onClick={() => {
                            // console.log(index0, 'index0=====');
                            Dialog.confirm({
                              v2: true,
                              title: '提示',
                              centered: true,
                              content: '确认清空该奖品？',
                              onOk: () => {
                                formData.prizeList.splice(index, 1, { ...PRIZE_INFO, specs: 1 });
                                setData(formData);
                              },
                              onCancel: () => console.log('cancel'),
                            } as any);
                          }}
                        >
                          <i className={`iconfont icon-shanchu`} />
                        </Button>
                      )}
                    </div>
                  )}
                />
              )}
            </Table>
          </FormItem>
        </Form>
      </LzPanel>

      <LzDialog
        title={false}
        visible={visible}
        footer={false}
        onClose={() => setVisible(false)}
        style={{ width: '670px' }}
      >
        <ChoosePrize
          typeList={[4]}
          defaultTarget={4}
          formData={formData}
          editValue={editValue}
          onChange={onPrizeChange}
          onCancel={onCancel}
          hasLimit={false}
          hasProbability={false}
        />
      </LzDialog>
    </div>
  );
};
