import React, { useState, useEffect, useRef } from "react";
// @ts-ignore
import styles from "./style.module.scss";
import LzSteps from "@/components/LzSteps";
// 设置模版
import Customize from "./customize";
// 活动设置
import Setting from "./setting";
// 活动完成
import Complete from "./complete";
// 风险控制组件
import LzRiskConfirm from "@/components/LzRiskConfirm";
import { Button, Loading, Message } from "@alifd/next";
import {
  INIT_PAGE_DATA,
  DEFAULT_CUSTOM_VALUE,
  STEPS,
  CustomValue,
  PageData
  // checkActivityData,
} from "./util";
import { getActivityInfoAllOrigin } from "@/api/common";
import { createActivity, updateActivity } from "@/api/v99211";
import { getParams, getActivityParams, deepCopy } from "@/utils";

export default (props) => {
  const { history } = props;
  const [loading, setLoading] = useState<boolean>(false);
  const [riskLoading, setRiskLoading] = useState<boolean>(false);
  // 步骤
  const [step, setStep] = useState<number>(1);
  // 装修后的数据
  // @ts-ignore
  const [customInfo, setCustomInfo] = useState<Required<CustomValue> | undefined>();
  // 装修默认数据
  const [defaultCustomInfo, setDefaultCustomInfo] = useState<Required<CustomValue>>(deepCopy(DEFAULT_CUSTOM_VALUE));
  // 风险控制dialog
  const [risk, setRisk] = useState<boolean>(false);
  // 活动设置数据
  const [activityInfo, setActivityInfo] = useState<Required<any> | undefined>();
  // 活动默认数据
  const [defaultActivityInfo, setDefaultActivityInfo] = useState<Required<PageData>>(deepCopy(INIT_PAGE_DATA()));
  // iframe DOM
  const iframeRef = useRef<HTMLIFrameElement | null>(null);
  // 活动设置模块Ref 用于校验各组件必填
  const customizeRef = useRef<{ submit: () => void | null }>(null);
  const settingRef = useRef<{ submit: () => void | null }>(null);
  // 获取活动类型 & 模板ID
  const [activityType, code] = getActivityParams();
  // 创建好的活动id
  const [activityId, setActivityId] = useState<string>("");
  // 当前操作类型
  const operationType: string = getParams("type");

  /**
   * 向c端发送消息
   */
  const sendMessage = (formData, type): void => {
    const iWindow = iframeRef.current?.contentWindow;
    // console.log(`📧📧📧向c端发送消息  ${new Date()}`, type);
    // console.log(formData);
    iWindow?.postMessage(
      {
        from: "B",
        type,
        event: "update",
        data: formData
      },
      "*"
    );
  };

  /**
   * 初始化获取活动数据, 发送数据
   */
  const fetchActivityData = (): void => {
    setLoading(true);
    getActivityInfoAllOrigin({ id: getParams("id"), type: operationType }).then((res) => {
      const customData = JSON.parse(res.decoData as string);
      console.log(customData,'customData');
      if (res.decoData) {
        setDefaultCustomInfo(customData);
        setCustomInfo(deepCopy(customData));
        // 发送装修数据
        sendMessage(customData, "deco");
      }
      if (res.activityData) {
        const activityData = JSON.parse(res.activityData as string);
        if (operationType !== "tpl") {
          customData!.cmdImg = activityData.cmdImg || customData!.cmdImg;
          customData!.h5Img = activityData.h5Img || customData!.h5Img;
          customData!.mpImg = activityData.mpImg || customData!.mpImg;
          setDefaultCustomInfo(customData!);
        }
        setDefaultActivityInfo(deepCopy(activityData));
        setActivityInfo(activityData);
        // 发送活动设置数据
        sendMessage(activityData, "activity");
      } else {
        // @ts-ignore
        Object.keys(defaultActivityInfo.moduleInfo?.pushAllowed).forEach((key) => {
          // @ts-ignore
          defaultActivityInfo.moduleInfo.pushAllowed[key] = 1;
        });
        setDefaultActivityInfo(defaultActivityInfo);
        setActivityInfo(deepCopy(defaultActivityInfo));
      }
      setLoading(false);
    });
  };

  const handleCustomizeChange = (formData): void => {
    console.log(formData, "装修数据更新");
    setCustomInfo(formData);
    sendMessage(formData, "deco");
  };
  /**
   * 活动设置内容
   * @param data 活动配置数据
   * @param type 用于c端弹出任务列表
   */
  const handleSettingChange = (data, type?: string): void => {
    console.log(data, "活动数据更新");
    setActivityInfo(data);
  };

  /**
   * 风险提示确认回调
   */
  const handleRiskConfirm = (): void => {
    setRiskLoading(true);
    const isEdit: boolean = operationType === "edit";
    const events: [Function, Function] = [createActivity, updateActivity];
    events[+isEdit]({
      activityData: { ...(activityInfo || defaultActivityInfo), templateCode: code, activityId: getParams("id") },
      decoData: JSON.stringify(customInfo || defaultCustomInfo)
    })
      .then((res: { activityId: string }): void => {
        setActivityId(res.activityId);
        setRisk(false);
        setRiskLoading(false);
        Message.success(`活动${isEdit ? "编辑" : "创建"}成功`);
        setStep(step + 1);
      })
      .catch((err) => {
        setRiskLoading(false);
        Message.error(err.message);
      });
  };

  // 初始化监听/销毁postMessage信息
  useEffect(() => {
    fetchActivityData();
  }, []);

  // 设置模板按钮
  const StepOneBtn = () => (
    <div className="crm-footer">
      {operationType !== "edit" && (
        <Button onClick={() => history.push(`/select?activityType=${activityType}`)}>重新选择模板</Button>
      )}
      <Button
        type="primary"
        onClick={() => {
          // @ts-ignore
          activityInfo!.moduleInfo.selectedModule = "kvModule";
          customizeRef.current!.submit();
        }}
        disabled={loading}
      >
        下一步
      </Button>
    </div>
  );
  // 活动设置按钮
  const StepTwoBtn = () => (
    <div className="crm-footer">
      <Button onClick={() => setStep(step - 1)}>上一步</Button>
      <Button type="primary"
              onClick={
                () => {
                  const data: any = settingRef.current!.submit();
                  console.log(data, "模块信息");
                  console.log(activityInfo, "基础信息");
                  console.log(customInfo || defaultCustomInfo, "装修数据");
                  for (const [key, value] of Object.entries(data.resultPushAllowed)) {
                    // @ts-ignore
                    const module = activityInfo?.moduleInfo.modules?.[key];
                    if (value as any > 0) {
                      Message.error(`请先保存${module.name}模块再创建`);
                      return;
                    }
                  }
                  const filteredModules = Object.entries(activityInfo?.moduleInfo.modules ?? {}).reduce((acc, [key, value]) => {
                    if (key === 'comSortList') {
                      acc[key] = value;
                      return acc;
                    }
                    if (data.resultPushAllowed.hasOwnProperty(key)) {
                      acc[key] = value;
                    }
                    return acc;
                  }, {} as Record<string, any>);

                  if (activityInfo && activityInfo.moduleInfo) {
                    activityInfo.moduleInfo.modules = filteredModules;
                  }
                  setRisk(true);
                }}
      >
        {operationType !== "edit" ? "创建" : "编辑"}
      </Button>
    </div>
  );

  // 渲染步骤按钮
  const renderStep = () => {
    const stepMap = {
      1: <StepOneBtn />,
      2: <StepTwoBtn />
    };
    return stepMap[step];
  };
  return (
    <div className="crm-container">
      <Loading visible={loading} inline={false}>
        <LzSteps steps={STEPS} current={step} />
        <div className={styles.container}>
          <div className={styles.operation}>
            {step === 1 && (
              <Customize
                sRef={customizeRef}
                onSettingChange={handleSettingChange}
                defaultValue={defaultActivityInfo}
                value={activityInfo}
                decoValue={customInfo}
                decoDefaultValue={defaultCustomInfo}
                handleChange={handleCustomizeChange}
                onSubmit={(err) => {
                  if (!err) {
                    setStep(step + 1);
                  }
                }}
              />
            )}
            {step === 2 && (
              <Setting
                defaultValue={defaultActivityInfo}
                value={activityInfo}
                sRef={settingRef}
                decoValue={customInfo}
                onSettingChange={handleSettingChange}
              />
            )}
            {step === 3 && <Complete history={history} activityId={activityId} />}
          </div>
        </div>
        {renderStep()}
      </Loading>
      <LzRiskConfirm
        loading={riskLoading}
        riskVisible={risk}
        onClose={() => setRisk(false)}
        onSubmit={handleRiskConfirm}
      />
    </div>
  );
};
