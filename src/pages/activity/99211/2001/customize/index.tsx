import React, { useImperative<PERSON><PERSON><PERSON>, useRef, useReducer } from "react";
// @ts-ignore
import styles from './style.module.scss';
// 基础信息
import BaseInfo from './components/Base';
// 分享设置
// import ShareInfo from './components/Share';
// 活动规则
import RulesInfo from './components/Rules';
import { PageData, CustomValue } from '../util';
import { Tab } from "@alifd/next";
import DecorateSettings from "./components/DecorateSettings";


interface Props {
  onSettingChange: (activityInfo: PageData, type) => void;
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  decoValue: CustomValue | undefined;
  onSubmit: (resultListLength: number) => void;
}

interface SettingProps extends Pick<Props, 'defaultValue' | 'value'> {
  onChange: (formData: Required<any>) => void;
}
interface EventProps extends Pick<Props, 'decoValue' | 'value'> {
  onChangeDecorate: (formData: Required<CustomValue>) => void;
  decoDefaultValue: CustomValue | undefined;
}
export default ({ onSettingChange, defaultValue, value, sRef, onSubmit, decoValue,handleChange,decoDefaultValue }) => {
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  console.log(formData);
  const [popupActiveKey, setPopupActiveKey] = React.useState('index');
  const handleTabChange = (key: string): void => {
    setPopupActiveKey(key);
  };
  const onChange = (activityInfo, type?: string): void => {
    setFormData(activityInfo);
    onSettingChange(activityInfo, type);
  };
  const onChangeDecorate = (formData): void => {
    handleChange(formData);
  };
  const settingProps: SettingProps = {
    onChange,
    defaultValue,
    value,
  };
  const eventProps: EventProps = {
    decoValue,
    value,
    onChangeDecorate,
    decoDefaultValue
  };
  // 各Form Ref
  const baseRef = useRef<{ submit: () => void | null }>(null);
  const rulesRef = useRef<{ submit: () => void | null }>(null);
  // const shareRef = useRef<{ submit: () => void | null }>(null);
  const checkForm = () => {
    const events: any[] = [
      baseRef.current!,
      rulesRef.current,
      // shareRef.current,
    ];
    return events.every((item, index: number): boolean => {
      const result = events[index].submit();
      if (result) {
        return false;
      }
      return true;
    });
  };

  // 传递Ref
  useImperativeHandle(sRef, (): { submit: () => void } => ({
    submit: (): void => {
      // 异常结果列表
      const resultList: unknown[] = [];
      const events: any[] = [
        baseRef.current!,
        rulesRef.current,
        // shareRef.current,
      ];
      // submit只会抛出异常，未有异常返回null
      events.every((item, index: number): boolean => {
        console.log('events', events, index);
        const result = events[index].submit();
        console.log('console.log(result);', result);
        if (result) {
          resultList.push(result);
          return false;
        }
        return true;
      });
      onSubmit(resultList.length);
    },
  }));

  return (
    <div className={styles.setting}>
      <Tab activeKey={popupActiveKey} defaultActiveKey="index" onChange={handleTabChange}>
        <Tab.Item title="活动信息" key="index">
          <BaseInfo sRef={baseRef} {...settingProps} />
          <RulesInfo sRef={rulesRef} {...settingProps} checkForm={checkForm} />
          {/*<ShareInfo sRef={shareRef} {...settingProps} decoValue={decoValue} />*/}
        </Tab.Item>
        <Tab.Item title="弹窗装修配置" key="dialog">
          <DecorateSettings {...eventProps}  />
        </Tab.Item>
      </Tab>
    </div>
  );
};
