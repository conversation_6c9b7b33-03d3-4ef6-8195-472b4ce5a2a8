import React, { useReducer, useImperative<PERSON>andle, useEffect } from "react";
import LzPanel from '@/components/LzPanel';
import { Form, Input, DatePicker2, Field, Radio, Checkbox } from "@alifd/next";
import dayjs from 'dayjs';
import format from '@/utils/format';
import constant from '@/utils/constant';
import { activityEditDisabled, calcDateDiff, validateActivityThreshold } from '@/utils';
import { FormLayout, PageData,moduleOptions } from '../../../util';
import { SortableContainer, SortableElement, arrayMove } from 'react-sortable-hoc';
const FormItem = Form.Item;
const RadioGroup = Radio.Group;
import styles from './style.module.scss';

const dateFormat: string = constant.DATE_FORMAT_TEMPLATE;
const { RangePicker } = DatePicker2;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};
interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}

const SortableItem = SortableElement(({ value, index }) => {
  const option = moduleOptions.find(opt => opt.des === value);
  return (
    <div className={styles.sortItem}>
      {option?.title}
    </div>
  );
});

const SortableList = SortableContainer(({ items }) => {
  return (
    <div>
      {items.map((value, index) => (
        <SortableItem
          key={`item-${value}`}
          index={index}
          value={value}
          disabled={index === 0 || activityEditDisabled()}  // 禁用第一个元素的拖拽
          collection={0}  // 确保所有项目在同一个collection中
        />
      ))}
    </div>
  );
});

export default ({ onChange, defaultValue, value, sRef }: Props) => {
  // @ts-ignore
  const field = Field.useField({ values: value || defaultValue });
  // 同装修收集数据
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  // 同步活动设置数据
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  // 日期改变，处理提交数据
  const onDataRangeChange = (rangeDate): void => {
    setData({
      rangeDate,
      startTime: format.formatDateTimeDayjs(rangeDate[0]),
      endTime: format.formatDateTimeDayjs(rangeDate[1]),
    });
  };
  // 获取持续时间
  const DateRangeDuration = ({ rangeDate }) => {
    const duration: string = calcDateDiff(rangeDate);
    return (
      <span style={{ fontSize: '12px', marginLeft: '5px' }}>
        {
          <span>
            活动持续<span style={{ color: 'red' }}>{duration || '不足1小时'}</span>
          </span>
        }
      </span>
    );
  };

  const handleCheckboxChange = (selectedValues) => {
    setData({ selectedOptions: [...selectedValues] });
  };

  // 修改onSortEnd函数
  const onSortEnd = ({ oldIndex, newIndex }) => {
    // 如果目标索引是 0，则不允许排序
    if (newIndex === 0) {
      return;
    }

    if (oldIndex !== newIndex) {
      const newSelectedOptions = arrayMove(formData.selectedOptions, oldIndex, newIndex);
      setData({ selectedOptions: newSelectedOptions });
    }
  };

  // 通过Ref触发表单校验
  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: any = null;

      field.validate((errors: any): void => {
        err = errors;
      });
      return validateActivityThreshold(formData, err, field);
    },
  }));
  return (
    <div>
      <LzPanel title="活动基本信息">
        <Form {...formItemLayout} field={field} disabled={activityEditDisabled()}>
          <FormItem label="活动名称" required requiredMessage="请输入活动名称" disabled={false}>
            <Input
              value={formData.activityName}
              placeholder="请输入活动名称"
              name="activityName"
              maxLength={20}
              showLimitHint
              className="w-300"
              onChange={(activityName) => setData({ activityName })}
            />
          </FormItem>
          <FormItem
            label="活动时间"
            required
            requiredMessage="请选择活动时间"
            extra={<div className="next-form-item-help">注：活动时间需设置在软件的订购有效期内</div>}
          >
            <RangePicker
              className="w-300"
              name="rangeDate"
              inputReadOnly
              format={dateFormat}
              hasClear={false}
              showTime
              value={formData.rangeDate || [new Date(formData.startTime), new Date(formData.endTime)]}
              onChange={onDataRangeChange}
              disabledDate={(date) => {
                return date.valueOf() < dayjs().subtract(1, 'day').valueOf();
              }}
            />
            <DateRangeDuration
              rangeDate={formData.rangeDate || [new Date(formData.startTime), new Date(formData.endTime)]}
            />
          </FormItem>
          <FormItem label="活动门槛" required>
            <RadioGroup
              value={formData.threshold}
              onChange={(val) => {
                setData({
                  threshold: val,
                });
              }}
            >
              {/*<Radio value={0}>无门槛</Radio>*/}
              <Radio value={1}>店铺会员</Radio>
            </RadioGroup>
          </FormItem>
          <FormItem label="活动模块配置" required>
            <Checkbox.Group onChange={handleCheckboxChange} value={formData.selectedOptions}>
              {moduleOptions.map((option) => (
                <Checkbox key={option.des} value={option.des} disabled={option.des === "kvModule" || activityEditDisabled()}>
                  {option.title}
                </Checkbox>
              ))}
            </Checkbox.Group>
          </FormItem>
          {
            formData.selectedOptions.length > 0 &&
            <FormItem label="活动模块排序">
              <SortableList
                items={formData.selectedOptions}
                onSortEnd={onSortEnd}
                axis="y"
                lockAxis="y"
                helperClass={styles.dragHelper}
                useDragHandle={false}
              />
            </FormItem>
          }
        </Form>
      </LzPanel>
    </div>
  );
};
