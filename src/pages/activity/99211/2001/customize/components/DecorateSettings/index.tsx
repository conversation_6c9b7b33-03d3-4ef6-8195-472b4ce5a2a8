import React, { useReducer, useEffect } from 'react';
import styles from './style.module.scss';
import { Form, Button, Grid } from '@alifd/next';
import LzImageSelector from '@/components/LzImageSelector';
import {  FormLayout } from '../../../util';
import LzPanel from '@/components/LzPanel';
import LzColorPicker from "@/components/LzColorPicker";


const formLayout: Omit<FormLayout, 'wrapperCol' | 'labelAlign'> = {
  labelCol: {
    fixedSpan: 6,
  },
  colon: true,
};

export default ({ decoValue, decoDefaultValue, onChangeDecorate }) => {
  // 装修数据 || 初始数据
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  },  decoValue || decoDefaultValue);

  // 更新数据，向上传递
  const setForm = (obj: any): void => {
    setFormData(obj);
    onChangeDecorate({ ...formData, ...obj });
  };
  // 用于处理装修组件重新挂载赋值问题
  // 重置接口返回默认值
  useEffect((): void => {
    setFormData( decoValue || decoDefaultValue);
  }, [ decoValue || decoDefaultValue]);

  return (
    <div className={styles.base}>
      <LzPanel title="弹窗背景">
        <Form {...formLayout} className={styles.form}>
          <Form.Item label="图片">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={600}
                  height={678}
                  value={formData.dialogBg}
                  onChange={(dialogBg: any) => {
                    setForm({ dialogBg });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：600px*678px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({
                        dialogBg: decoValue?.dialogBg,
                      });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
        </Form>
      </LzPanel>
      <LzPanel title="弹窗内颜色配置">
        <Form {...formLayout} className={styles.form}>
          <Form.Item label="页面背景色">
            <LzColorPicker
              value={formData.actBgColor}
              onChange={(actBgColor: any) => setForm({ actBgColor })}
            />
            <Button
              type="primary"
              text
              onClick={() => {
                setForm({ actBgColor: decoValue?.actBgColor });
              }}
            >
              重置
            </Button>
          </Form.Item>
          <Form.Item label="标题文案颜色">
            <LzColorPicker
              value={formData.titleTextColor}
              onChange={(titleTextColor: any) => setForm({ titleTextColor })}
            />
            <Button
              type="primary"
              text
              onClick={() => {
                setForm({ titleTextColor: decoValue?.titleTextColor });
              }}
            >
              重置
            </Button>
          </Form.Item>
          <Form.Item label="标题文案颜色">
            <LzColorPicker
              value={formData.titleTextColor}
              onChange={(titleTextColor: any) => setForm({ titleTextColor })}
            />
            <Button
              type="primary"
              text
              onClick={() => {
                setForm({ titleTextColor: decoValue?.titleTextColor });
              }}
            >
              重置
            </Button>
          </Form.Item>
          <Form.Item label="其他文字颜色">
            <LzColorPicker
              value={formData.otherTextColor}
              onChange={(otherTextColor: any) => setForm({ otherTextColor })}
            />
            <Button
              type="primary"
              text
              onClick={() => {
                setForm({ otherTextColor: decoValue?.otherTextColor });
              }}
            >
              重置
            </Button>
          </Form.Item>
          <Form.Item label="高亮文字颜色">
            <LzColorPicker
              value={formData.highlightTextColor}
              onChange={(highlightTextColor: any) => setForm({ highlightTextColor })}
            />
            <Button
              type="primary"
              text
              onClick={() => {
                setForm({ highlightTextColor: decoValue?.highlightTextColor });
              }}
            >
              重置
            </Button>
          </Form.Item>
          <Form.Item label="按钮文字颜色">
            <LzColorPicker
              value={formData.btnTextColor}
              onChange={(btnTextColor: any) => setForm({ btnTextColor })}
            />
            <Button
              type="primary"
              text
              onClick={() => {
                setForm({ btnTextColor: decoValue?.btnTextColor });
              }}
            >
              重置
            </Button>
          </Form.Item>
          <Form.Item label="按钮背景颜色">
            <LzColorPicker
              value={formData.btnBgColor}
              onChange={(btnBgColor: any) => setForm({ btnBgColor })}
            />
            <Button
              type="primary"
              text
              onClick={() => {
                setForm({ btnBgColor: decoValue?.btnBgColor });
              }}
            >
              重置
            </Button>
          </Form.Item>
          <Form.Item label="高亮按钮文字颜色">
            <LzColorPicker
              value={formData.highlightBtnTextColor}
              onChange={(highlightBtnTextColor: any) => setForm({ highlightBtnTextColor })}
            />
            <Button
              type="primary"
              text
              onClick={() => {
                setForm({ highlightBtnTextColor: decoValue?.highlightBtnTextColor });
              }}
            >
              重置
            </Button>
          </Form.Item>
          <Form.Item label="高亮按钮背景颜色">
            <LzColorPicker
              value={formData.highlightBtnBgColor}
              onChange={(highlightBtnBgColor: any) => setForm({ highlightBtnBgColor })}
            />
            <Button
              type="primary"
              text
              onClick={() => {
                setForm({ highlightBtnBgColor: decoValue?.highlightBtnBgColor });
              }}
            >
              重置
            </Button>
          </Form.Item>
        </Form>
      </LzPanel>
    </div>
  );
};
