import { deepCopy } from '@/utils';

function updateModule(modules, selectedModule, payload) {
  if (Array.isArray(modules[selectedModule])) {
    return {
      ...modules,
      [selectedModule]: payload,
    };
  } else {
    return {
      ...modules,
      [selectedModule]: {
        ...modules[selectedModule],
        ...payload,
      },
    };
  }
}
function updatePushAllowed(pushAllowed, selectedModule) {
  return {
    ...pushAllowed,
    [selectedModule]: pushAllowed[selectedModule] + 1,
  };
}
function resetPushAllowed(pushAllowed, selectedModule) {
  return {
    ...pushAllowed,
    [selectedModule]: 0,
  };
}

export function decorateReducer(state, action) {
  switch (action.type) {
    case 'SELECT_MODULE':
      return { ...state, selectedModule: action.payload };
    case 'UPDATE_MODULE':
      return {
        ...state,
        modules: updateModule(state.modules, state.selectedModule, action.payload),
        pushAllowed: action.isUpdated ? updatePushAllowed(state.pushAllowed, state.selectedModule) : state.pushAllowed,
      };
    case 'RESET_PUSH': {
      return {
        ...state,
        pushAllowed: resetPushAllowed(state.pushAllowed, state.selectedModule),
        defaultModule: {
          ...state.defaultModule,
          [state.selectedModule]: deepCopy(state.modules[state.selectedModule]),
        },
      };
    }
    case 'INIT_MODULE': {
      const defaultModule = deepCopy({ ...state.modules, ...action.payload });
      return {
        ...state,
        modules: {
          ...state.modules,
          ...action.payload,
        },
        defaultModule,
      };
    }
    // case 'MOVE_UP': {
    //   const { payload } = action;
    //   const index = state.modules.comSortList.findIndex((k) => k === payload);
    //   if (index > 0) {
    //     const temp = state.modules.comSortList[index - 1];
    //     state.modules.comSortList[index - 1] = payload;
    //     state.modules.comSortList[index] = temp;
    //   }
    //   return {
    //     ...state,
    //   };
    // }
    // case 'MOVE_DOWN': {
    //   const { payload } = action;
    //   const index = state.modules.comSortList.findIndex((k) => k === payload);
    //   if (index < state.modules.comSortList.length - 1) {
    //     const temp = state.modules.comSortList[index + 1];
    //     state.modules.comSortList[index + 1] = payload;
    //     state.modules.comSortList[index] = temp;
    //   }
    //   return {
    //     ...state,
    //   };
    // }
    default:
      return {
        ...state,
      };
  }
}
