export const urlRegExp = new RegExp(/^(http:\/\/|https:\/\/)(.*\.)?(isvjcloud\.com|isvjd\.com|jd\.com)(\/.*)?(\?.*)?$/);

export const urlRegularCheck = (rule: any, value: any, callback: any) => {
  if (value && !urlRegExp.test(value)) {
    callback(`链接只能以https://开头且应包含.isvjcloud.com、.isvjd.com或.jd.com`);
  } else {
    callback();
  }
};
export interface FormLayout {
  labelCol: {
    span?: number;
    fixedSpan?: number;
  };
  wrapperCol: {
    span: number;
  };
  labelAlign: 'left' | 'top';
  colon: boolean;
}
interface PrizeType {
  [key: number]: string;
}
export const PRIZE_TYPE: PrizeType = {
  12: '京元宝权益',
  2: '京豆',
  1: '优惠券',
  3: '实物',
  4: '积分',
  6: '红包',
  7: '礼品卡',
  8: '京东E卡',
  9: 'PLUS会员卡',
  10: '爱奇艺会员卡',
};
export interface PrizeInfo {
  prizeName: string;
  prizeImg: string;
  prizeType: number;
  probability: number | string;
  unitCount: number;
  unitPrice: number;
  sendTotalCount: number;
  dayLimit: number;
  dayLimitType: number;
  ifPlan: number;
  startTime?: string;
  endTime?: string;
  startDate?: string;
  endDate?: string;
  planId?: string;
  quantityRemain?: number;
  planStatus?: number;
}
