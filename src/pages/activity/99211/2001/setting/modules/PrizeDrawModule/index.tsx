import React, { useState } from "react";
import {
  Card,
  Button,
  Divider,
  Loading,
  Message,
  Table,
  Tab,
  NumberPicker,
  Form,
  Radio,
  Grid,
  Dialog, Input
} from "@alifd/next";
import styles from "./index.module.scss";
import LzImageSelector from "@/components/LzImageSelector";
import LzColorPicker from "@/components/LzColorPicker";
import LzDialog from "@/components/LzDialog";
import ChoosePrizeForDZ from "@/components/ChoosePrizeForDZ";
import { PRIZE_INFO, PRIZE_TYPE } from "../../../util";
import { activityEditDisabled, deepCopy } from "@/utils";

const FormItem = Form.Item;
const RadioGroup = Radio.Group;
const { Row, Col } = Grid;
// 封装答案单元格组件
export default ({ data, dispatch, formData }) => {
  const [loading, setLoading] = useState(false);
  // 当前编辑行数据
  const [editValue, setEditValue] = useState(null);
  // 当前编辑行index
  const [target, setTarget] = useState(0);
  const [visible, setVisible] = React.useState(false);
  const saveSetting = (): any => {
    if (!data.prizeList.length) {
      Message.error("请配置抽奖礼品");
      return;
    }
    if (!data.ruleText) {
      Message.error("请填写活动规则");
      return;
    }
    if (!data.energyValue) {
      Message.error("请填写能量值");
      return;
    }
    if (data.winLotteryDayType == 2 && !data.winLotteryDayValue) {
      Message.error("请填写中奖天数");
      return;
    }
    if (data.winLotteryTotalType == 2 && !data.winLotteryTotalValue) {
      Message.error("请填写中奖总次数");
      return;
    }
    setLoading(true);
    Message.success(`${data.name}模块保存成功`);
    dispatch({ type: "RESET_PUSH" });
    setLoading(false);
  };

  const setData = (value, isUpdated = true) => {
    dispatch({ type: "UPDATE_MODULE", payload: value, isUpdated });
  };
  const onPrizeChange = (val): boolean | void => {
    data.prizeList[target] = { ...val };
    setData(data);
    setVisible(false);
  };
  const commonProps = {
    title: data.name,
    extra: (
      <div>
        <Button type="primary" onClick={saveSetting}>
          保存当前设置
        </Button>
      </div>
    )
  };
  return (
    <div>
      <Card free>
        <Card.Header {...commonProps} />
        <Divider />
        <Card.Content>
          <Loading visible={loading} style={{ width: "100%" }}>
            <div className={styles.operation}>
              <Tab>
                <Tab.Item title="页面设置" key="index0">
                  <div className={styles.memberBgContainer}>
                    <div className="crm-label">活动背景</div>
                    <div className={styles.imgUpload}>
                      <LzImageSelector
                        bgWidth={200}
                        bgHeight={140}
                        width={750}
                        height={578}
                        value={data.pageBg}
                        onChange={(pageBg) => {
                          const updatedData = { ...data };
                          updatedData.pageBg = pageBg;
                          setData(updatedData);
                        }}
                      />
                      <div className={styles.tip}>
                        <div>图片尺寸为750px*578px</div>
                        <div>图片格式：大小不超过1M图片类型为jpg、png</div>
                      </div>
                    </div>

                    <div className="crm-label">抽奖按钮</div>
                    <div className={styles.imgUpload}>
                      <LzImageSelector
                        bgWidth={200}
                        bgHeight={140}
                        width={340}
                        height={82}
                        value={data.btnBg}
                        onChange={(btnBg) => {
                          const updatedData = { ...data };
                          updatedData.btnBg = btnBg;
                          setData(updatedData);
                        }}
                      />
                      <div className={styles.tip}>
                        <div>图片尺寸为340px*82px</div>
                        <div>图片格式：大小不超过1M图片类型为jpg、png</div>
                      </div>
                    </div>
                  </div>
                  <div className={styles.colorContainer}>
                    <div className={styles.colorPicker}>
                      <div className={styles.colorPickerItem}>
                        <span>礼品名称文字颜色</span>
                        <LzColorPicker
                          value={data.giftTextColor || "#000000"}
                          onChange={(giftTextColor) => {
                            const updatedData = { ...data };
                            updatedData.giftTextColor = giftTextColor;
                            setData(updatedData);
                          }}
                        />
                      </div>

                      <div className={styles.colorPickerItem}>
                        <span>弹窗按钮文字颜色</span>
                        <LzColorPicker
                          value={data.dialogTextColor || "#000000"}
                          onChange={(dialogTextColor) => {
                            const updatedData = { ...data };
                            updatedData.dialogTextColor = dialogTextColor;
                            setData(updatedData);
                          }}
                        />
                      </div>
                      <div className={styles.colorPickerItem}>
                        <span>弹窗按钮颜色</span>
                        <LzColorPicker
                          value={data.dialogBtnColor || "#000000"}
                          onChange={(dialogBtnColor) => {
                            const updatedData = { ...data };
                            updatedData.dialogBtnColor = dialogBtnColor;
                            setData(updatedData);
                          }}
                        />
                      </div>
                    </div>
                  </div>
                </Tab.Item>
                <Tab.Item title="奖品配置" key="index1">
                  <div className={styles.MemberContainer}>
                    <div className="crm-label">奖品配置</div>
                    <Button
                      disabled={data?.prizeList?.length >= 12 || activityEditDisabled()}
                      type="primary"
                      onClick={() => {
                        data?.prizeList?.push(deepCopy({ ...PRIZE_INFO }));
                        setData(data);
                      }}
                    >
                      +添加奖品（{data.prizeList?.length}/12）
                    </Button>
                    <Table dataSource={data?.prizeList} style={{ marginTop: "15px" }}>
                      <Table.Column title="奖品名称" dataIndex="prizeName" />
                      <Table.Column
                        title="奖品类型"
                        cell={(_, index, row) => <div>{PRIZE_TYPE[row.prizeType]}</div>}
                        dataIndex="prizeType"
                      />
                      <Table.Column
                        title="单位数量"
                        cell={(_, index, row) => {
                          if (row.prizeType === 1) {
                            return <div>{row.numPerSending ? `${row.numPerSending}张` : ""}</div>;
                          } else {
                            return <div>{row.unitCount ? `${row.unitCount}份` : ""}</div>;
                          }
                        }}
                      />
                      <Table.Column
                        title="中奖概率(%)"
                        cell={(_, index, row) => <div>{row.probability ? row.probability : ""}</div>}
                      />
                      <Table.Column
                        title="发放份数"
                        cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}` : ""}</div>}
                      />
                      <Table.Column
                        title="单份价值(元)"
                        cell={(_, index, row) => (
                          <div>{row.prizeName ? (row.unitPrice ? Number(row.unitPrice).toFixed(2) : 0) : ""}</div>
                        )}
                      />
                      {
                        !activityEditDisabled() &&
                        <Table.Column
                          title="操作"
                          width={130}
                          cell={(val, index, _) => (
                            <FormItem>
                              <Button
                                text
                                type="primary"
                                onClick={() => {
                                  let row = data.prizeList[index];
                                  if (row.prizeName === "") {
                                    row = null;
                                  }
                                  setEditValue(row);
                                  setTarget(index);
                                  setVisible(true);
                                }}
                              >
                                <i className={`iconfont icon-chaojiyingxiaoicon-25`} />
                              </Button>
                              {data.prizeList.length > 1 && (
                                <Button
                                  text
                                  type="primary"
                                  disabled={activityEditDisabled()}
                                  onClick={() => {
                                    Dialog.confirm({
                                      v2: true,
                                      title: "提示",
                                      centered: true,
                                      content: "确认删除该奖品？",
                                      onOk: () => {
                                        data.prizeList.splice(index, 1);
                                        setData(data);
                                      },
                                      onCancel: () => console.log("cancel")
                                    } as any);
                                  }}
                                >
                                  <i className={`iconfont icon-shanchu`} />
                                </Button>
                              )}
                            </FormItem>
                          )}
                        />
                      }
                    </Table>
                  </div>
                  <div className={styles.MemberContainer}>
                    <div className="crm-label">概率文案配置</div>
                    <Form labelCol={{ span: 4 }}
                          wrapperCol={{ span: 20 }}
                          labelAlign="left"
                          colon>
                      <FormItem label="活动规则" required requiredMessage="请输入活动规则说明">
                        <Input.TextArea
                          value={data.ruleText}
                          name="rules"
                          onChange={(ruleText) => setData({ ruleText })}
                          autoHeight={{ minRows: 8, maxRows: 40 }}
                          placeholder="请输入活动规则说明"
                          maxLength={100}
                          showLimitHint
                          className="form-input-ctrl"
                        />
                      </FormItem>
                    </Form>
                  </div>
                </Tab.Item>
                <Tab.Item title="抽奖配置" key="index2">
                  <div className={styles.MemberContainer}>
                    <Form labelCol={{ span: 6 }}
                          wrapperCol={{ span: 18 }}
                          labelAlign="left"
                          colon
                    >
                      <FormItem label={"单次抽奖消耗能量值"} required requiredMessage={"请输入单次抽奖消耗能量值"}>
                        <NumberPicker
                          style={{ width: "150px" }}
                          value={data.energyValue}
                          onChange={(energyValue) => {
                            const updatedData = { ...data };
                            updatedData.energyValue = energyValue;
                            setData(updatedData);
                          }}
                          disabled={activityEditDisabled()}
                          type="inline"
                          min={1}
                          max={999}
                        />
                      </FormItem>
                      <FormItem label="每人每天最多中奖" required>
                        <RadioGroup
                          value={data.winLotteryDayType}
                          disabled={activityEditDisabled()}
                          onChange={(winLotteryDayType: number) => {
                            const updatedData = { ...data };
                            updatedData.winLotteryDayType = winLotteryDayType;
                            setData({ winLotteryDayType });
                          }}
                        >
                          <Radio id="1" value={1}>
                            不限制
                          </Radio>
                          <Radio id="2" value={2}>
                            限制
                          </Radio>
                        </RadioGroup>
                      </FormItem>
                      <FormItem label=" " colon={false}>
                        <Row gutter="4">
                          <Col>
                            {data.winLotteryDayType === 2 && (
                              <div className={styles.panel}>
                                <FormItem
                                  name="winLotteryDayCounts"
                                  required
                                  requiredTrigger="onBlur"
                                  requiredMessage="请输入每人每天最多中奖次数"
                                  style={{ margin: 0 }}
                                >
                                  <NumberPicker
                                    value={data.winLotteryDayCounts}
                                    disabled={activityEditDisabled()}
                                    onChange={(winLotteryDayCounts: number) => {
                                      const updatedData = { ...data };
                                      updatedData.winLotteryDayCounts = winLotteryDayCounts;
                                      setData(updatedData);
                                    }}
                                    type="inline"
                                    min={1}
                                    max={9999999}
                                    className={styles.number}
                                  />
                                  限制每天内用户最多中奖{data.winLotteryDayCounts}次
                                </FormItem>
                              </div>
                            )}
                            {data.winLotteryDayType === 1 &&
                              <div className={styles.panel}>不限制每天用户最多中奖次数</div>}
                            <div className={styles.tip}>注： 限制每人每天最多中奖次数</div>
                          </Col>
                        </Row>
                      </FormItem>
                      <FormItem label="每人累计最多中奖" required>
                        <RadioGroup
                          disabled={activityEditDisabled()}
                          value={data.winLotteryTotalType}
                          onChange={(winLotteryTotalType: number) => {
                            const updatedData = { ...data };
                            updatedData.winLotteryTotalType = winLotteryTotalType;
                            setData(updatedData);
                          }}
                        >
                          <Radio id="1" value={1}>
                            不限制
                          </Radio>
                          <Radio id="2" value={2}>
                            限制
                          </Radio>
                        </RadioGroup>
                      </FormItem>
                      <FormItem label=" " colon={false}>
                        <Row gutter="4">
                          <Col>
                            {data.winLotteryTotalType === 2 && (
                              <div className={styles.panel}>
                                <FormItem
                                  name="winLotteryTotalCounts"
                                  required
                                  requiredTrigger="onBlur"
                                  requiredMessage="请输入限制活动周期内用户最多中奖次数"
                                  style={{ margin: 0 }}
                                  disabled={activityEditDisabled()}
                                >
                                  <NumberPicker
                                    value={data.winLotteryTotalCounts}
                                    onChange={(winLotteryTotalCounts: number) => {
                                      const updatedData = { ...data };
                                      updatedData.winLotteryTotalCounts = winLotteryTotalCounts;
                                      setData(updatedData);
                                    }}
                                    type="inline"
                                    min={1}
                                    max={9999999}
                                    className={styles.number}
                                  />
                                  限制活动周期内用户最多中奖{data.winLotteryTotalCounts}次
                                </FormItem>
                              </div>
                            )}
                            {data.winLotteryTotalType === 1 && (
                              <div className={styles.panel}>不限制活动周期内用户最多中奖次数</div>
                            )}
                            <div className={styles.tip}>注： 限制活动期间内每人累计最多中奖次数</div>
                          </Col>
                        </Row>
                      </FormItem>
                    </Form>

                  </div>
                </Tab.Item>
              </Tab>
            </div>
          </Loading>
        </Card.Content>
      </Card>
      <LzDialog
        title={"选择奖项"}
        visible={visible}
        footer={false}
        onClose={() => setVisible(false)}
        style={{ width: "700px" }}
      >
        <ChoosePrizeForDZ
          defaultEditValue={data?.prizeList[target] ?? null}
          formData={formData}
          editValue={editValue}
          onChange={onPrizeChange}
          onCancel={() => {
            setVisible(false);
          }}
          hasProbability={true}
          hasLimit={false}
          hasShowTime={false}
          typeList={[2, 3]}
          defaultTarget={2}
        />
      </LzDialog>
    </div>
  );
}

