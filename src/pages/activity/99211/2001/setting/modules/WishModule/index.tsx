import React, { useState } from "react";
import {
  Card,
  Button,
  Divider,
  Loading,
  Message,
  Table,
  Tab,
  NumberPicker,
  Form,
  Dialog,
  Input,
  DatePicker2
} from "@alifd/next";
import styles from "./index.module.scss";
import LzImageSelector from "@/components/LzImageSelector";
import LzColorPicker from "@/components/LzColorPicker";
import LzDialog from "@/components/LzDialog";
import ChoosePrizeForDZ from "@/components/ChoosePrizeForDZ";
import { PRIZE_INFO, PRIZE_TYPE } from "../../../util";
import { activityEditDisabled, deepCopy } from "@/utils";
import dayjs from "dayjs";

const { RangePicker } = DatePicker2;
const FormItem = Form.Item;
// 封装答案单元格组件
export default ({ data, dispatch, formData }) => {
  console.log(formData, "formData");
  const [loading, setLoading] = useState(false);
  // 当前编辑行数据
  const [editValue, setEditValue] = useState(null);
  // 当前编辑行index
  const [target, setTarget] = useState(0);
  const [visible, setVisible] = React.useState(false);
  const saveSetting = (): any => {
    if (!data.prizeList.length) {
      Message.error("请配置许愿礼品");
      return;
    }
    if (data.prizeList.some((e: any): boolean => e.prizeName === "")) {
      Message.error("请填写完整许愿礼品信息");
      return;
    }
    if (!data.energyValue) {
      Message.error("请填写单次许愿消耗能量值");
      return;
    }
    if (!data.wishCounts) {
      Message.error("请填写每人最多许愿次数");
      return;
    }
    if (!data.wishEndTime) {
      Message.error("请填写许愿结束时间");
      return;
    }
    if (!data.ruleText) {
      Message.error("请填写活动规则");
      return;
    }
    setLoading(true);
    Message.success(`${data.name}模块保存成功`);
    dispatch({ type: "RESET_PUSH" });
    setLoading(false);
  };

  const setData = (value, isUpdated = true) => {
    dispatch({ type: "UPDATE_MODULE", payload: value, isUpdated });
  };
  const onPrizeChange = (val): boolean | void => {
    data.prizeList[target] = { ...val };
    setData(data);
    setVisible(false);
  };
  const commonProps = {
    title: data.name,
    extra: (
      <div>
        <Button type="primary" onClick={saveSetting}>
          保存当前设置
        </Button>
      </div>
    )
  };
  return (
    <div>
      <Card free>
        <Card.Header {...commonProps} />
        <Divider />
        <Card.Content>
          <Loading visible={loading} style={{ width: "100%" }}>
            <div className={styles.operation}>
              <Tab>
                <Tab.Item title="页面设置" key="index0">
                  <div className={styles.memberBgContainer}>
                    <div className="crm-label">活动背景</div>
                    <div className={styles.imgUpload}>
                      <LzImageSelector
                        bgWidth={200}
                        bgHeight={140}
                        width={750}
                        height={1110}
                        value={data.pageBg}
                        onChange={(pageBg) => {
                          const updatedData = { ...data };
                          updatedData.pageBg = pageBg;
                          setData(updatedData);
                        }}
                      />
                      <div className={styles.tip}>
                        <div>图片尺寸为750px*1110px</div>
                        <div>图片格式：大小不超过1M图片类型为jpg、png</div>
                      </div>
                    </div>
                  </div>
                  <div className={styles.colorContainer}>
                    <div className={styles.colorPicker}>
                      <div className={styles.colorPickerItem}>
                        <span>弹窗按钮文字颜色</span>
                        <LzColorPicker
                          value={data.dialogTextColor || "#000000"}
                          onChange={(dialogTextColor) => {
                            const updatedData = { ...data };
                            updatedData.dialogTextColor = dialogTextColor;
                            setData(updatedData);
                          }}
                        />
                      </div>
                      <div className={styles.colorPickerItem}>
                        <span>弹窗按钮颜色</span>
                        <LzColorPicker
                          value={data.dialogBtnColor || "#000000"}
                          onChange={(dialogBtnColor) => {
                            const updatedData = { ...data };
                            updatedData.dialogBtnColor = dialogBtnColor;
                            setData(updatedData);
                          }}
                        />
                      </div>

                      <div className={styles.colorPickerItem}>
                        <span>提交愿望文字颜色</span>
                        <LzColorPicker
                          value={data.wishesTextColor || "#000000"}
                          onChange={(wishesTextColor) => {
                            const updatedData = { ...data };
                            updatedData.wishesTextColor = wishesTextColor;
                            setData(updatedData);
                          }}
                        />
                      </div>
                      <div className={styles.colorPickerItem}>
                        <span>提交愿望按钮颜色</span>
                        <LzColorPicker
                          value={data.wishesBtnColor || "#000000"}
                          onChange={(wishesBtnColor) => {
                            const updatedData = { ...data };
                            updatedData.wishesBtnColor = wishesBtnColor;
                            setData(updatedData);
                          }}
                        />
                      </div>

                      <div className={styles.colorPickerItem}>
                        <span>奖品愿望角标文字颜色</span>
                        <LzColorPicker
                          value={data.prizeWishTextColor || "#000000"}
                          onChange={(prizeWishTextColor) => {
                            const updatedData = { ...data };
                            updatedData.prizeWishTextColor = prizeWishTextColor;
                            setData(updatedData);
                          }}
                        />
                      </div>
                      <div className={styles.colorPickerItem}>
                        <span>奖品愿望角标按钮颜色</span>
                        <LzColorPicker
                          value={data.prizeWishBtnColor || "#000000"}
                          onChange={(prizeWishBtnColor) => {
                            const updatedData = { ...data };
                            updatedData.prizeWishBtnColor = prizeWishBtnColor;
                            setData(updatedData);
                          }}
                        />
                      </div>

                      <div className={styles.colorPickerItem}>
                        <span>加减号文字颜色</span>
                        <LzColorPicker
                          value={data.addTextColor || "#000000"}
                          onChange={(addTextColor) => {
                            const updatedData = { ...data };
                            updatedData.addTextColor = addTextColor;
                            setData(updatedData);
                          }}
                        />
                      </div>
                      <div className={styles.colorPickerItem}>
                        <span>加减号按钮颜色</span>
                        <LzColorPicker
                          value={data.addBtnColor || "#000000"}
                          onChange={(addBtnColor) => {
                            const updatedData = { ...data };
                            updatedData.addBtnColor = addBtnColor;
                            setData(updatedData);
                          }}
                        />
                      </div>

                      <div className={styles.colorPickerItem}>
                        <span>高亮文字颜色</span>
                        <LzColorPicker
                          value={data.highlightTextColor || "#000000"}
                          onChange={(highlightTextColor) => {
                            const updatedData = { ...data };
                            updatedData.highlightTextColor = highlightTextColor;
                            setData(updatedData);
                          }}
                        />
                      </div>
                      <div className={styles.colorPickerItem}>
                        <span>其他字体颜色</span>
                        <LzColorPicker
                          value={data.otherColor || "#000000"}
                          onChange={(otherColor) => {
                            const updatedData = { ...data };
                            updatedData.otherColor = otherColor;
                            setData(updatedData);
                          }}
                        />
                      </div>
                    </div>
                  </div>
                </Tab.Item>
                <Tab.Item title="奖品配置" key="index1">
                  <div className={styles.MemberContainer}>
                    <div className="crm-label">奖品配置</div>
                    <Button
                      disabled={data?.prizeList?.length >= 6 || activityEditDisabled()}
                      type="primary"
                      onClick={() => {
                        data?.prizeList?.push(deepCopy({ ...PRIZE_INFO }));
                        setData(data);
                      }}
                    >
                      +添加奖品（{data.prizeList?.length}/6）
                    </Button>
                    <Table dataSource={data?.prizeList} style={{ marginTop: "15px" }}>
                      <Table.Column title="奖品名称" dataIndex="prizeName" />
                      <Table.Column
                        title="奖品类型"
                        cell={(_, index, row) => <div>{PRIZE_TYPE[row.prizeType]}</div>}
                        dataIndex="prizeType"
                      />
                      <Table.Column
                        title="单位数量"
                        cell={(_, index, row) => {
                          if (row.prizeType === 1) {
                            return <div>{row.numPerSending ? `${row.numPerSending}张` : ""}</div>;
                          } else {
                            return <div>{row.unitCount ? `${row.unitCount}份` : ""}</div>;
                          }
                        }}
                      />
                      <Table.Column
                        title="发放份数"
                        cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}` : ""}</div>}
                      />
                      <Table.Column
                        title="单份价值(元)"
                        cell={(_, index, row) => (
                          <div>{row.prizeName ? (row.unitPrice ? Number(row.unitPrice).toFixed(2) : 0) : ""}</div>
                        )}
                      />
                      {
                        !activityEditDisabled() && <Table.Column
                          title="操作"
                          width={130}
                          cell={(val, index, _) => (
                            <FormItem>
                              <Button
                                text
                                type="primary"
                                onClick={() => {
                                  let row = data.prizeList[index];
                                  if (row.prizeName === "") {
                                    row = null;
                                  }
                                  setEditValue(row);
                                  setTarget(index);
                                  setVisible(true);
                                }}
                              >
                                <i className={`iconfont icon-chaojiyingxiaoicon-25`} />
                              </Button>
                              {data.prizeList.length > 1 && (
                                <Button
                                  text
                                  type="primary"
                                  disabled={activityEditDisabled()}
                                  onClick={() => {
                                    Dialog.confirm({
                                      v2: true,
                                      title: "提示",
                                      centered: true,
                                      content: "确认删除该奖品？",
                                      onOk: () => {
                                        data.prizeList.splice(index, 1);
                                        setData(data);
                                      },
                                      onCancel: () => console.log("cancel")
                                    } as any);
                                  }}
                                >
                                  <i className={`iconfont icon-shanchu`} />
                                </Button>
                              )}
                            </FormItem>
                          )}
                        />
                      }

                    </Table>
                  </div>

                </Tab.Item>
                <Tab.Item title="许愿配置" key="index2">
                  <div className={styles.MemberContainer}>
                    <Form labelCol={{ span: 6 }}
                          wrapperCol={{ span: 18 }}
                          labelAlign="left"
                          colon
                    >
                      <FormItem label={"单次许愿消耗能量值"}
                                required
                                requiredMessage={"请输入单次许愿消耗能量值"}
                                disabled={activityEditDisabled()}
                      >
                        <NumberPicker
                          style={{ width: "150px" }}
                          value={data.energyValue}
                          onChange={(energyValue) => {
                            const updatedData = { ...data };
                            updatedData.energyValue = energyValue;
                            setData(updatedData);
                          }}
                          type="inline"
                          min={1}
                          max={999}
                        />
                      </FormItem>
                      <FormItem
                        label={"每份礼品每人最多许愿次数"}
                        name="wishCounts"
                        required
                        requiredTrigger="onBlur"
                        requiredMessage="请输入每人每天最多中奖次数"
                        disabled={activityEditDisabled()}
                      >
                        <NumberPicker
                          value={data.wishCounts}
                          onChange={(wishCounts: number) => {
                            const updatedData = { ...data };
                            updatedData.wishCounts = wishCounts;
                            setData(updatedData);
                          }}
                          type="inline"
                          min={1}
                          max={9999999}
                        />
                        次
                      </FormItem>
                      <FormItem
                        label={"许愿时间"}
                        required
                        requiredMessage="请输入每人每天最多中奖次数"
                        disabled={activityEditDisabled()}
                      >
                        <RangePicker
                          className="w-300"
                          name="rangeDate"
                          showTime
                          value={[formData.startTime, data.wishEndTime]}
                          disabled={[true, false]}
                          onChange={(_, dateString) => {
                            const updatedData = { ...data };
                            updatedData.wishEndTime = dateString[1];
                            // showStartTime 在 wishEndTime 之后一天
                            updatedData.showStartTime = dayjs(dateString[1]).add(1, "day").format("YYYY-MM-DD HH:mm:ss");
                            setData(updatedData);
                          }}
                          disabledDate={(date) => {
                            // 禁用早于活动开始时间和晚于活动结束时间的日期
                            return date.valueOf() < dayjs(formData.startTime).valueOf() || date.valueOf() > dayjs(formData.endTime).valueOf();
                          }}
                        />
                      </FormItem>
                      <FormItem
                        label={"公示时间"}
                      >
                        <RangePicker
                          className="w-300"
                          name="rangeDate"
                          showTime
                          value={[data.showStartTime, formData.endTime]}
                          disabled={[true, true]}
                          disabledDate={(date) => {
                            return date.valueOf() < dayjs().subtract(1, "day").valueOf();
                          }}
                        />
                      </FormItem>
                      <FormItem label="活动规则" required requiredMessage="请输入活动规则说明">
                        <Input.TextArea
                          value={data.ruleText}
                          name="rules"
                          onChange={(ruleText) => setData({ ruleText })}
                          autoHeight={{ minRows: 8, maxRows: 40 }}
                          placeholder="请输入活动规则说明"
                          maxLength={100}
                          showLimitHint
                          className="form-input-ctrl"
                        />
                      </FormItem>
                    </Form>
                  </div>
                </Tab.Item>
              </Tab>
            </div>
          </Loading>
        </Card.Content>
      </Card>
      <LzDialog
        title={"选择奖项"}
        visible={visible}
        footer={false}
        onClose={() => setVisible(false)}
        style={{ width: "700px" }}
      >
        <ChoosePrizeForDZ
          defaultEditValue={data?.prizeList[target] ?? null}
          formData={formData}
          editValue={editValue}
          onChange={onPrizeChange}
          onCancel={() => {
            setVisible(false);
          }}
          hasProbability={false}
          hasLimit={false}
          hasShowTime={false}
          typeList={[3]}
          defaultTarget={3}
        />
      </LzDialog>
    </div>
  );
}

