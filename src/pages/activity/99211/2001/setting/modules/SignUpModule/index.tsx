import React from "react";
import { Card, Button, Divider, Loading, Message,  Table, Tab, NumberPicker, Dialog } from "@alifd/next";
import styles from "./index.module.scss";
import LzImageSelector from "@/components/LzImageSelector";
import LzColorPicker from "@/components/LzColorPicker";
import dayjs from "dayjs";
import { activityEditDisabled } from "@/utils";


const calcDateDiff = (rangeDate: any[]): string => {
  const startDate = dayjs(rangeDate[0]);
  const endDate = dayjs(rangeDate[1]);
  const diffInDays = endDate.diff(startDate, "day");
  const diffInMs = endDate.diff(startDate);
  const fullDaysMs = diffInDays * 24 * 60 * 60 * 1000;
  // 如果不是整天，天数+1，否则直接返回天数
  if (diffInMs > fullDaysMs) {
    return String(diffInDays + 1);
  }
  return String(diffInDays);
};

export default ({ data, dispatch, formData }) => {
  const maxDay: string = calcDateDiff(formData.rangeDate);
  const [loading, setLoading] = React.useState(false);
  const saveSetting = (): any => {
    if (!data.prizeList.length) {
      Message.error("请配置签到奖励");
      return;
    }
    // 循环判断data.prizeList中的每一项是否为空，如果为空，则提示用户
    if (data.prizeList.some((item) => !item.signDay || !item.energyValue)) {
      Message.error("请填写完整签到奖励信息");
      return;
    }
    setLoading(true);
    Message.success(`${data.name}模块保存成功`);
    dispatch({ type: "RESET_PUSH" });
    setLoading(false);
  };
  const setData = (value, isUpdated = true) => {
    dispatch({ type: "UPDATE_MODULE", payload: value, isUpdated });
  };
  const commonProps = {
    title: data.name,
    extra: (
      <div>
        <Button type="primary" onClick={saveSetting}>
          保存当前设置
        </Button>
      </div>
    )
  };
  return (
    <div>
      <Card free>
        <Card.Header {...commonProps} />
        <Divider />
        <Card.Content>
          <Loading visible={loading} style={{ width: "100%" }}>
            <div className={styles.operation}>
              <Tab>
                <Tab.Item title="图片设置" key="index0">
                  <div className={styles.memberBgContainer}>
                    <div className="crm-label">活动背景</div>
                    <div className={styles.imgUpload}>
                      <LzImageSelector
                        bgWidth={200}
                        bgHeight={140}
                        width={750}
                        height={540}
                        value={data.pageBg}
                        onChange={(pageBg) => {
                          const updatedData = { ...data };
                          updatedData.pageBg = pageBg;
                          setData(updatedData);
                        }}
                      />
                      <div className={styles.tip}>
                        <div>图片尺寸宽度为750px,高度为540px</div>
                        <div>图片格式：大小不超过1M图片类型为jpg、png</div>
                      </div>
                    </div>

                    <div className="crm-label">已签到图标</div>
                    <div className={styles.imgUpload}>
                      <LzImageSelector
                        bgWidth={200}
                        bgHeight={140}
                        width={90}
                        height={90}
                        value={data.hasSignUpIconBg}
                        onChange={(hasSignUpIconBg) => {
                          const updatedData = { ...data };
                          updatedData.hasSignUpIconBg = hasSignUpIconBg;
                          setData(updatedData);
                        }}
                      />
                      <div className={styles.tip}>
                        <div>图片尺寸为90px*90px</div>
                        <div>图片格式：大小不超过1M图片类型为jpg、png</div>
                      </div>
                    </div>

                    <div className="crm-label">待签到图标</div>
                    <div className={styles.imgUpload}>
                      <LzImageSelector
                        bgWidth={200}
                        bgHeight={140}
                        width={90}
                        height={90}
                        value={data.noSignUpIconBg}
                        onChange={(noSignUpIconBg) => {
                          const updatedData = { ...data };
                          updatedData.noSignUpIconBg = noSignUpIconBg;
                          setData(updatedData);
                        }}
                      />
                      <div className={styles.tip}>
                        <div>图片尺寸为90px*90px</div>
                        <div>图片格式：大小不超过1M图片类型为jpg、png</div>
                      </div>
                    </div>

                    <div className="crm-label">未到日期签到图标</div>
                    <div className={styles.imgUpload}>
                      <LzImageSelector
                        bgWidth={200}
                        bgHeight={140}
                        width={90}
                        height={90}
                        value={data.noSignUpIconDateBg}
                        onChange={(noSignUpIconDateBg) => {
                          const updatedData = { ...data };
                          updatedData.noSignUpIconDateBg = noSignUpIconDateBg;
                          setData(updatedData);
                        }}
                      />
                      <div className={styles.tip}>
                        <div>图片尺寸为90px*90px</div>
                        <div>图片格式：大小不超过1M图片类型为jpg、png</div>
                      </div>
                    </div>

                    <div className="crm-label">左箭头</div>
                    <div className={styles.imgUpload}>
                      <LzImageSelector
                        bgWidth={200}
                        bgHeight={140}
                        width={40}
                        height={40}
                        value={data.leftArrowBg}
                        onChange={(leftArrowBg) => {
                          const updatedData = { ...data };
                          updatedData.leftArrowBg = leftArrowBg;
                          setData(updatedData);
                        }}
                      />
                      <div className={styles.tip}>
                        <div>图片尺寸为40px*40px</div>
                        <div>图片格式：大小不超过1M图片类型为jpg、png</div>
                      </div>
                    </div>

                    <div className="crm-label">右箭头</div>
                    <div className={styles.imgUpload}>
                      <LzImageSelector
                        bgWidth={200}
                        bgHeight={140}
                        width={40}
                        height={40}
                        value={data.rightArrowBg}
                        onChange={(rightArrowBg) => {
                          const updatedData = { ...data };
                          updatedData.rightArrowBg = rightArrowBg;
                          setData(updatedData);
                        }}
                      />
                      <div className={styles.tip}>
                        <div>图片尺寸为40px*40px</div>
                        <div>图片格式：大小不超过1M图片类型为jpg、png</div>
                      </div>
                    </div>
                  </div>
                </Tab.Item>
                <Tab.Item title="颜色设置" key="index1">
                  <div className={styles.colorContainer}>
                    <div className={styles.colorPicker}>
                      <div className={styles.colorPickerItem}>
                        <span>立即签到按钮颜色</span>
                        <LzColorPicker
                          value={data.signUpBtnColor || "#000000"}
                          onChange={(signUpBtnColor) => {
                            const updatedData = { ...data };
                            updatedData.signUpBtnColor = signUpBtnColor;
                            setData(updatedData);
                          }}
                        />
                      </div>
                      <div className={styles.colorPickerItem}>
                        <span>立即签到文字颜色</span>
                        <LzColorPicker
                          value={data.signUpTextColor || "#000000"}
                          onChange={(signUpTextColor) => {
                            const updatedData = { ...data };
                            updatedData.signUpTextColor = signUpTextColor;
                            setData(updatedData);
                          }}
                        />
                      </div>
                      <div className={styles.colorPickerItem}>
                        <span>签到记录按钮颜色</span>
                        <LzColorPicker
                          value={data.signUpRecordBtnColor || "#000000"}
                          onChange={(signUpRecordBtnColor) => {
                            const updatedData = { ...data };
                            updatedData.signUpRecordBtnColor = signUpRecordBtnColor;
                            setData(updatedData);
                          }}
                        />
                      </div>
                      <div className={styles.colorPickerItem}>
                        <span>签到记录文字颜色</span>
                        <LzColorPicker
                          value={data.signUpRecordTextColor || "#000000"}
                          onChange={(signUpRecordTextColor) => {
                            const updatedData = { ...data };
                            updatedData.signUpRecordTextColor = signUpRecordTextColor;
                            setData(updatedData);
                          }}
                        />
                      </div>
                      <div className={styles.colorPickerItem}>
                        <span>待签到时能量值文字颜色</span>
                        <LzColorPicker
                          value={data.signUpEnergyTextColor || "#000000"}
                          onChange={(signUpEnergyTextColor) => {
                            const updatedData = { ...data };
                            updatedData.signUpEnergyTextColor = signUpEnergyTextColor;
                            setData(updatedData);
                          }}
                        />
                      </div>
                      <div className={styles.colorPickerItem}>
                        <span>全部其他文字颜色</span>
                        <LzColorPicker
                          value={data.otherTextColor || "#000000"}
                          onChange={(otherTextColor) => {
                            const updatedData = { ...data };
                            updatedData.otherTextColor = otherTextColor;
                            setData(updatedData);
                          }}
                        />
                      </div>
                      <div className={styles.colorPickerItem}>
                        <span>高亮文字颜色(天数)</span>
                        <LzColorPicker
                          value={data.highlightTextColor || "#000000"}
                          onChange={(highlightTextColor) => {
                            const updatedData = { ...data };
                            updatedData.highlightTextColor = highlightTextColor;
                            setData(updatedData);
                          }}
                        />
                      </div>
                    </div>
                  </div>
                </Tab.Item>
                <Tab.Item title="签到奖励" key="index2">
                  <Button
                    style={{ marginBottom: 20 }}
                    type="primary"
                    disabled={data.prizeList.length >= maxDay || activityEditDisabled()}
                    onClick={() => {
                      if (data.prizeList.length >= maxDay) {
                        Message.error("签到天数已达到上限");
                        return;
                      }
                      const updatedData = { ...data };
                      updatedData.prizeList.push({
                        signDay: "",
                        energyValue: ""
                      });
                      setData(updatedData);

                    }}>增加签到天数</Button>
                  <Table
                    dataSource={data.prizeList}
                    fixedHeader
                    maxBodyHeight={500}>
                    <Table.Column title="签到天数"
                                  dataIndex="prizeName"
                                  align={"center"}
                                  cell={(value, index, record) => {
                                    return (
                                      <div>
                                        <NumberPicker
                                          className={styles.formNumberPicker}
                                          disabled = {activityEditDisabled()}
                                          type="inline"
                                          precision={0}
                                          max={maxDay}
                                          min={0}
                                          value={record.signDay}
                                          onChange={(signDay) => {
                                            if (typeof signDay !== "number" || isNaN(signDay)) {
                                              Message.error("请输入有效的签到天数");
                                              return;
                                            }

                                            if (index > 0) {
                                              const prevDay = data.prizeList[index - 1].signDay;
                                              if (signDay <= prevDay) {
                                                Message.error("当前签到天数必须大于上一行的签到天数");
                                                return;
                                              }
                                            }
                                            const updatedData = { ...data };
                                            updatedData.prizeList[index].signDay = signDay;
                                            setData(updatedData);
                                          }}
                                        />
                                      </div>
                                    );
                                  }} />
                    <Table.Column
                      title="可获得能量值"
                      align={"center"}
                      dataIndex="prizeName"
                      cell={(value, index, record) => {
                        return (
                          <div>
                            <NumberPicker
                              disabled = {activityEditDisabled()}
                              className={styles.formNumberPicker}
                              type="inline"
                              precision={0}
                              max={99999}
                              min={0}
                              value={record.energyValue}
                              onChange={(energy) => {
                                if (typeof energy !== "number" || isNaN(energy)) {
                                  Message.error("请输入有效的能量值");
                                  return;
                                }
                                const updatedData = { ...data };
                                updatedData.prizeList[index].energyValue = energy;
                                setData(updatedData);
                              }}
                            />
                          </div>
                        );
                      }} />
                    {
                      !activityEditDisabled()&&
                      <Table.Column
                        title="操作"
                        align={"center"}
                        width={100}
                        cell={(value, index, record) => {
                          return (
                            <div>
                              <Button type="primary" onClick={() => {
                                Dialog.confirm({
                                  title: "删除",
                                  content: "确定删除该签到奖励吗？",
                                  onOk: () => {
                                    if (data.prizeList.length === 1) {
                                      Message.error("至少需要一个签到奖励");
                                      return;
                                    }
                                    const updatedData = { ...data };
                                    updatedData.prizeList.splice(index, 1);
                                    setData(updatedData);
                                  }
                                });
                              }}>删除</Button>
                            </div>
                          );
                        }} />
                    }
                  </Table>
                </Tab.Item>
              </Tab>
            </div>
          </Loading>
        </Card.Content>
      </Card>
    </div>
  );
}

