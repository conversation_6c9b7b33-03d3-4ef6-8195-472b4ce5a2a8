import React from 'react';
import styles from './index.module.scss';
import LzImageSelector from '@/components/LzImageSelector';
import { Card, Button, Divider, Message, Loading, } from '@alifd/next';
import LzDialog from '@/components/LzDialog';
import EditHotZone from '../../compoonets/EditHotZone';


export default ({ data, dispatch, id }) => {
  const [hotVisible, setHotVisible] = React.useState(false);
  const [loading, setLoading] = React.useState(false);

  const addHotZone = () => {
    if (!data.hotZoneSetting.bg) {
      Message.error('请先上传背景图片后添加热区');
      return;
    }
    setHotVisible(true);
  };
  const saveValidate = () => {
    if (!data.hotZoneSetting.bg) {
      Message.error('请先上传背景图片');
      return false;
    }
    if (!data.hotZoneSetting.hotZoneList.length) {
      Message.error(`请添加热区`);
      return false;
    }
    return true;
  };
  const saveSetting = (): any => {
    if (!saveValidate()) {
      return false;
    }
    setLoading(true);
    Message.success(`${data.name}模块保存成功`);
    dispatch({ type: 'RESET_PUSH' });
    setLoading(false);
  };
  const setData = (value) => {
    dispatch({ type: 'UPDATE_MODULE', payload: value });
  };
  const commonProps = {
    title: data.name,
    extra: (
      <div>
        <Button type="primary" onClick={saveSetting}>
          保存模块设置
        </Button>
      </div>
    ),
  };

  return (
    <div>
      <Card free>
        <Card.Header {...commonProps} />
        <Divider />
        <Card.Content>
          <Loading visible={loading} inline={false}>
            <div className={styles.operation}>
              <div className={styles.MemberContainer} key={`member`}>
                <div className="crm-label">活动图设置</div>
                <div className={styles.imgUpload}>
                  <div>
                    <LzImageSelector
                      bgWidth={200}
                      bgHeight={140}
                      width={750}
                      value={data.hotZoneSetting.bg}
                      onChange={(bg) => {
                        data.hotZoneSetting.bg = bg;
                        data.hotZoneSetting.hotZoneList = [];
                        setData(data);
                      }}
                    />
                    <div className={styles.tip}>
                      <div>图片尺寸：建议宽度为750px</div>
                      <div>图片格式：大小不超过1M图片类型为jpg、png</div>
                    </div>
                  </div>
                  <div className={styles.setting}>
                    <Button className={styles.btn} onClick={() => addHotZone()}>
                      编辑热区
                    </Button>
                    {data.hotZoneSetting.hotZoneList.map((hot, hotZoneIndex) => (
                      <div key={hotZoneIndex} className={styles.urlContainer}>
                        <div className={styles.url}>{`热区${String(hotZoneIndex + 1).padStart(2, '0')}:${
                          hot.url
                        }`}</div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </Loading>
        </Card.Content>
      </Card>
      <LzDialog
        title={'编辑热区'}
        visible={hotVisible}
        footer={false}
        onClose={() => setHotVisible(false)}
        style={{ width: '750px' }}
      >
        <EditHotZone
          data={data.hotZoneSetting}
          onChange={(hotZoneList) => {
            data.hotZoneSetting.hotZoneList = hotZoneList;
            setData(data);
            setHotVisible(false);
          }}
          onClose={() => setHotVisible(false)}
          actionType={[1]}
        />
      </LzDialog>
    </div>
  );
};
