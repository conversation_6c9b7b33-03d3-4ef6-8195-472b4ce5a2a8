import React, { useState } from "react";
import {
  Card,
  Button,
  Divider,
  Loading,
  Message,
  Table,
  Tab,
  Form,
  Radio,
  Grid,
  Dialog, Upload
} from "@alifd/next";
import styles from "./index.module.scss";
import LzImageSelector from "@/components/LzImageSelector";
import LzDialog from "@/components/LzDialog";
import {  downloadExcel } from "@/utils";
import SwiperItem from "../../compoonets/SwiperSettings";
// @ts-ignore
import { config } from "ice";
import CONST from "@/utils/constant";
import { templateExport } from "@/api/v99211";
import LzColorPicker from "@/components/LzColorPicker";

const FormItem = Form.Item;
const RadioGroup = Radio.Group;
const { Row, Col } = Grid;

export default ({ data, dispatch, formData }) => {
  const [loading, setLoading] = useState(false);
  const [visible, setVisible] = React.useState(false);
  const [editValue, setEditValue] = React.useState(null);
  const [editIndex, setEditIndex] = React.useState(0);
  // @ts-ignore
  const [uploaderRef, setUploaderRef] = useState(false);
  const saveSetting = (): any => {
    if (data.btnShowType==2 &&!data.btnList.length) {
      Message.error("请配置按钮信息");
      return;
    }
    if (!data.productList.length){
      Message.error("请配置商品");
      return;
    }
    setLoading(true);
    Message.success(`${data.name}模块保存成功`);
    dispatch({ type: "RESET_PUSH" });
    setLoading(false);
  };
  const clearFileAndProductData = () => {
    const updatedData = { ...data };
    updatedData.fileList = [];
    updatedData.productList = [];
    return updatedData;
  };
  const setData = (value, isUpdated = true) => {
    dispatch({ type: "UPDATE_MODULE", payload: value, isUpdated });
  };
  const onSubmit = (val, type) => {
    const updatedData = { ...data };
    if (type === "add") {
      updatedData.btnList.push(val);
      setData(updatedData);
    } else {
      updatedData.btnList[editIndex] = val;
    }
  };
  const commonProps = {
    title: data.name,
    extra: (
      <div>
        <Button type="primary" onClick={saveSetting}>
          保存当前设置
        </Button>
      </div>
    )
  };
  const downloadTemplate = async () => {
    try {
      const data: any = await templateExport();
      downloadExcel(data, '商品导入模板');
    } catch (error) {
      Message.error(error.message);
    }
  };

  const saveUploaderRef = (ref) => {
    if (!ref) {
      return;
    }
    setUploaderRef(ref.getInstance());
  };
  const setTemporarySeriesList = async (value) => {

    Dialog.success({
      title: '导入结果',
      content: (
        <div>
          <p>导入成功</p>
        </div>
      ),
      onOk: () => {
          const updatedData = { ...data };
          updatedData.productList = value;
          setData(updatedData);
      },
    });
  };
  return (
    <div>
      <Card free>
        <Card.Header {...commonProps} />
        <Divider />
        <Card.Content>
          <Loading visible={loading} style={{ width: "100%" }}>
            <div className={styles.operation}>
              <Tab>
                <Tab.Item title="页面设置" key="index0">
                  <div className={styles.memberBgContainer}>
                    <div className="crm-label">活动背景</div>
                    <div className={styles.imgUpload}>
                      <LzImageSelector
                        bgWidth={200}
                        bgHeight={140}
                        width={750}
                        height={1370}
                        value={data.pageBg}
                        onChange={(pageBg) => {
                          const updatedData = { ...data };
                          updatedData.pageBg = pageBg;
                          setData(updatedData);
                        }}
                      />
                      <div className={styles.tip}>
                        <div>图片尺寸为750px*1370px</div>
                        <div>图片格式：大小不超过1M图片类型为jpg、png</div>
                      </div>
                    </div>

                    <div className="crm-label">Tab按钮背景</div>
                    <div className={styles.imgUpload}>
                      <LzImageSelector
                        bgWidth={200}
                        bgHeight={140}
                        width={162}
                        height={48}
                        value={data.tabBg}
                        onChange={(tabBg) => {
                          const updatedData = { ...data };
                          updatedData.tabBg = tabBg;
                          setData(updatedData);
                        }}
                      />
                      <div className={styles.tip}>
                        <div>图片尺寸为162px*48px</div>
                        <div>图片格式：大小不超过1M图片类型为jpg、png</div>
                      </div>
                    </div>

                    <div className="crm-label">Tab选中按钮背景</div>
                    <div className={styles.imgUpload}>
                      <LzImageSelector
                        bgWidth={200}
                        bgHeight={140}
                        width={162}
                        height={48}
                        value={data.tabActiveBg}
                        onChange={(tabActiveBg) => {
                          const updatedData = { ...data };
                          updatedData.tabActiveBg = tabActiveBg;
                          setData(updatedData);
                        }}
                      />
                      <div className={styles.tip}>
                        <div>图片尺寸为162px*48px</div>
                        <div>图片格式：大小不超过1M图片类型为jpg、png</div>
                      </div>
                    </div>
                  </div>
                  <div className={styles.colorContainer}>
                    <div className={styles.colorPicker}>
                      <div className={styles.colorPickerItem}>
                        <span>Tab文字颜色</span>
                        <LzColorPicker
                          value={data.otherTextColor || "#000000"}
                          onChange={(otherTextColor) => {
                            const updatedData = { ...data };
                            updatedData.otherTextColor = otherTextColor;
                            setData(updatedData);
                          }}
                        />
                      </div>
                      <div className={styles.colorPickerItem}>
                        <span>Tab选中文字颜色</span>
                        <LzColorPicker
                          value={data.highlightTextColor || "#000000"}
                          onChange={(highlightTextColor) => {
                            const updatedData = { ...data };
                            updatedData.highlightTextColor = highlightTextColor;
                            setData(updatedData);
                          }}
                        />
                      </div>
                    </div>
                  </div>
                </Tab.Item>
                <Tab.Item title="按钮设置" key="index1">
                  <div className={styles.MemberContainer} key={`member`}>
                    <Form labelCol={{ span: 4 }}
                          wrapperCol={{ span: 20 }}
                          labelAlign="left"
                          colon
                    >
                      <FormItem label="是否展示底部按钮" required>
                        <RadioGroup
                          value={data.btnShowType}
                          onChange={(btnShowType: number) => {
                            const updatedData = { ...data };
                            updatedData.btnShowType = btnShowType;
                            setData({ btnShowType });
                          }}
                        >
                          <Radio id="1" value={1}>
                            不展示
                          </Radio>
                          <Radio id="2" value={2}>
                            展示
                          </Radio>
                        </RadioGroup>
                      </FormItem>
                      <FormItem label=" " colon={false}>
                        <Row gutter="4">
                          <Col>
                            {data.btnShowType === 2 && (
                              <div>
                                <div className="crm-label">按钮背景</div>
                                <div className={styles.imgUpload}>
                                  <LzImageSelector
                                    bgWidth={200}
                                    bgHeight={140}
                                    width={750}
                                    height={120}
                                    value={data.btnBg}
                                    onChange={(btnBg) => {
                                      const updatedData = { ...data };
                                      updatedData.btnBg = btnBg;
                                      setData(updatedData);
                                    }}
                                  />
                                  <div className={styles.tip}>
                                    <div>图片尺寸为750px*120px</div>
                                    <div>图片格式：大小不超过1M图片类型为jpg、png</div>
                                  </div>
                                </div>

                                <Button type={"primary"}
                                        style={{ margin: "10px 0" }}
                                        disabled={data.btnList?.length >= 3}
                                        onClick={() => {
                                          setEditValue(null);
                                          setVisible(true);
                                        }}>
                                  增加按钮（{data.btnList?.length || 0}/3）
                                </Button>
                                <Table
                                  dataSource={data.btnList}
                                  fixedHeader
                                  maxBodyHeight={500}>
                                  <Table.Column title="图片"
                                                dataIndex="prizeName"
                                                align={"center"}
                                                width={250}
                                                cell={(value, index, record) => {
                                                  return (
                                                    <div>
                                                      <img style={{width:222,height:72}} src={record.img}
                                                           alt={""} />
                                                    </div>
                                                  );
                                                }} />
                                  <Table.Column
                                    title="跳转链接"
                                    align={"center"}
                                    cell={(value, index, record) => {
                                      return (
                                        <div>
                                          {record.link}
                                        </div>
                                      );
                                    }} />
                                  <Table.Column
                                    title="操作"
                                    align={"center"}
                                    width={140}
                                    cell={(value, index, record) => {
                                      return (
                                        <div style={{ display: "flex", justifyContent: "space-between" }}>
                                          <Button onClick={() => {
                                            setEditValue(record);
                                            setVisible(true);
                                            setEditIndex(index);
                                          }}>
                                            编辑
                                          </Button>
                                          <Button type="primary" onClick={() => {
                                            Dialog.confirm({
                                              title: "删除",
                                              content: "确定删除该按钮吗？",
                                              onOk: () => {
                                                if (data.btnList.length === 1) {
                                                  Message.error("至少需要一个按钮配置");
                                                  return;
                                                }
                                                const updatedData = { ...data };
                                                updatedData.btnList.splice(index, 1);
                                                setData(updatedData);
                                              }
                                            });
                                          }}>删除</Button>
                                        </div>
                                      );
                                    }} />
                                </Table>
                              </div>
                            )}
                          </Col>
                        </Row>
                      </FormItem>
                    </Form>

                  </div>
                </Tab.Item>
                <Tab.Item title="商品配置" key="index2">
                  <div className={styles.MemberContainer}>
                    <Form labelCol={{ span: 4 }}
                          wrapperCol={{ span: 20 }}
                          labelAlign="left"
                          colon
                    >
                      <FormItem label="数据上传">
                        <Message type="notice" style={{ marginBottom: 10 }}>
                          导入须知： <br />
                          1.导入前请下载模板，将你要上传的数据放入模板中，使用模板进行导入。
                          <br />
                          2.单次导入最大5M，导入中请不要关闭此页面。
                          <br />
                          <Button text type="primary" onClick={downloadTemplate} >
                            下载模板
                          </Button>
                        </Message>

                        <Upload
                          action={`${config.baseURL}/99211/template/import`}
                          name="file"
                          method="post"
                          headers={{
                            token: localStorage.getItem(CONST.LZ_SSO_TOKEN),
                            prd: localStorage.getItem(CONST.LZ_SSO_PRD),
                          }}
                          ref={saveUploaderRef}
                          value={data.fileList}
                          fileNameRender={(file) => <span>{data.fileName}</span>}
                          limit={1}
                          listType="text"
                          accept=".xls,.xlsx"
                          onChange={(info) => {
                            if (info && info.length > 0) {
                              const file = info[0];
                              if (!file || !file.size) {
                                Message.error('文件无效');
                                return;
                              }
                              if (file?.size > 5 * 1024 * 1024) {
                                Message.error('文件大小不能超过5M');
                                return;
                              }
                              const updatedData= {...data}
                              updatedData.fileName = file.name;
                              updatedData.fileList = info;
                              setData(updatedData)
                            } else {
                              setData(clearFileAndProductData());
                            }
                          }}
                          onError={(res:any) => {
                            Message.error(res?.response?.message || '文件错误，请上传正确的文件');
                          }}
                          onRemove={() => {
                            setData(clearFileAndProductData());
                            Message.error('删除成功，请重新上传');
                          }}
                          onSuccess={(res:any) => {
                            if (res.response.code === 200) {
                              setTemporarySeriesList(res.response.data);
                            } else {
                              setData(clearFileAndProductData());
                              Message.error(res.response?.message || '文件错误，请上传正确的文件');
                            }
                          }}
                          style={{ marginBottom: 10 }}
                        >
                          <div className="next-upload-drag">
                            <p className="next-upload-drag-icon">
                              <Button type="primary">
                                上传产品数据
                              </Button>
                            </p>
                            <p className="next-upload-drag-hint">支持xls类型的文件</p>
                          </div>
                        </Upload>
                      </FormItem>
                    </Form>
                  </div>
                  <Tab>
                    {
                      data.productList && data.productList.map((item:any,index) => {
                        return(
                          <Tab.Item title={item.tabName} key={index}>
                            <Table dataSource={item.skuList}>
                              <Table.Column title="商品图片" cell={(value, index, record)=>{
                                return(
                                  <img style={{width: 100,height: 100}} alt={''} src={record.img}/>
                                )
                              }} />
                              <Table.Column title="商品链接" dataIndex="link" />
                            </Table>
                          </Tab.Item>
                        )
                      })
                    }
                  </Tab>
                </Tab.Item>
              </Tab>
            </div>
          </Loading>
        </Card.Content>
      </Card>
      <LzDialog
        title={"新增"}
        visible={visible}
        footer={false}
        onClose={() => setVisible(false)}
        style={{ width: "450px" }}
      >
        <SwiperItem
          suggestSize={{ height: 73, width:223 }}
          size={{ height: 73, width: 223 }}
          editValue={editValue}
          onSubmit={onSubmit}
          onClose={() => setVisible(false)}
        />
      </LzDialog>
    </div>
  );
}

