.operation {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15px;

  .container {
    padding: 15px;
    background: #f4f6f9;
    border-radius: 5px;
  }

  .memberBgContainer {
    @extend .container;
    .imgUpload {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-bottom: 10px;

      .tip {
        margin-top: 10px;
      }
    }
  }
  .colorContainer {
    @extend .container;

    .colorPicker {
      display: flex;
      margin-top: 10px;
      gap: 20px;

      span {
        font-weight: bold;
        margin-right: 10px;
      }
    }
  }
  .MemberContainer {
    @extend .container;
    .imgUpload {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-bottom: 10px;

      .tip {
        margin-top: 10px;
      }
    }
  }
  .formLineHeight {
    line-height: 28px;
  }
  .formInLine {
    display: inline-block;
    vertical-align: top;
    margin-bottom: 0;
  }
  .cardList {
    display: flex;
    flex-wrap: wrap;
    gap: 0 10px;
    margin-top: 10px;
    .cardName {
      width: 120px;
    }
    .cardNamePre {
      width: 180px;
    }
    .validateInput {
      width: 0;
    }
  }
}
