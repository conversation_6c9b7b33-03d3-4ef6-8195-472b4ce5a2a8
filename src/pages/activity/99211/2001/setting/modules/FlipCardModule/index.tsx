import React, { useEffect } from "react";
import { Card, Button, Divider, Loading, Message, Form, Input, Field, Radio, NumberPicker } from "@alifd/next";
import styles from "./index.module.scss";
import LzImageSelector from "@/components/LzImageSelector";
import LzColorPicker from "@/components/LzColorPicker";
import { FormLayout } from "../../utils";
import { activityEditDisabled } from "@/utils";

const FormItem = Form.Item;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4
  },
  wrapperCol: {
    span: 20
  },
  labelAlign: "left",
  colon: true
};
const cardTypeConfig = {
  4: {
    width: 309,
    height: 309
  },
  6: {
    width: 205,
    height: 310
  },
  9: {
    width: 205,
    height: 205
  }
};
export default ({ data, dispatch, id }) => {
  const field: Field = Field.useField();
  const ruleField: Field = Field.useField();
  const [loading, setLoading] = React.useState(false);
  const [allProbability, setAllProbability] = React.useState(0);
  const saveSetting = (): any => {
    field.validate((err, value) => {
      console.log("err", err);
      if (err) {
        return;
      }
      let isIncreasing = true;
      for (let i = 0; i < data.energyValueList.length; i++) {
        if (data.energyValueList[i]?.cardNum >= data.energyValueList[i + 1]?.cardNum) {
          isIncreasing = false;
          break;
        }
      }
      if (!isIncreasing) {
        Message.error("卡片数必须递增");
        return;
      }
      if (!data.ruleText) {
        Message.error("请输入活动规则说明");
        return;
      }
      setLoading(true);
      Message.success(`${data.name}模块保存成功`);
      dispatch({ type: "RESET_PUSH" });
      setLoading(false);
    });
  };
  const setData = (value, isUpdated = true) => {
    dispatch({ type: "UPDATE_MODULE", payload: value, isUpdated });
  };

  useEffect(() => {
    if (data.cardList.length !== data.cardNumType) {
      const cardList: any = [];
      for (let i = 0; i < data.cardNumType; i++) {
        cardList.push({
          id: i,
          front: "",
          back: "",
          cardNum: "",
          name: "",
          probability: ""
        });
      }
      setData({ cardList });
    }
  }, [data.cardNumType]);

  useEffect(() => {
    let probability = 0;
    data.cardList.forEach((item) => {
      if (item.probability) {
        probability += Number(item.probability);
      }
    });
    if (probability > 100) {
      Message.error(`概率总和不能大于100`);
      return;
    }
    setAllProbability(probability);
  }, [data.cardList]);

  const checkFlipCount = (rule, value, callback) => {
    if (!value) {
      callback("请输入每日可翻卡次数");
    } else {
      callback();
    }
  };

  const checkCardNum = (rule, value, callback) => {
    if (!value) {
      callback("请输入卡片数");
    } else {
      callback();
    }
  };

  const commonProps = {
    title: data.name,
    extra: (
      <div>
        <Button type="primary" onClick={saveSetting}>
          保存当前设置
        </Button>
      </div>
    )
  };
  return (
    <div>
      <Card free>
        <Card.Header {...commonProps} />
        <Divider />
        <Card.Content>
          <Loading visible={loading} style={{ width: "100%" }}>
            <div className={styles.operation}>
              <div className={styles.memberBgContainer}>
                <div className="crm-label">活动背景</div>
                <div className={styles.imgUpload}>
                  <LzImageSelector
                    bgWidth={200}
                    bgHeight={140}
                    width={750}
                    value={data.pageBg}
                    onChange={(pageBg) => {
                      setData({ pageBg });
                    }}
                  />
                  <div className={styles.tip}>
                    <div>图片尺寸宽度为750px</div>
                    <div>图片格式：大小不超过1M图片类型为jpg、png</div>
                  </div>
                </div>
                <div className="crm-label">翻卡背景图</div>
                <div className={styles.imgUpload}>
                  <LzImageSelector
                    bgWidth={200}
                    bgHeight={140}
                    width={640}
                    height={640}
                    value={data.cardBg}
                    onChange={(cardBg) => {
                      setData({ cardBg });
                    }}
                  />
                  <div className={styles.tip}>
                    <div>图片尺寸：640*640px</div>
                    <div>图片格式：大小不超过1M图片类型为jpg、png</div>
                  </div>
                </div>
                <div className="crm-label">翻卡按钮图</div>
                <div className={styles.imgUpload}>
                  <LzImageSelector
                    bgWidth={200}
                    bgHeight={140}
                    height={73}
                    value={data.flipBtn}
                    onChange={(flipBtn) => {
                      setData({ flipBtn });
                    }}
                  />
                  <div className={styles.tip}>
                    <div>图片尺寸高度为73px</div>
                    <div>图片格式：大小不超过1M图片类型为jpg、png</div>
                  </div>
                </div>
              </div>
              <div className={styles.colorContainer}>
                <div className="crm-label">页面颜色配置</div>
                <div className={styles.colorPicker}>
                  <div className={styles.colorPickerItem}>
                    <span>弹窗按钮文字颜色</span>
                    <LzColorPicker
                      value={data.dialogTextColor || "#000000"}
                      onChange={(dialogTextColor) => {
                        setData({ dialogTextColor });
                      }}
                    />
                  </div>
                  <div className={styles.colorPickerItem}>
                    <span>弹窗按钮颜色</span>
                    <LzColorPicker
                      value={data.dialogBtnColor || "#000000"}
                      onChange={(dialogBtnColor) => {
                        setData({ dialogBtnColor });
                      }}
                    />
                  </div>
                  <div className={styles.colorPickerItem}>
                    <span>规则文字颜色</span>
                    <LzColorPicker
                      value={data.ruleTextColor || "#000000"}
                      onChange={(ruleTextColor) => {
                        setData({ ruleTextColor });
                      }}
                    />
                  </div>
                  <div className={styles.colorPickerItem}>
                    <span>翻卡次数文字颜色</span>
                    <LzColorPicker
                      value={data.flipCountTextColor || "#000000"}
                      onChange={(flipCountTextColor) => {
                        setData({ flipCountTextColor });
                      }}
                    />
                  </div>
                </div>
              </div>
              <div className={styles.MemberContainer}>
                <div className="crm-label">活动内容配置</div>
                <Form {...formItemLayout} field={field} disabled ={activityEditDisabled()}>
                  <FormItem label="卡片数量" required>
                    <Radio.Group
                      name="cardNumType"
                      value={data.cardNumType}
                      onChange={(cardNumType) => {
                        setData({
                          cardNumType, energyValueList: [
                            {
                              id: 1,
                              // 翻卡次数
                              cardNum: "",
                              // 能量值
                              energyValue: ""
                            }]
                        });

                        if (data.flipCount && cardNumType >= data.flipCount) {
                          field.setErrors({ flipCount: "" });
                        }
                        data.energyValueList.forEach((item, index) => {
                          if (item.cardNum && item.cardNum > cardNumType) {
                            field.setErrors({ [`cardNum${item.id}`]: "" });
                          }
                        });

                      }}
                    >
                      <Radio value={4}>4张卡片</Radio>
                      <Radio value={6}>6张卡片</Radio>
                      <Radio value={9}>9张卡片</Radio>
                    </Radio.Group>
                  </FormItem>
                  <FormItem label="机会设置" name="flipCount" required requiredMessage="请输入每日可翻卡次数"
                            validator={checkFlipCount}>
                    用户每日可获得
                    <NumberPicker
                      // @ts-ignore
                      name="flipCount"
                      min={1}
                      max={data.cardNumType}
                      type="inline"
                      value={data.flipCount}
                      onChange={(flipCount: number) => setData({ flipCount })}
                    />
                    次翻卡机会
                  </FormItem>
                  <FormItem label="奖励设置" required>
                    {data.energyValueList?.map((item, index) => (
                      <FormItem className={styles.formLineHeight}>
                        翻开
                        <FormItem
                          required
                          requiredMessage="请输入卡片数"
                          className={styles.formInLine}
                          validator={checkCardNum}
                        >
                          <NumberPicker
                            // @ts-ignore
                            name={`cardNum${item.id}`}
                            min={1}
                            max={data.cardNumType}
                            type="inline"
                            value={item.cardNum}
                            onChange={(cardNum: number) => {
                              const { energyValueList } = data;
                              energyValueList[index].cardNum = cardNum;
                              setData({ energyValueList });
                            }}
                          />
                        </FormItem>
                        张卡片，可获得
                        <FormItem required requiredMessage="请输入能量值" className={styles.formInLine}>
                          <NumberPicker
                            // @ts-ignore
                            name={`energyValue${item.id}`}
                            min={1}
                            type="inline"
                            value={item.energyValue}
                            onChange={(energyValue: number) => {
                              const { energyValueList } = data;
                              energyValueList[index].energyValue = energyValue;
                              setData({ energyValueList });
                            }}
                          />
                        </FormItem>
                        能量值{" "}
                        {data.energyValueList.length > 1 && (
                          <Button
                            text
                            type="primary"
                            onClick={() => {
                              const { energyValueList } = data;
                              energyValueList.splice(index, 1);
                              setData({ energyValueList });
                            }}
                          >
                            <i className={`iconfont icon-shanchu`} />
                          </Button>
                        )}
                      </FormItem>
                    ))}
                    <Button
                      type="primary"
                      onClick={() => {
                        const { energyValueList } = data;
                        energyValueList.push({
                          id: energyValueList[energyValueList.length - 1].id + 1, // 最后一个id+1
                          cardNum: "",
                          energyValue: ""
                        });
                        setData({ energyValueList });
                      }}
                      disabled={data.energyValueList.length >= data.cardNumType}
                    >
                      添加奖励
                    </Button>
                  </FormItem>
                  <div className="crm-label">卡面正面配置（横排）</div>
                  <FormItem label="中奖概率">{allProbability}</FormItem>
                  <div className={styles.cardList}>
                    {data.cardList?.map((item, index) => (
                      <div className={styles.imgUpload}>
                        <FormItem className={styles.formInLine} required requiredMessage="请选择图片">
                          <LzImageSelector
                            bgWidth={80}
                            bgHeight={80}
                            width={cardTypeConfig[data.cardNumType].width}
                            height={cardTypeConfig[data.cardNumType].height}
                            value={item.front}
                            onChange={(front) => {
                              const { cardList } = data;
                              cardList[index].front = front;
                              setData({ cardList });
                              field.setError(`cardFront${item.id}`, "");
                            }}
                          />
                          <Input
                            value={item.front}
                            name={`cardFront${item.id}`}
                            className={`validateInput ${styles.validateInput}`}
                          />
                        </FormItem>
                        <div className={styles.tip}>
                          <FormItem required requiredMessage="请输入名称" name={`cardName${item.id}`}>
                            名称：
                            <Input
                              name={`cardName${item.id}`}
                              maxLength={10}
                              showLimitHint
                              className={styles.cardName}
                              value={item.name}
                              onChange={(name) => {
                                const { cardList } = data;
                                cardList[index].name = name;
                                setData({ cardList });
                              }}
                            />
                          </FormItem>
                          <FormItem required requiredMessage="请输入中奖概率" name={`probability${item.id}`}>
                            中奖概率：
                            <NumberPicker
                              className={styles.cardName}
                              // @ts-ignore
                              name={`probability${item.id}`}
                              min={0}
                              max={100}
                              step={1}
                              precision={2}
                              type="inline"
                              value={item.probability}
                              onChange={(probability: number) => {
                                const newCardList = [...data.cardList];
                                newCardList[index].probability = probability;
                                const totalProbability = newCardList.reduce((acc, cur) => acc + (cur.probability || 0), 0);
                                if (totalProbability > 100) {
                                  Message.error("概率总和不能大于100!");
                                  newCardList[index].probability = "";
                                }
                                setData({ cardList: newCardList });
                              }}
                            />
                          </FormItem>
                        </div>
                      </div>
                    ))}
                  </div>
                  <div className={styles.tip}>
                    <div>
                      图片尺寸：{cardTypeConfig[data.cardNumType].width}*{cardTypeConfig[data.cardNumType].height}px
                    </div>
                    <div>图片格式：大小不超过1M图片类型为jpg、png</div>
                  </div>
                  <div className="crm-label">卡面背面配置（横排）</div>
                  <div className={styles.cardList}>
                    {data.cardList?.map((item, index) => (
                      <div className={styles.imgUpload}>
                        <FormItem className={styles.formInLine} required requiredMessage="请选择图片"
                                  name={`back${item.id}`}>
                          <LzImageSelector
                            bgWidth={80}
                            bgHeight={80}
                            width={cardTypeConfig[data.cardNumType].width}
                            height={cardTypeConfig[data.cardNumType].height}
                            value={item.back}
                            onChange={(back) => {
                              const { cardList } = data;
                              cardList[index].back = back;
                              field.setError(`back${item.id}`, "");
                              setData({ cardList });
                            }}
                          />
                          <Input
                            value={item.front}
                            name={`back${item.id}`}
                            className={`validateInput ${styles.validateInput}`}
                          />
                        </FormItem>
                        <div className={styles.tip}>
                          <div className={styles.cardNamePre}>名称：{item.name}</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </Form>
              </div>
              <div className={styles.MemberContainer}>
                <div className="crm-label">规则设置</div>
                <Form {...formItemLayout} field={ruleField}>
                  <FormItem label="活动规则" required requiredMessage="请输入活动规则说明">
                    <Input.TextArea
                      value={data.ruleText}
                      name="rules"
                      onChange={(ruleText) => setData({ ruleText })}
                      autoHeight={{ minRows: 8, maxRows: 40 }}
                      placeholder="请输入活动规则说明"
                      maxLength={100}
                      showLimitHint
                      className="form-input-ctrl"
                    />
                  </FormItem>
                </Form>
              </div>
            </div>
          </Loading>
        </Card.Content>
      </Card>
    </div>
  );
};
