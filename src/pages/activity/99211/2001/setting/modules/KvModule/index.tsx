import React from 'react';
import { Card, Button, Divider, Loading, Message, Form, Input, Field,Tab } from '@alifd/next';
import styles from './index.module.scss';
import LzImageSelector from '@/components/LzImageSelector';
import LzColorPicker from '@/components/LzColorPicker';
import { FormLayout } from '../../utils';
import { activityEditDisabled } from "@/utils";

const FormItem = Form.Item;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};
export default ({ data, dispatch, id }) =>{
  const field: Field = Field.useField();
  const [loading, setLoading] = React.useState(false);

  const saveSetting = (): any => {
    if (!data.ruleText){
      Message.error('请输入活动规则说明');
      return;
    }
    if (!data.energyValue){
      Message.error('请输入能量值');
      return;
    }
    if (!data.memberPoint){
      Message.error('请输入会员积分');
      return;
    }
    setLoading(true);
    Message.success(`${data.name}模块保存成功`);
    dispatch({ type: 'RESET_PUSH' });
    setLoading(false);
  };

  const setData = (value, isUpdated = true) => {
    dispatch({ type: 'UPDATE_MODULE', payload: value, isUpdated });
  };

  const commonProps = {
    title: data.name,
    extra: (
      <div>
        <Button type="primary" onClick={saveSetting}>
          保存当前设置
        </Button>
      </div>
    ),
  };
  return (
    <div>
      <Card free>
        <Card.Header {...commonProps} />
        <Divider />
        <Card.Content>
          <Loading visible={loading} style={{ width: '100%' }}>
            <div className={styles.operation}>
                <Tab>
                  <Tab.Item title="装修设置" key="index0">
                    <div className={styles.memberBgContainer}>
                      <div className="crm-label">活动kv</div>
                      <div className={styles.imgUpload}>
                        <LzImageSelector
                          bgWidth={200}
                          bgHeight={140}
                          width={750}
                          height={900}
                          value={data.pageBg}
                          onChange={(pageBg) => {
                            const updatedData = { ...data };
                            updatedData.pageBg = pageBg;
                            setData(updatedData);
                          }}
                        />
                        <div className={styles.tip}>
                          <div>图片尺寸宽度为750px,高度为900px</div>
                          <div>图片格式：大小不超过1M图片类型为jpg、png</div>
                        </div>
                      </div>
                    </div>
                    <div className={styles.colorContainer}>
                  <div className="crm-label">页面颜色配置</div>
                  <div className={styles.colorPicker}>
                    <div>
                      <span>活动规则按钮颜色</span>
                      <LzColorPicker
                        value={data.ruleBtnColor || "#000000"}
                        onChange={(ruleBtnColor) => {
                          const updatedData = { ...data };
                          updatedData.ruleBtnColor = ruleBtnColor;
                          setData(updatedData);
                        }}
                      />
                    </div>
                    <div>
                      <span>活动规则文字颜色</span>
                      <LzColorPicker
                        value={data.ruleTextColor || "#000000"}
                        onChange={(ruleTextColor) => {
                          const updatedData = { ...data };
                          updatedData.ruleTextColor = ruleTextColor;
                          setData(updatedData);
                        }}
                      />
                    </div>
                    <div>
                      <span>加入会员按钮颜色</span>
                      <LzColorPicker
                        value={data.memberBtnColor || "#000000"}
                        onChange={(memberBtnColor) => {
                          const updatedData = { ...data };
                          updatedData.memberBtnColor = memberBtnColor;
                          setData(updatedData);
                        }}
                      />
                    </div>
                    <div>
                      <span>加入会员文字颜色</span>
                      <LzColorPicker
                        value={data.memberTextColor || "#000000"}
                        onChange={(memberTextColor) => {
                          const updatedData = { ...data };
                          updatedData.memberTextColor = memberTextColor;
                          setData(updatedData);
                        }}
                      />
                    </div>
                    <div>
                      <span>其他文字颜色</span>
                      <LzColorPicker
                        value={data.otherTextColor || "#000000"}
                        onChange={(otherTextColor) => {
                          const updatedData = { ...data };
                          updatedData.otherTextColor = otherTextColor;
                          setData(updatedData);
                        }}
                      />
                    </div>
                  </div>
                </div>
                  </Tab.Item>
                  <Tab.Item title="页面设置" key="index1">
                    <div className={styles.MemberContainer}>
                      <div className="crm-label">规则设置</div>
                      <Form {...formItemLayout} field={field}>
                        <FormItem label="活动规则" required requiredMessage="请输入活动规则说明">
                          <Input.TextArea
                            value={data.ruleText}
                            name="rules"
                            onChange={(ruleText) => setData({ ruleText })}
                            autoHeight={{ minRows: 8, maxRows: 40 }}
                            placeholder="请输入活动规则说明"
                            maxLength={100}
                            showLimitHint
                            className="form-input-ctrl"
                          />
                        </FormItem>
                      </Form>
                    </div>
                    <div className={styles.MemberContainer}>
                  <div className="crm-label">能量值兑换会员积分设置</div>
                    <Input
                      label="能量值"
                      width={200}
                      value={data.energyValue}
                      disabled={activityEditDisabled()}
                      onChange={(energyValue) => setData({ energyValue })}
                    />
                  <span style={{ marginLeft: 10, marginRight: 10 }}>:</span>
                  <Input
                    width={200}
                    disabled={activityEditDisabled()}
                    onChange={(memberPoint) => setData({ memberPoint })}
                    innerAfter={'会员积分'}
                    value={data.memberPoint}
                  />
                </div>
                  </Tab.Item>
                </Tab>
            </div>
          </Loading>
        </Card.Content>
      </Card>
    </div>
  );
}

