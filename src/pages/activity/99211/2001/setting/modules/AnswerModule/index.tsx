import React, { useState } from "react";
import {
  <PERSON>,
  Button,
  Divider,
  Loading,
  Message,
  Table,
  Tab,
  NumberPicker,
  Dialog,
  Form,
  Radio,
  Input, Upload
} from "@alifd/next";
import styles from "./index.module.scss";
import LzImageSelector from "@/components/LzImageSelector";
import LzColorPicker from "@/components/LzColorPicker";
import LzDialog from "@/components/LzDialog";
import QuestionEdit from "../../compoonets/QuestionEdit";
import { activityEditDisabled, deepCopy, downloadExcel } from "@/utils";
import { quesTemplateExport} from "@/api/v99211";
import { config } from "ice";
import CONST from "@/utils/constant";

const FormItem = Form.Item;
// 封装答案单元格组件
const AnswerCell = ({ index, data }) => {
  const answer = data.answerResponses?.[index];
  const answerName = answer?.answerName ?? "--";
  const isCorrect = answer?.rightFlag === 1;

  return (
    <div style={{ display: "flex", alignItems: "center" }}>
      {answerName}
      {isCorrect && <div style={{ color: "red", fontSize: "10px" }}>（正确答案）</div>}
    </div>
  );
};
export default ({ data, dispatch, formData }) => {
  const [loading, setLoading] = useState(false);

  // 编辑问题弹窗
  const [showQuestionDialog, setShowQuestionDialog] = useState(false);
  const [editQuestionType, setEditQuestionType] = useState(1); // 问题类型
  const [selectQuestionEditData, setSelectQuestionEditData] = useState(null);
  const [selectQuestionTarget, setSelectQuestionTarget] = useState(-1);

  // @ts-ignore
  const [uploaderRef, setUploaderRef] = useState(false);
  const onSelectQuestionChange = (datas) => {
    const updatedData = { ...data };
    updatedData.questionsList.push(datas);
    setData(updatedData);
    setShowQuestionDialog(false);
  };
  const addQuestionOnChange = (value: any) => {
    console.log(value, "新建问题");
    setSelectQuestionTarget(-1); // 新增问题的时候将修改的问题下表修改为0
    setEditQuestionType(value);
    setSelectQuestionEditData(null);
    setShowQuestionDialog(true);
  };
  const saveSetting = (): any => {
    if (!data.questionsList.length) {
      Message.error("请配置答题问题");
      return;
    }
    if (!data.prizeList.length) {
      Message.error("请配置答题奖励");
      return;
    }
    if (data.prizeList.some((item) => !item.correctCount || !item.energyValue)) {
      Message.error("请填写完整答题奖励信息");
      return;
    }
    if (data.prizeList.length > data.questionsList.length) {
      Message.error("答题奖励数量不能大于答题问题总数量");
      return;
    }
    if (!data.ruleText){
      Message.error("请填写活动规则");
      return;
    }
    setLoading(true);
    Message.success(`${data.name}模块保存成功`);
    dispatch({ type: "RESET_PUSH" });
    setLoading(false);
  };

  const setData = (value, isUpdated = true) => {
    dispatch({ type: "UPDATE_MODULE", payload: value, isUpdated });
  };
const downloadQuestionTemplate =async () => {
  try {
    const data: any = await quesTemplateExport();
    downloadExcel(data, '答题导入模板');
  } catch (error) {
    Message.error(error.message);
  }
}
  const saveUploaderRef = (ref) => {
    if (!ref) {
      return;
    }
    setUploaderRef(ref.getInstance());
  };
  const setTemporarySeriesList = async (value) => {

    Dialog.success({
      title: '导入结果',
      content: (
        <div>
          <p>导入成功</p>
        </div>
      ),
      onOk: () => {
        const updatedData = { ...data };
        updatedData.questionsList = value;
        setData(updatedData);
      },
    });
  };
  const clearFileAndProductData = () => {
    const updatedData = { ...data };
    updatedData.fileList = [];
    updatedData.questionsList = [];
    return updatedData;
  };
  const commonProps = {
    title: data.name,
    extra: (
      <div>
        <Button type="primary" onClick={saveSetting}>
          保存当前设置
        </Button>
      </div>
    )
  };
  return (
    <div>
      <Card free>
        <Card.Header {...commonProps} />
        <Divider />
        <Card.Content>
          <Loading visible={loading} style={{ width: "100%" }}>
            <div className={styles.operation}>
              <Tab>
                <Tab.Item title="答题首页设置" key="index0">
                  <div className={styles.memberBgContainer}>
                    <div className="crm-label">活动背景</div>
                    <div className={styles.imgUpload}>
                      <LzImageSelector
                        bgWidth={200}
                        bgHeight={140}
                        width={750}
                        height={520}
                        value={data.answerPageBg}
                        onChange={(answerPageBg) => {
                          const updatedData = { ...data };
                          updatedData.answerPageBg = answerPageBg;
                          setData(updatedData);
                        }}
                      />
                      <div className={styles.tip}>
                        <div>图片尺寸为750px*520px</div>
                        <div>图片格式：大小不超过1M图片类型为jpg、png</div>
                      </div>
                    </div>
                  </div>
                  <div className={styles.colorContainer}>
                    <div className={styles.colorPicker}>
                      <div className={styles.colorPickerItem}>
                        <span>开始挑战文字颜色</span>
                        <LzColorPicker
                          value={data.challengeTextColor || "#000000"}
                          onChange={(challengeTextColor) => {
                            const updatedData = { ...data };
                            updatedData.challengeTextColor = challengeTextColor;
                            setData(updatedData);
                          }}
                        />
                      </div>
                      <div className={styles.colorPickerItem}>
                        <span>开始挑战按钮颜色</span>
                        <LzColorPicker
                          value={data.challengeBtnColor || "#000000"}
                          onChange={(challengeBtnColor) => {
                            const updatedData = { ...data };
                            updatedData.challengeBtnColor = challengeBtnColor;
                            setData(updatedData);
                          }}
                        />
                      </div>
                      <div className={styles.colorPickerItem}>
                        <span>弹窗按钮文字颜色</span>
                        <LzColorPicker
                          value={data.dialogTextColor || "#000000"}
                          onChange={(dialogTextColor) => {
                            const updatedData = { ...data };
                            updatedData.dialogTextColor = dialogTextColor;
                            setData(updatedData);
                          }}
                        />
                      </div>
                      <div className={styles.colorPickerItem}>
                        <span>弹窗按钮颜色</span>
                        <LzColorPicker
                          value={data.dialogBtnColor || "#000000"}
                          onChange={(dialogBtnColor) => {
                            const updatedData = { ...data };
                            updatedData.dialogBtnColor = dialogBtnColor;
                            setData(updatedData);
                          }}
                        />
                      </div>
                    </div>
                  </div>
                </Tab.Item>
                <Tab.Item title="答题过程设置" key="index1">
                  <div style={{margin: "10px"}}>
                    <Radio.Group onChange={(value) => {
                      const updatedData = { ...data };
                      updatedData.pageType = value;
                      setData(updatedData);
                    }}>
                      <Radio id="2" value={2}>
                        答题页面
                      </Radio>
                      <Radio id="3" value={3}>
                        回答正确
                      </Radio>
                      <Radio id="4" value={4}>
                        回答错误
                      </Radio>
                    </Radio.Group>
                  </div>
                  <div className={styles.memberBgContainer}>
                    <div className="crm-label">活动背景</div>
                    <div className={styles.imgUpload}>
                      <LzImageSelector
                        bgWidth={200}
                        bgHeight={140}
                        width={750}
                        height={520}
                        value={data.answeringPageBg}
                        onChange={(answeringPageBg) => {
                          const updatedData = { ...data };
                          updatedData.answeringPageBg = answeringPageBg;
                          setData(updatedData);
                        }}
                      />
                      <div className={styles.tip}>
                        <div>图片尺寸为750px*520px</div>
                        <div>图片格式：大小不超过1M图片类型为jpg、png</div>
                      </div>
                    </div>
                  </div>
                  <div className={styles.colorContainer}>
                    <div className={styles.colorPicker}>
                      <div className={styles.colorPickerItem}>
                        <span>选项按钮颜色</span>
                        <LzColorPicker
                          value={data.optionBtnColor || "#000000"}
                          onChange={(optionBtnColor) => {
                            const updatedData = { ...data };
                            updatedData.optionBtnColor = optionBtnColor;
                            setData(updatedData);
                          }}
                        />
                      </div>
                      <div className={styles.colorPickerItem}>
                        <span>选项文字颜色</span>
                        <LzColorPicker
                          value={data.optionTextColor || "#000000"}
                          onChange={(optionTextColor) => {
                            const updatedData = { ...data };
                            updatedData.optionTextColor = optionTextColor;
                            setData(updatedData);
                          }}
                        />
                      </div>
                      <div className={styles.colorPickerItem}>
                        <span>选中选项按钮颜色</span>
                        <LzColorPicker
                          value={data.optionBtnSelectedColor || "#000000"}
                          onChange={(optionBtnSelectedColor) => {
                            const updatedData = { ...data };
                            updatedData.optionBtnSelectedColor = optionBtnSelectedColor;
                            setData(updatedData);
                          }}
                        />
                      </div>
                      <div className={styles.colorPickerItem}>
                        <span>选中选项字体颜色</span>
                        <LzColorPicker
                          value={data.optionTextSelectedColor || "#000000"}
                          onChange={(optionTextSelectedColor) => {
                            const updatedData = { ...data };
                            updatedData.optionTextSelectedColor = optionTextSelectedColor;
                            setData(updatedData);
                          }}
                        />
                      </div>
                      <div className={styles.colorPickerItem}>
                        <span>继续答题按钮颜色</span>
                        <LzColorPicker
                          value={data.continueBtnColor || "#000000"}
                          onChange={(continueBtnColor) => {
                            const updatedData = { ...data };
                            updatedData.continueBtnColor = continueBtnColor;
                            setData(updatedData);
                          }}
                        />
                      </div>
                      <div className={styles.colorPickerItem}>
                        <span>继续答题文字颜色</span>
                        <LzColorPicker
                          value={data.continueTextColor || "#000000"}
                          onChange={(continueTextColor) => {
                            const updatedData = { ...data };
                            updatedData.continueTextColor = continueTextColor;
                            setData(updatedData);
                          }}
                        />
                      </div>
                      <div className={styles.colorPickerItem}>
                        <span>高亮文字颜色</span>
                        <LzColorPicker
                          value={data.highlightTextColor || "#000000"}
                          onChange={(highlightTextColor) => {
                            const updatedData = { ...data };
                            updatedData.highlightTextColor = highlightTextColor;
                            setData(updatedData);
                          }}
                        />
                      </div>
                      <div className={styles.colorPickerItem}>
                        <span>普通文字颜色</span>
                        <LzColorPicker
                          value={data.otherTextColor || "#000000"}
                          onChange={(otherTextColor) => {
                            const updatedData = { ...data };
                            updatedData.otherTextColor = otherTextColor;
                            setData(updatedData);
                          }}
                        />
                      </div>
                    </div>
                  </div>
                </Tab.Item>
                <Tab.Item title="题目配置" key="index2">
                  <div style={{ display: "flex", width: "100%", justifyContent: "space-between" }}>
                    <div style={{display:'flex',width:'200px',justifyContent:'space-between'}}>
                      <Button
                        type="primary"
                        onClick={addQuestionOnChange}
                        disabled={activityEditDisabled()}
                      >
                        新增题目
                      </Button>
                      <Button
                        type="primary"
                        onClick={downloadQuestionTemplate}
                        disabled={activityEditDisabled()}
                      >
                        下载题库模板
                      </Button>
                    </div>
                    <Upload
                      action={`${config.baseURL}/99211/quesTemplate/import`}
                      name="file"
                      method="post"
                      headers={{
                        token: localStorage.getItem(CONST.LZ_SSO_TOKEN),
                        prd: localStorage.getItem(CONST.LZ_SSO_PRD),
                      }}
                      ref={saveUploaderRef}
                      value={data.fileList}
                      fileNameRender={(file) => <span>{data.fileName}</span>}
                      limit={1}
                      listType="text"
                      accept=".xls,.xlsx"
                      onChange={(info) => {
                        if (info && info.length > 0) {
                          const file = info[0];
                          if (!file || !file.size) {
                            Message.error('文件无效');
                            return;
                          }
                          if (file?.size > 5 * 1024 * 1024) {
                            Message.error('文件大小不能超过5M');
                            return;
                          }
                          const updatedData= {...data}
                          updatedData.fileName = file.name;
                          updatedData.fileList = info;
                          setData(updatedData)
                        } else {
                          setData(clearFileAndProductData());
                        }
                      }}
                      onError={(res:any) => {
                        Message.error(res?.response?.message || '文件错误，请上传正确的文件');
                      }}
                      onRemove={() => {
                        setData(clearFileAndProductData());
                        Message.error('删除成功，请重新上传');
                      }}
                      onSuccess={(res:any) => {
                        if (res.response.code === 200) {
                          setTemporarySeriesList(res.response.data);
                        } else {
                          setData(clearFileAndProductData());
                          Message.error(res.response?.message || '文件错误，请上传正确的文件');
                        }
                      }}
                      style={{ marginBottom: 10 }}
                    >
                      <Button type="primary"
                              disabled={activityEditDisabled()}
                              >
                        批量导入题库
                      </Button>
                    </Upload>
                  </div>
                  <Table.StickyLock dataSource={data.questionsList} style={{ marginTop: "10px" }}>
                    <Table.Column title="题目" dataIndex="questionName" lock="left" width={120} align={"center"} />
                    <Table.ColumnGroup title="答案" style={{ textAlign: "center" }}>
                      {["A", "B", "C", "D"].map((option, idx) => (
                        <Table.Column
                          align={"center"}
                          key={option}
                          title={`选项${option}`}
                          width={160}
                          cell={(value, index, data) => <AnswerCell index={idx} data={data} />}
                        />
                      ))}
                    </Table.ColumnGroup>
                    {
                      !activityEditDisabled() &&
                      <Table.Column
                        title="操作"
                        width={130}
                        align={"center"}
                        lock="right"
                        cell={(val, index, _) => (
                          <Form.Item>
                            {data.questionsList[index].questionName && (
                              <Button
                                text
                                type="primary"
                                onClick={() => {
                                  let row = data.questionsList[index];
                                  if (row.questionName === "" || !row.questionName) {
                                    row = null;
                                  }
                                  setEditQuestionType(row.questionType);
                                  setSelectQuestionEditData(deepCopy(row));
                                  setSelectQuestionTarget(index);
                                  setShowQuestionDialog(true);
                                }}
                              >
                                <i className={`iconfont icon-chaojiyingxiaoicon-25`} />
                              </Button>
                            )}
                            <Button
                              text
                              type="primary"
                              onClick={() => {
                                Dialog.confirm({
                                  v2: true,
                                  title: "提示",
                                  centered: true,
                                  content: "确认删除该问题？",
                                  onOk: () => {
                                    data.questionsList.splice(index, 1);
                                    setData(formData);
                                  },
                                  onCancel: () => console.log("cancel")
                                } as any);
                              }}
                            >
                              <i className={`iconfont icon-shanchu`} />
                            </Button>
                          </Form.Item>
                        )}
                      />
                    }

                  </Table.StickyLock>
                </Tab.Item>
                <Tab.Item title="奖品配置" key="index3">
                  <Button
                    style={{ marginBottom: 20 }}
                    type="primary"
                    disabled={data.prizeList.length >= 10 || activityEditDisabled()}
                    onClick={() => {
                      if (data.prizeList.length >= 10) {
                        Message.error("签到天数已达到上限");
                        return;
                      }
                      const updatedData = { ...data };
                      updatedData.prizeList.push({
                        correctCount: updatedData.prizeList.length + 1,
                        energyValue: (updatedData.prizeList.length + 1) * 10
                      });
                      setData(updatedData);

                    }}>增加奖项</Button>
                  <Table
                    dataSource={data.prizeList}
                    fixedHeader
                    maxBodyHeight={500}>
                    <Table.Column title="连续答对题目数量"
                                  align={"center"}
                                  cell={(value, index, record) => {
                                    return (
                                      <div>
                                        <NumberPicker
                                          className={styles.formNumberPicker}
                                          type="inline"
                                          precision={0}
                                          max={10}
                                          min={0}
                                          disabled
                                          value={record.correctCount}
                                          onChange={(correctCount) => {
                                            if (typeof correctCount !== "number" || isNaN(correctCount)) {
                                              Message.error("请输入有效的签到天数");
                                              return;
                                            }

                                            if (index > 0) {
                                              const prevDay = data.prizeList[index - 1].correctCount;
                                              if (correctCount <= prevDay) {
                                                Message.error("当前签到天数必须大于上一行的签到天数");
                                                return;
                                              }
                                            }
                                            const updatedData = { ...data };
                                            updatedData.prizeList[index].correctCount = correctCount;
                                            setData(updatedData);
                                          }}
                                        />
                                      </div>
                                    );
                                  }} />
                    <Table.Column
                      title="可获得能量值数量"
                      align={"center"}
                      cell={(value, index, record) => {
                        return (
                          <div>
                            <NumberPicker
                              className={styles.formNumberPicker}
                              type="inline"
                              precision={0}
                              max={99999}
                              min={0}
                              value={record.energyValue}
                              onChange={(energy) => {
                                if (typeof energy !== "number" || isNaN(energy)) {
                                  Message.error("请输入有效的能量值");
                                  return;
                                }
                                const updatedData = { ...data };
                                updatedData.prizeList[index].energyValue = energy;
                                setData(updatedData);
                              }}
                            />
                          </div>
                        );
                      }} />
                    {
                      !activityEditDisabled() &&
                      <Table.Column
                        title="操作"
                        align={"center"}
                        width={100}
                        cell={(value, index, record) => {
                          return (
                            <div>
                              <Button type="primary"
                                      disabled={index !== data.prizeList.length - 1}
                                      onClick={() => {
                                        Dialog.confirm({
                                          title: "删除",
                                          content: "确定删除该奖励吗？",
                                          onOk: () => {
                                            if (data.prizeList.length === 1) {
                                              Message.error("至少需要一个奖励");
                                              return;
                                            }
                                            const updatedData = { ...data };
                                            updatedData.prizeList.splice(index, 1);
                                            setData(updatedData);
                                          }
                                        });
                                      }}>删除</Button>
                            </div>
                          );
                        }} />
                    }
                  </Table>
                </Tab.Item>
                <Tab.Item title="规则配置" key="index4">
                  <div className={styles.MemberContainer}>
                    <div className="crm-label">规则设置</div>
                    <Form labelCol={{ span: 4 }}
                                  wrapperCol={{ span: 20 }}
                                  labelAlign="left"
                                  colon>
                      <FormItem label="活动规则" required requiredMessage="请输入活动规则说明">
                        <Input.TextArea
                          value={data.ruleText}
                          name="rules"
                          onChange={(ruleText) => setData({ ruleText })}
                          autoHeight={{ minRows: 8, maxRows: 40 }}
                          placeholder="请输入活动规则说明"
                          maxLength={100}
                          showLimitHint
                          className="form-input-ctrl"
                        />
                      </FormItem>
                    </Form>
                  </div>
                </Tab.Item>
              </Tab>
              <div>
                <LzDialog
                  title="新增问题"
                  className="lz-dialog-mini"
                  visible={showQuestionDialog}
                  footer={false}
                  style={{ width: "870px" }}
                  onCancel={() => {
                    setShowQuestionDialog(false);
                  }}
                  onClose={() => {
                    setShowQuestionDialog(false);
                  }}
                >
                  <QuestionEdit
                    editTarget={selectQuestionTarget}
                    questionType={editQuestionType}
                    // @ts-ignore
                    editValueData={selectQuestionEditData}
                    onSubmit={onSelectQuestionChange}
                    cancel={() => {
                      setShowQuestionDialog(false);
                    }}
                  />
                </LzDialog>
              </div>
            </div>
          </Loading>
        </Card.Content>
      </Card>
    </div>
  );
}

