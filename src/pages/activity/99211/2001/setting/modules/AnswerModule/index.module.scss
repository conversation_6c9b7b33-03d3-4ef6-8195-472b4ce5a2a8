.operation {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15px;

  .container {
    padding: 15px;
    background: #F4F6F9;
    border-radius: 5px;
  }


  .memberBgContainer {
    @extend .container;
    .imgUpload {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-bottom: 10px;

      .tip {
        margin-top: 10px;
      }
    }
  }
  .colorContainer {
    @extend .container;

    .colorPicker {
      display: flex;
      margin-top: 10px;
      gap: 20px;
      flex-wrap: wrap;
      .colorPickerItem {
        width: 200px;
        span {
          font-weight: bold;
          margin-right: 10px;
        }
      }
    }
  }
  .MemberContainer {
    @extend .container;
  }
}
