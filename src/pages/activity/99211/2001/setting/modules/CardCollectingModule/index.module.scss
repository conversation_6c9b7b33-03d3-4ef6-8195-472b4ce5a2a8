.operation {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15px;

  .container {
    padding: 15px;
    background: #F4F6F9;
    border-radius: 5px;
  }


  .memberBgContainer {
    @extend .container;
    .imgUpload {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-bottom: 10px;

      .tip {
        margin-top: 10px;
      }
    }
  }
  .colorContainer {
    @extend .container;

    .colorPicker {
      display: flex;
      margin-top: 10px;
      gap: 20px;
      flex-wrap: wrap;
      .colorPickerItem {
        width: 200px;
        span {
          font-weight: bold;
          margin-right: 10px;
        }
      }
    }
  }
  .MemberContainer {
    @extend .container;
  }
  .panel {
    box-sizing: border-box;
    background: #d7dde4;
    padding: 15px;
    border: 1px solid #d7dde4;
    min-width: 350px;
  }
  .number {
    margin: 0 10px;
  }
  .tip {
    font-size: 12px;
    color: gray;
    margin-top: 15px;
  }
  .tips {
    font-size: 12px;
  }

}
