import React, { useState } from "react";
import {
  Card,
  Button,
  Divider,
  Loading,
  Message,
  Table,
  Tab,
  NumberPicker,
  Form,
  Dialog,
  Input
} from "@alifd/next";
import styles from "./index.module.scss";
import LzImageSelector from "@/components/LzImageSelector";
import LzColorPicker from "@/components/LzColorPicker";
import LzDialog from "@/components/LzDialog";
import CardSettings from "../../compoonets/CardSettings";
import { activityEditDisabled } from "@/utils";

const FormItem = Form.Item;
// 封装答案单元格组件
export default ({ data, dispatch, formData }) => {
  const [loading, setLoading] = useState(false);
  // 当前编辑行数据
  const [editValue, setEditValue] = useState(null);

  const [editIndex, setEditIndex] = React.useState(0);
  const [visible, setVisible] = React.useState(false);
  const saveSetting = (): any => {
    if (!data.cardList.length) {
      Message.error("请配置卡片信息");
      return;
    }
    if (data.cardList.length<2) {
      Message.error("请配置普通卡片信息");
      return;
    }
    if (data.cardList.some((e: any): boolean => e.probability === '')) {
      Message.error("请填写完整卡片信息");
      return;
    }
    if (data.cardList.reduce((acc, cur) => acc + Number(cur.probability), 0) > 100) {
      Message.error("卡片概率总和不能大于100");
      return;
    }
    if (!data.cardCountLimitDay) {
      Message.error("请填写每人每日抽卡次数");
      return;
    }
    if (!data.useSingleCount){
      Message.error("请填写单次兑换卡片数量");
      return;
    }
    if(!data.cardEnergyValueOfCards){
      Message.error("请填写单次兑换卡片所获得能量值");
      return;
    }
    if(!data.exchangeCountsOfCard){
      Message.error("请填写单次兑换卡片可兑换次数");
      return;
    }
    if (!data.cardEnergyValueOfAllCards) {
      Message.error("请填写全套卡片可获得能量值");
      return;
    }
    if(!data.exchangeCountsOfAllCard){
      Message.error("请填写全套卡片可兑换次数");
      return;
    }

    if (!data.ruleText) {
      Message.error("请填写活动规则");
      return;
    }


    setLoading(true);
    Message.success(`${data.name}模块保存成功`);
    dispatch({ type: "RESET_PUSH" });
    setLoading(false);
  };

  const setData = (value, isUpdated = true) => {
    dispatch({ type: "UPDATE_MODULE", payload: value, isUpdated });
  };
  const onSubmit = (val, type) => {
    const updatedData = { ...data };
    if (type === "add") {
      updatedData.cardList.push({...val,cardType:2});
      setData(updatedData);
    } else {
      updatedData.cardList[editIndex] = val;
      setData(updatedData);
    }
  };
  const commonProps = {
    title: data.name,
    extra: (
      <div>
        <Button type="primary" onClick={saveSetting}>
          保存当前设置
        </Button>
      </div>
    )
  };
  return (
    <div>
      <Card free>
        <Card.Header {...commonProps} />
        <Divider />
        <Card.Content>
          <Loading visible={loading} style={{ width: "100%" }}>
            <div className={styles.operation}>
              <Tab>
                <Tab.Item title="页面设置" key="index0">
                  <div className={styles.memberBgContainer}>
                    <div className="crm-label">活动背景</div>
                    <div className={styles.imgUpload}>
                      <LzImageSelector
                        bgWidth={200}
                        bgHeight={140}
                        width={750}
                        height={715}
                        value={data.pageBg}
                        onChange={(pageBg) => {
                          const updatedData = { ...data };
                          updatedData.pageBg = pageBg;
                          setData(updatedData);
                        }}
                      />
                      <div className={styles.tip}>
                        <div>图片尺寸为750px*715px</div>
                        <div>图片格式：大小不超过1M图片类型为jpg、png</div>
                      </div>
                    </div>
                  </div>
                  <div className={styles.colorContainer}>
                    <div className={styles.colorPicker}>
                      <div className={styles.colorPickerItem}>
                        <span>弹窗按钮文字颜色</span>
                        <LzColorPicker
                          value={data.dialogTextColor || "#000000"}
                          onChange={(dialogTextColor) => {
                            const updatedData = { ...data };
                            updatedData.dialogTextColor = dialogTextColor;
                            setData(updatedData);
                          }}
                        />
                      </div>
                      <div className={styles.colorPickerItem}>
                        <span>弹窗按钮颜色</span>
                        <LzColorPicker
                          value={data.dialogBtnColor || "#000000"}
                          onChange={(dialogBtnColor) => {
                            const updatedData = { ...data };
                            updatedData.dialogBtnColor = dialogBtnColor;
                            setData(updatedData);
                          }}
                        />
                      </div>

                      <div className={styles.colorPickerItem}>
                        <span>点击抽卡文字颜色</span>
                        <LzColorPicker
                          value={data.cardDrawTextColor || "#000000"}
                          onChange={(cardDrawTextColor) => {
                            const updatedData = { ...data };
                            updatedData.cardDrawTextColor = cardDrawTextColor;
                            setData(updatedData);
                          }}
                        />
                      </div>
                      <div className={styles.colorPickerItem}>
                        <span>点击抽卡按钮颜色</span>
                        <LzColorPicker
                          value={data.cardDrawBtnColor || "#000000"}
                          onChange={(cardDrawBtnColor) => {
                            const updatedData = { ...data };
                            updatedData.cardDrawBtnColor = cardDrawBtnColor;
                            setData(updatedData);
                          }}
                        />
                      </div>

                      <div className={styles.colorPickerItem}>
                        <span>我的卡片文字颜色</span>
                        <LzColorPicker
                          value={data.cardTextColor || "#000000"}
                          onChange={(cardTextColor) => {
                            const updatedData = { ...data };
                            updatedData.cardTextColor = cardTextColor;
                            setData(updatedData);
                          }}
                        />
                      </div>
                      <div className={styles.colorPickerItem}>
                        <span>我的卡片按钮颜色</span>
                        <LzColorPicker
                          value={data.cardBtnColor || "#000000"}
                          onChange={(cardBtnColor) => {
                            const updatedData = { ...data };
                            updatedData.cardBtnColor = cardBtnColor;
                            setData(updatedData);
                          }}
                        />
                      </div>


                      <div className={styles.colorPickerItem}>
                        <span>活动规则文字颜色</span>
                        <LzColorPicker
                          value={data.ruleTextColor || "#000000"}
                          onChange={(ruleTextColor) => {
                            const updatedData = { ...data };
                            updatedData.ruleTextColor = ruleTextColor;
                            setData(updatedData);
                          }}
                        />
                      </div>
                      <div className={styles.colorPickerItem}>
                        <span>其他字体颜色</span>
                        <LzColorPicker
                          value={data.otherColor || "#000000"}
                          onChange={(otherColor) => {
                            const updatedData = { ...data };
                            updatedData.otherColor = otherColor;
                            setData(updatedData);
                          }}
                        />
                      </div>
                    </div>
                  </div>
                </Tab.Item>
                <Tab.Item title="卡片配置" key="index1">
                  <div className={styles.MemberContainer}>
                    <div className="crm-label">卡片配置</div>
                    <Button
                      disabled={data?.cardList?.length >= 20 || activityEditDisabled()}
                      type="primary"
                      onClick={() => {
                        setEditValue(null);
                        setVisible(true);
                      }}
                    >
                      +添加卡片（{data.cardList?.length}/20）
                    </Button>
                    <Table dataSource={data?.cardList} style={{ marginTop: "15px" }}>
                      <Table.Column title="卡片名称" dataIndex="cardName" />
                      <Table.Column title="卡片类型" cell={(value, index, record) =>{
                        return (
                          <div>{record.cardType === 1 ? "稀有卡" : "普通卡"}</div>
                        );
                      }} />
                      <Table.Column title="卡片图" cell={(value, index, record) => {
                        return (
                          <div>
                            <img style={{ width: "100px", height: "100px" }} src={record.img} alt={""} />
                          </div>
                        );
                      }} />
                      <Table.Column
                        title="中奖概率(%)"
                        cell={(_, index, row) => <div>{row.probability ? row.probability : ""}</div>}
                      />
                      {
                        !activityEditDisabled() &&   <Table.Column
                          title="操作"
                          width={130}
                          cell={(val, index, _) => (
                            <FormItem>
                              <Button
                                text
                                type="primary"
                                onClick={() => {
                                  let row = data.cardList[index];
                                  if (row.prizeName === "") {
                                    row = null;
                                  }
                                  setEditValue(row);
                                  setEditIndex(index);
                                  setVisible(true);
                                }}
                              >
                                <i className={`iconfont icon-chaojiyingxiaoicon-25`} />
                              </Button>
                              {data.cardList.length > 1 && (
                                <Button
                                  text
                                  type="primary"
                                  disabled={activityEditDisabled()}
                                  onClick={() => {
                                    Dialog.confirm({
                                      v2: true,
                                      title: "提示",
                                      centered: true,
                                      content: "确认删除该卡片？",
                                      onOk: () => {
                                        data.cardList.splice(index, 1);
                                        setData(data);
                                      },
                                      onCancel: () => console.log("cancel")
                                    } as any);
                                  }}
                                >
                                  <i className={`iconfont icon-shanchu`} />
                                </Button>
                              )}
                            </FormItem>
                          )}
                        />
                      }

                    </Table>
                  </div>
                </Tab.Item>
                <Tab.Item title="抽卡配置" key="index2">
                  <div className={styles.MemberContainer}>
                    <Form labelCol={{ span: 4 }}
                          wrapperCol={{ span: 20 }}
                          labelAlign="left"
                          colon
                    >
                      <FormItem label={"限制设置"} required >
                        <FormItem label={""} disabled={activityEditDisabled()}>
                          <span>1.每人每日抽卡次数</span>
                          <NumberPicker
                            style={{ width: "100px", margin: "0 5px" }}
                            value={data.cardCountLimitDay}
                            onChange={(cardCountLimitDay) => {
                              const updatedData = { ...data };
                              updatedData.cardCountLimitDay = cardCountLimitDay;
                              setData(updatedData);
                            }}
                            type="inline"
                            min={1}
                            max={999}
                          />
                          <span> 次</span>
                        </FormItem>
                        <FormItem label={""} disabled={activityEditDisabled()}>
                          <span>2.消耗</span>
                          <NumberPicker
                            style={{ width: "100px", margin: "0 5px" }}
                            value={data.useSingleCount}
                            onChange={(useSingleCount) => {
                              const updatedData = { ...data };
                              updatedData.useSingleCount = useSingleCount;
                              setData(updatedData);
                            }}
                            type="inline"
                            min={1}
                            max={data.cardList.length}
                          />
                          <span> 张不同卡片可兑换</span>
                          <NumberPicker
                            style={{ width: "100px", margin: "0 5px" }}
                            value={data.cardEnergyValueOfCards}
                            onChange={(cardEnergyValueOfCards) => {
                              const updatedData = { ...data };
                              updatedData.cardEnergyValueOfCards = cardEnergyValueOfCards;
                              setData(updatedData);
                            }}
                            type="inline"
                            min={1}
                            max={999}
                          />
                          <span>能量，活动期间，每人可兑换</span>
                          <NumberPicker
                            style={{ width: "100px", margin: "0 5px" }}
                            value={data.exchangeCountsOfCard}
                            onChange={(exchangeCountsOfCard) => {
                              const updatedData = { ...data };
                              updatedData.exchangeCountsOfCard = exchangeCountsOfCard;
                              setData(updatedData);
                            }}
                            type="inline"
                            min={1}
                            max={999}
                          />
                          <span>次</span>
                        </FormItem>
                        <FormItem label={""} disabled={activityEditDisabled()}>
                          <span>3.消耗全套卡片可兑换</span>
                          <NumberPicker
                            style={{ width: "100px", margin: "0 5px" }}
                            value={data.cardEnergyValueOfAllCards}
                            onChange={(cardEnergyValueOfAllCards) => {
                              const updatedData = { ...data };
                              updatedData.cardEnergyValueOfAllCards = cardEnergyValueOfAllCards;
                              setData(updatedData);
                            }}
                            type="inline"
                            min={1}
                            max={999}
                          />
                          <span>能量，活动期间，每人可兑换</span>
                          <NumberPicker
                            style={{ width: "100px", margin: "0 5px" }}
                            value={data.exchangeCountsOfAllCard}
                            onChange={(exchangeCountsOfAllCard) => {
                              const updatedData = { ...data };
                              updatedData.exchangeCountsOfAllCard = exchangeCountsOfAllCard;
                              setData(updatedData);
                            }}
                            type="inline"
                            min={1}
                            max={999}
                          />
                          <span>次</span>
                        </FormItem>
                      </FormItem>

                      <FormItem label="活动规则" required requiredMessage="请输入活动规则说明">
                        <Input.TextArea
                          value={data.ruleText}
                          name="rules"
                          onChange={(ruleText) => setData({ ruleText })}
                          autoHeight={{ minRows: 8, maxRows: 40 }}
                          placeholder="请输入活动规则说明"
                          maxLength={100}
                          showLimitHint
                          className="form-input-ctrl"
                        />
                      </FormItem>
                    </Form>
                  </div>
                </Tab.Item>
              </Tab>
            </div>
          </Loading>
        </Card.Content>
      </Card>
      <LzDialog
        title={"新增"}
        visible={visible}
        footer={false}
        onClose={() => setVisible(false)}
        style={{ width: "450px" }}
      >
        <CardSettings
          suggestSize={{ height: 298, width: 210 }}
          size={{ height: 298, width: 210 }}
          editValue={editValue}
          onSubmit={onSubmit}
          onClose={() => setVisible(false)}
        />
      </LzDialog>
    </div>
  );
}

