import React from 'react';
import kvModuleDecorate from "../../modules/KvModule";
import signUpModuleDecorate from "../../modules/SignUpModule";
import answerModuleDecorate from "../../modules/AnswerModule";
import prizeDrawModuleDecorate from "../../modules/PrizeDrawModule";
import hotZoneModuleDecorate from "../../modules/HotZoneModule";
import swiperModuleDecorate from "../../modules/SwiperModule";
import hotProductModuleDecorate from "../../modules/HotProductModule";
import WishModuleDecorate from "../../modules/WishModule";
import CardCollectingModuleDecorate from "../../modules/CardCollectingModule";
import flipCardModuleDecorate from "../../modules/FlipCardModule";

const  previews= {
    kvModule: {
      decorateComponent: kvModuleDecorate,
      name: "kv"
    },
     signUpModule: {
      decorateComponent: signUpModuleDecorate,
      name: "签到"
    },
    answerModule: {
      decorateComponent: answerModuleDecorate,
      name: "答题"
    },
    hotZoneModule: {
      decorateComponent:  hotZoneModuleDecorate,
      name: "抽奖"
    },
    prizeDrawModule: {
      decorateComponent:prizeDrawModuleDecorate,
      name: "热区"
    },
    swiperModule: {
      decorateComponent:swiperModuleDecorate,
      name: "轮播"
    },
    hotProductModule: {
      decorateComponent:hotProductModuleDecorate,
      name: "商品"
    },
    wishModule: {
      decorateComponent:WishModuleDecorate,
      name: "许愿"
    },
    cardCollectingModule: {
      decorateComponent:CardCollectingModuleDecorate,
      name: "许愿"
    },
    flipCardModule: {
      decorateComponent: flipCardModuleDecorate,
      name: "翻卡"
    }
  };

export default ({ state, dispatch, moduleName,formData })=> {
  const Components = previews[moduleName].decorateComponent;
  return (
    <Components
      data={state.modules[moduleName]}
      defaultData={state.defaultModule[moduleName]}
      id={state.id}
      dispatch={dispatch}
      formData={formData}
    />
  );
}



