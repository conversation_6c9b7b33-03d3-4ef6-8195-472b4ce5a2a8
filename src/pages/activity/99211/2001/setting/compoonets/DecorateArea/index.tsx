import React, { useEffect } from 'react';
import styles from './index.module.scss';
import DecorateComponent from '../DecorateComponent/index'

export default ({ selectedModule, state, dispatch,formData })=> {
  useEffect(() => {
    console.log(formData,'state');
    console.log(selectedModule,'moduleName');
  }, []);
  return (
    <div className={styles.decorateComponent}>
      {
        selectedModule && (
          <DecorateComponent state={state} dispatch={dispatch} moduleName={selectedModule} formData={formData} />
        )
      }
    </div>
  );
}

