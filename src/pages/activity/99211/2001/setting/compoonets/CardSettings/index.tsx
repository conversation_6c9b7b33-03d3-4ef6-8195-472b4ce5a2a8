import React from 'react';
// @ts-ignore
import styles from './index.module.scss';
import LzImageSelector from '@/components/LzImageSelector';
import { Divider, Button, Input, Form, Field } from "@alifd/next";
import { deepCopy } from "@/utils";

const formItemLayout = {
  labelCol: {
    fixedSpan: 4,
  },
  wrapperCol: {
    span: 14,
  },
};
export default ({ suggestSize = { height: 488, width: 426 }, onSubmit, onClose, editValue, size = {} as any }) => {
  const equityFiled = Field.useField({});
  const [giftInfo, setGiftInfo] = React.useState<any>(editValue ? deepCopy(editValue) : {});

  const onConfirm = () => {
    let err: any = null;
    // @ts-ignore
    equityFiled.validate((errors: Object[]): void => {
      err = errors;
    });
    if (!err) {
      onSubmit(giftInfo, editValue ? 'edit' : 'add');
      setGiftInfo({});
      onClose();
    }
  };
  return (
    <div className={styles.editMemberCard}>
      <Form field={equityFiled} {...formItemLayout} labelAlign={'left'} colon>
        <Form.Item required requiredMessage={'请上传图片'} label={'图片'}>
          <Input htmlType={'hidden'} name={`icon`} value={giftInfo.icon} />
          <LzImageSelector
            name={`icon`}
            value={giftInfo.img}
            width={size?.width}
            height={size?.height}
            onChange={(img) => {
              setGiftInfo({ ...giftInfo, img });
            }}
          />
          <div className={styles.tip}>
            建议尺寸 {suggestSize.width}*{suggestSize.height}px, png格式，500k以内
          </div>
        </Form.Item>

        <Form.Item label="卡片名称" required requiredMessage="请输入卡片名称" >
          <Input
            className={styles.formInputCtrl}
            maxLength={10}
            showLimitHint
            placeholder="请输入卡片名称"
            trim
            value={giftInfo.cardName}
            onChange={(cardName) => {
              setGiftInfo({ ...giftInfo, cardName });
            }}
          />
        </Form.Item>
          <Form.Item label="中奖概率" required requiredMessage="请输入中奖概率">
            <Input
              name="probability"
              value={giftInfo.probability}
              addonTextAfter="%"
              onChange={(probability) => {
                if (probability) {
                  const regex = /^(\d|[1-9]\d|100)(\.\d{0,3})?$/;
                  if (regex.test(probability as any)) {
                    setGiftInfo({ ...giftInfo, probability });
                  }
                } else {
                setGiftInfo({ ...giftInfo, probability: '' })
                }
              }}
              className={styles.formInputCtrl}
            />
          </Form.Item>
      </Form>
      <Divider />
      <div className={styles.footer}>
        <Button type={'primary'} onClick={onConfirm}>
          确定
        </Button>
        <Button onClick={onClose}>取消</Button>
      </div>
    </div>
  );
};
