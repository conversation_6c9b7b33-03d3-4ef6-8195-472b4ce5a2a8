import { Button, Field, Form, Input, Loading, Message, Radio } from '@alifd/next';
import React, { useReducer, useState } from 'react';
import styles from './style.module.scss';
// import {  updateQuestionBankInfo } from '@/api/v10039';
import dayJs from 'dayjs';

interface QuestionProps {
  questionType: number;
  editValueData: Object;
  editTarget: number;
  cancel: () => void;
  onSubmit: (data) => void;
}
export default ({ questionType, editValueData, cancel, editTarget, onSubmit }: QuestionProps) => {
  const [loading, setLoading] = useState(false);
  const field: Field = Field.useField();

  const [isClick, setIsClick] = useState(true); // 新增问题按钮连点问题 true 可以点击 false 不可以点击
  const editDefaultValue = {
    questionName: '',
    questionType:  1,
    answerResponses: Array.from({ length: 4 }, (_, i) => ({
      id: `${dayJs().valueOf()}-${i}`, // 使用索引保证唯一性
      answerName: '',
      rightFlag: 0, // 0否1是 是否是正确答案
    })),
  };

  const [questionData, setQuestionData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, editValueData || editDefaultValue);

  // 增加答案

  const addAnswerClick = () => {
    const questionAnswerData = {
      answerName: '',
      rightFlag: 0, // 是否是正确答案
      id: dayJs().valueOf(),
    };
    questionData.answerResponses.push(questionAnswerData);
    setQuestionData(questionData);
  };
  const onOkCreate = (values, errors): boolean | void => {
    // console.log(questionData, '题目设置');
    if (!isClick) {
      return;
    }
    const questionDataArr1 = questionData.answerResponses.filter((e) => e.rightFlag === 1);
    if (!errors) {
      if (questionData.questionType === 1) {
        if (questionDataArr1.length <= 0) {
          Message.error('请至少设置一个正确选项');
          return;
        }
      } else if (questionData.questionType === 2) {
        if (questionData.answerResponses.length < 2) {
          Message.error('请至少设置两个选项答案');
          return;
        }
        if (questionDataArr1.length <= 0 || questionDataArr1.length <= 1) {
          Message.error('请至少设置两个正确选项');
          return;
        }
      }
      console.log(questionData, '提交题目');
      setLoading(true);
      setIsClick(false);
      onSubmit(questionData)
    }
  };

  return (
    <div>
      <Loading visible={loading} inline={false}>
        <Form style={{ display: 'block' }} labelCol={{ fixedSpan: 5 }}>
          <Form.Item label="题目标题" required>
            <Form.Item required requiredMessage="请输入问题标题">
              <Input
                maxLength={20}
                name="questionName"
                value={questionData.questionName}
                showLimitHint
                placeholder="请输入问题标题"
                onChange={(questionName) => {
                  questionData.questionName = questionName;
                  setQuestionData(questionData);
                }}
              />
            </Form.Item>
            <Form.Item label="">
              {questionData.answerResponses?.map((item, index) => {
                return (
                  <div key={item.id} className={styles.answerAllStyle}>
                      <Radio
                        checked={item.rightFlag}
                        onChange={(datas) => {
                          questionData.answerResponses.forEach((itemData) => {
                            itemData.rightFlag = 0;
                          });
                          questionData.answerResponses[index].rightFlag = 1;
                          setQuestionData(questionData);
                          console.log(questionData, '单选====');
                        }}
                      />

                    <Form.Item required requiredMessage="请输入选项答案">
                      <Input
                        style={{ width: '200px', margin: '0 10px' }}
                        name={`answerName${item.id}`}
                        className={styles.answerInputStyle}
                        maxLength={20}
                        value={item.answerName}
                        showLimitHint
                        placeholder="请输入选项答案"
                        onChange={(answerName) => {
                          questionData.answerResponses[index].answerName = answerName;
                          setQuestionData(questionData);
                        }}
                      />
                    </Form.Item>
                    {index < 3 && (
                      <Button
                        className={styles.buttonDiv}
                        type="primary"
                        size="small"
                        disabled={questionData.answerResponses.length >= 4}
                        onClick={() => {
                          console.log('增加答案');
                          addAnswerClick();
                        }}
                      >
                        增加
                      </Button>
                    )}
                    {index > 0 && (
                      <Button
                        className={styles.buttonDiv}
                        type="secondary"
                        size="small"
                        onClick={() => {
                          questionData.answerResponses.splice(index, 1);
                          setQuestionData(questionData);
                          field.setErrors();
                        }}
                      >
                        删除
                      </Button>
                    )}
                  </div>
                );
              })}
            </Form.Item>
          </Form.Item>
          <Form.Item label=" ">
            <Form.Submit validate type="primary" onClick={onOkCreate}>
              提交
            </Form.Submit>
            <Button onClick={cancel} style={{ marginLeft: 15 }}>
              取消
            </Button>
          </Form.Item>
        </Form>
      </Loading>
    </div>
  );
};
