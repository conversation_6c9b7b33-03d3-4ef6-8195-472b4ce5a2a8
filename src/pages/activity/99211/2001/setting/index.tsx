import React, { useEffect, useImperativeHandle, useReducer, useRef } from "react";
import DecorateArea from "./compoonets/DecorateArea";
import { decorateReducer } from "./decorateReducer";
import styles from "./index.module.scss";
// @ts-ignore
import { config } from "ice";
import { getActivityParams } from "@/utils";

export default ({ defaultValue, value, sRef,onSettingChange,decoValue }) => {
  const [state, dispatch] = useReducer(decorateReducer, value?.moduleInfo || defaultValue.moduleInfo);
  const [init, setInit] = React.useState(false);
  const iframeRef = useRef<HTMLIFrameElement | null>(null);
  const [activityType, code] = getActivityParams();
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  /**
   * 向c端发送消息
   */
  const sendMessage = (type, event, postData): void => {
    const iWindow = iframeRef.current?.contentWindow;
    // delete postData.modules;
    console.log(`向c端发送消息  ${new Date()}`, postData);
    iWindow?.postMessage(
      {
        from: "B",
        type,
        event,
        data: postData
      },
      "*"
    );
  };

  /**
   * 接收到来自c的消息
   */
  const receiveMessage = (res): void => {
    console.log(`接收到来自c的消息`, res.data);
    if (res.data.type === "mounted") {
      getInitData().then();
    }
    if (res.data.event === "changeSelect") {
      dispatch({
        type: "SELECT_MODULE",
        payload: res.data.data
      });
    }
    // if (res.data.event === 'changeSort') {
    //   dispatch({
    //     type: 'SELECT_MODULE',
    //     payload: res.data.data.item,
    //   });
    //   if (res.data.data.type === 'up') {
    //     dispatch({
    //       type: 'MOVE_UP',
    //       payload: res.data.data.item,
    //     });
    //   } else {
    //     dispatch({
    //       type: 'MOVE_DOWN',
    //       payload: res.data.data.item,
    //     });
    //   }
    // }
  };
  const getInitData = async () => {
    setInit(true);
    dispatch({
      type: "INIT_MODULE",
      payload: { ...formData.moduleInfo.modules, comSortList: formData.selectedOptions },
    });
  };

  useEffect(() => {
    if (!init) return;
    if (!iframeRef.current) return;
    console.log(state.modules,'state.modules');
    sendMessage("activity", "update", state.modules);
    sendMessage( "deco","update",decoValue);
    setFormData({ ...formData,moduleInfo:state });
    onSettingChange({ ...formData,moduleInfo:state });
  }, [state]);

  // 传递Ref
  useImperativeHandle(sRef, () => ({
    submit: () => {
      // const result = {};
      // formData.selectedOptions.forEach((item) => {
      //   result[item] = state.modules[item];
      // });
      const resultPushAllowed = {};
      formData.selectedOptions.forEach((item) => {
        resultPushAllowed[item] = state.pushAllowed[item];
      });
      return {
        // result,
        resultPushAllowed,
        selectedModule: state.selectedModule
      };
    }
  }));

  // 初始化监听/销毁postMessage信息
  useEffect(() => {
    console.log('value', value);
    // 当监听事件中存在对state值的访问时，无法获取监听后的值，需要重新绑定事件
    window.addEventListener("message", receiveMessage, false);
    return (): void => {
      window.removeEventListener("message", receiveMessage);
    };
  }, []);
  return (
    <div>
      <div id="main">
          <div className={styles.mainContainer}>
            <div className={styles.preview}>
              <div className={styles.phone}>
                <iframe
                  style={{ height: "100%" }}
                  ref={iframeRef}
                  width={375}
                  src={`${config.previewUrl}/${activityType}/${code}/?pageType=preview`}
                  frameBorder="0"
                />
              </div>
            </div>
              <DecorateArea selectedModule={state.selectedModule} state={state} dispatch={dispatch} formData={formData} />
          </div>
      </div>
    </div>
  );
};
