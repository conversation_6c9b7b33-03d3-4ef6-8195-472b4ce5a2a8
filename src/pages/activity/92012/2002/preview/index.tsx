/**
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * Date: 2023-06-07 09:50
 * Description: 活动预览
 */
import React, { useReducer, useState } from 'react';
import { Form, Table, Input, Button } from '@alifd/next';
import { formItemLayout, PageData, PRIZE_TYPE } from '../util';
import styles from './style.module.scss';
import { goToPropertyList } from '@/utils';
import format from '@/utils/format';
import LzDialog from '@/components/LzDialog';

const FormItem = Form.Item;

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  labelAlign?: 'left' | 'top';
}

export default ({ defaultValue, value, labelAlign = 'left' }: Props) => {
  const [formData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  const [skuVisible, setSkuVisible] = useState(false);
  const [seriesSkuList, setSeriesSkuList] = useState([]);
  formItemLayout.labelAlign = labelAlign;
  return (
    <div className={styles.preview}>
      <Form {...formItemLayout} isPreview>
        <FormItem label="活动标题">{formData.activityName}</FormItem>
        <FormItem label="活动时间">{`${format.formatDateTimeDayjs(formData.rangeDate[0])}至${format.formatDateTimeDayjs(
          formData.rangeDate[1],
        )}`}</FormItem>
        <FormItem label="活动门槛">{formData.threshold === 0 ? '无门槛' : '店铺会员'}</FormItem>
        {formData.seriesList.length > 0 &&
          formData.seriesList.map((item) => {
            return (
              <>
                <FormItem label="机制名">{item.seriesName}</FormItem>
                <FormItem label="复购正装罐数门槛">{item.moreMum}</FormItem>
                <FormItem label="SKU列表">
                  <Button
                    onClick={() => {
                      console.log(item.seriesSkuList);
                      setSeriesSkuList(item.seriesSkuList);
                      setSkuVisible(true);
                    }}
                  >
                    查看SKU
                  </Button>
                </FormItem>
                <FormItem label="奖品列表">
                  <Table dataSource={item.seriesPrizeList} style={{ marginTop: '15px' }}>
                    <Table.Column title="奖品名称" dataIndex="prizeName" />
                    <Table.Column
                      title="奖品类型"
                      cell={(_, index, row) => (
                        <div style={{ cursor: 'pointer' }} onClick={() => goToPropertyList(row.prizeType)}>
                          {PRIZE_TYPE[row.prizeType]}
                        </div>
                      )}
                    />
                    {/* <Table.Column */}
                    {/*  title="单位数量" */}
                    {/*  cell={(_, index, row) => { */}
                    {/*    if (row.prizeType === 1) { */}
                    {/*      return <div>{row.numPerSending ? `${row.numPerSending}张` : ''}</div>; */}
                    {/*    } else { */}
                    {/*      return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>; */}
                    {/*    } */}
                    {/*  }} */}
                    {/* /> */}
                    {/* <Table.Column */}
                    {/*  title="发放份数" */}
                    {/*  cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>} */}
                    {/* /> */}
                    {/* <Table.Column */}
                    {/*  title="单份价值(元)" */}
                    {/*  cell={(_, index, row) => <div>{row.unitPrice ? Number(row.unitPrice).toFixed(2) : '0'}</div>} */}
                    {/* /> */}
                    <Table.Column
                      title="奖品图"
                      cell={(_, index, row) => <img style={{ width: '30px' }} src={row.prizeImg} alt="" />}
                    />
                  </Table>
                </FormItem>
              </>
            );
          })}
        <FormItem label="订单限制">限制</FormItem>
        <FormItem label="首购订单时间">
          {`${format.formatDateTimeDayjs(formData.firstOrderRange[0])}至${format.formatDateTimeDayjs(
            formData.firstOrderRange[1],
          )}`}
        </FormItem>
        <FormItem label="首购排除SKU">{formData.skuIds}</FormItem>
        <FormItem label="复购时间">首购订单后{formData.moreBuyTimeLimitNum}天下单</FormItem>
        <FormItem label="订单状态">{formData.orderStatus === 1 ? '已付款' : '已完成'}</FormItem>
        <FormItem label="奖品发放时间">订单完成{formData.jdTimeLimitNum}天发放</FormItem>
        <FormItem label="活动分享">{formData.shareStatus === 0 ? '关闭' : '开启'}</FormItem>
        {formData.shareStatus !== 0 && (
          <>
            <FormItem label="分享标题">{formData.shareTitle}</FormItem>
            <FormItem label="图文分享图片">
              <img src={formData.h5Img} style={{ width: '200px' }} alt="" />
            </FormItem>
            <FormItem label="京口令分享图片">
              <img src={formData.cmdImg} style={{ width: '200px' }} alt="" />
            </FormItem>
            <FormItem label="小程序分享图片">
              <img src={formData.mpImg} style={{ width: '200px' }} alt="" />
            </FormItem>
          </>
        )}
        <FormItem label="规则内容">
          <Input.TextArea className="rule-word-break" value={formData.rules} />
        </FormItem>
      </Form>
      {/*  sku列表 */}
      <LzDialog
        title={false}
        visible={skuVisible}
        footer={false}
        onClose={() => setSkuVisible(false)}
        style={{ width: '670px', height: '500' }}
      >
        {seriesSkuList.length && (
          <div style={{ margin: '10px' }}>
            <Table dataSource={seriesSkuList} fixedHeader>
              <Table.Column title="系列名称" dataIndex="seriesName" />
              <Table.Column title="试用装SKU" dataIndex="sampleSkuId" />
              <Table.Column title="试用装名称" dataIndex="sampleSkuName" />
              <Table.Column
                title="试用装图"
                cell={(_, index, row) => <img style={{ width: '30px' }} src={row.sampleSkuPicture} alt="" />}
              />
              <Table.Column title="正装SKU" dataIndex="formalSkuId" />
              <Table.Column title="正装名称" dataIndex="formalSkuName" />
              <Table.Column
                title="正装图"
                cell={(_, index, row) => <img style={{ width: '30px' }} src={row.formalSkuPicture} alt="" />}
              />
              <Table.Column title="正装罐数" dataIndex="formalNum" />
              <Table.Column title="返还京豆数" dataIndex="prizeNum" />
            </Table>
          </div>
        )}
      </LzDialog>
    </div>
  );
};
