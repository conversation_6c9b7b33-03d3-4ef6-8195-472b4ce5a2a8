/**
 * Author: z<PERSON><PERSON><PERSON>
 * Date: 2023-06-08 14:58
 * Description:
 */
import React, { useReducer, useState, useEffect, useImperativeHandle } from 'react';
import { Form, Field, Table, Button, Dialog, NumberPicker, Input, Grid, Message } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import LzDialog from '@/components/LzDialog';
import ChoosePrize from '@/components/ChoosePrizeForYili';
import { activityEditDisabled, deepCopy } from '@/utils';
import { FormLayout, SeriesList, PRIZE_TYPE, PRIZE_INFO, PrizeInfo, PageData } from '../../../util';

interface Props {
  value: SeriesList | undefined;
  defaultValue: Required<PageData>;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<SeriesList>) => void;
}

const FormItem = Form.Item;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

export default ({ sRef, onChange, value, defaultValue }: Props) => {
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value);
  const field: Field = Field.useField();

  // 选择奖品
  const [visible, setVisible] = useState(false);
  // 当前编辑行数据
  const [editValue, setEditValue] = useState(null);
  // 当前编辑行index
  const [target, setTarget] = useState(0);
  // 当前编辑的表格
  const [tableName, setTableName] = useState('');
  // 暂存累计/连续签到天数
  const [skuVisible, setSkuVisible] = useState(false);
  const [seriesSkuList, setSeriesSkuList] = useState([]);

  // 同步/更新数据
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  /**
   * 新建/编辑奖品回调
   * @param data 当前编辑奖品信息
   */
  // const onPrizeChange = (data): boolean | void => {
  //   console.log(defaultValue, 'defaultValuedefaultValue');
  //   if (activityEditDisabled()) {
  //     const { seriesName } = data;
  //     console.log(defaultValue, 'defaultValue');
  //     console.log(data, 'data');
  //     const targetPrizeInfo = defaultValue.seriesList.find((item) => item.seriesName === seriesName);
  //     console.log(targetPrizeInfo, 'targetPrizeInfo');
  //     if (
  //       data.planId === targetPrizeInfo.seriesPrizeList[0].planId &&
  //       data.sendTotalCount < targetPrizeInfo.seriesPrizeList[0].sendTotalCount
  //     ) {
  //       Message.error(`发放份数不能小于${targetPrizeInfo.seriesPrizeList[0].sendTotalCount}份`);
  //       return false;
  //     }
  //   }
  //   // 更新指定index 奖品信息
  //   if (tableName === 'prizeDay') {
  //     formData.prizeDay[target] = data;
  //   } else if (tableName === 'seriesPrizeList') {
  //     formData.seriesPrizeList[target] = { ...data, seriesName: formData.seriesName, sortId: formData.sortId };
  //   }
  //   setData(formData);
  //   setVisible(false);
  // };
  const onPrizeChange = (data): boolean | void => {
    console.log(defaultValue, 'defaultValuedefaultValue');
    if (activityEditDisabled()) {
      const { seriesName } = data;
      const targetPrizeInfo = defaultValue.seriesList.find((item) => item.seriesName === seriesName);
      if (!targetPrizeInfo) {
        // 如果 targetPrizeInfo 为 undefined，跳过校验
        console.log(`未找到系列名称为 ${seriesName} 的奖品信息，跳过校验`);
      } else {
        if (!targetPrizeInfo.seriesPrizeList || targetPrizeInfo.seriesPrizeList.length === 0) {
          Message.error(`系列名称为 ${seriesName} 的奖品列表为空`);
          return false;
        }

        if (
          data.planId === targetPrizeInfo.seriesPrizeList[0].planId &&
          data.sendTotalCount < targetPrizeInfo.seriesPrizeList[0].sendTotalCount
        ) {
          Message.error(`发放份数不能小于${targetPrizeInfo.seriesPrizeList[0].sendTotalCount}份`);
          return false;
        }
      }
    }

    // 更新指定index 奖品信息
    if (tableName === 'prizeDay') {
      formData.prizeDay[target] = data;
    } else if (tableName === 'seriesPrizeList') {
      formData.seriesPrizeList[target] = { ...data, seriesName: formData.seriesName, sortId: formData.sortId };
    }
    setData(formData);
    setVisible(false);
  };
  useEffect((): void => {
    setFormData(value);
  }, [value]);
  useEffect((): void => {
    // 初始奖品列表长度
    const prizeListLength = formData.seriesPrizeList.length;
    // 生成默认奖品列表
    const list: PrizeInfo[] = [...formData.seriesPrizeList];
    // 补齐奖品数到8
    for (let i = 0; i < 1 - prizeListLength; i++) {
      list.push(deepCopy(PRIZE_INFO));
    }
    // 如果奖品列表为空 说明：奖品列表已经够8 直接用prizeList
    // 如果不为空 说明：奖品列表已经不够8 使用补齐后的list
    setData({ seriesPrizeList: list.length ? list : formData.seriesPrizeList });
  }, []);

  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      console.log(898);
      let err: object | null = null;
      field.validate((errors: Object[]): void => {
        console.log('errors', errors);
        err = errors;
      });
      return err;
    },
  }));
  const onCancel = (): void => {
    setVisible(false);
  };

  return (
    <div>
      <LzPanel title="门槛及奖品设置">
        <Form {...formItemLayout} field={field}>
          <FormItem required requiredMessage="请输入机制名称" label="机制名称">
            <Input
              value={formData.seriesName}
              placeholder="请输入机制名称"
              name="seriesName"
              maxLength={20}
              showLimitHint
              className="w-300"
              onChange={(seriesName) => {
                setData({ seriesName });
              }}
            />
          </FormItem>
          <FormItem label="复购正装罐数" required requiredMessage="请输入复购正装罐数门槛">
            <NumberPicker
              min={1}
              max={99999}
              step={1}
              type="inline"
              value={formData.moreMum}
              onChange={(moreMum) => {
                setData({ moreMum });
              }}
              name="moreMum"
            />
            罐
          </FormItem>
          <FormItem label="SKU列表">
            <Button
              onClick={() => {
                setSeriesSkuList(formData.seriesSkuList);
                setSkuVisible(true);
              }}
            >
              查看SKU
            </Button>
          </FormItem>
          <FormItem required label="奖品列表">
            <FormItem>
              <Table dataSource={formData.seriesPrizeList} style={{ marginTop: '15px' }}>
                <Table.Column title="奖品名称" dataIndex="prizeName" />
                <Table.Column
                  title="奖品类型"
                  cell={(_, index, row) => <div>{PRIZE_TYPE[row.prizeType]}</div>}
                  dataIndex="prizeType"
                />
                {/* <Table.Column */}
                {/*  title="单位数量" */}
                {/*  cell={(_, index, row) => { */}
                {/*    if (row.prizeType === 1) { */}
                {/*      return <div>{row.numPerSending ? `${row.numPerSending}张` : ''}</div>; */}
                {/*    } else { */}
                {/*      return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>; */}
                {/*    } */}
                {/*  }} */}
                {/* /> */}
                {/* <Table.Column */}
                {/*  title="发放份数" */}
                {/*  cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>} */}
                {/* /> */}
                {/* <Table.Column */}
                {/*  title="单份价值(元)" */}
                {/*  cell={(_, index, row) => ( */}
                {/*    <div>{row.prizeName ? (row.unitPrice ? Number(row.unitPrice).toFixed(2) : 0) : ''}</div> */}
                {/*  )} */}
                {/* /> */}
                <Table.Column
                  title="奖品图"
                  cell={(_, index, row) => <img style={{ width: '30px' }} src={row.prizeImg} alt="" />}
                />
                <Table.Column
                  title="操作"
                  width={130}
                  cell={(val, index, _) => (
                    <FormItem>
                      <Button
                        text
                        type="primary"
                        onClick={() => {
                          let row = formData.seriesPrizeList[index];
                          if (row.prizeName === '') {
                            row = null;
                          }
                          setEditValue(row);
                          setTarget(index);
                          setTableName('seriesPrizeList');
                          setVisible(true);
                        }}
                      >
                        <i className={`iconfont icon-chaojiyingxiaoicon-25`} />
                      </Button>
                      {formData.seriesPrizeList.length > 1 && (
                        <Button
                          text
                          type="primary"
                          onClick={() => {
                            Dialog.confirm({
                              v2: true,
                              title: '提示',
                              centered: true,
                              content: '确认删除该奖品？',
                              onOk: () => {
                                formData.seriesPrizeList.splice(index, 1);
                                setData(formData);
                              },
                              onCancel: () => console.log('cancel'),
                            } as any);
                          }}
                        >
                          <i className={`iconfont icon-shanchu`} />
                        </Button>
                      )}
                    </FormItem>
                  )}
                />
              </Table>
            </FormItem>
            <FormItem>
              <Button
                disabled={formData.seriesPrizeList.length >= 1}
                type="primary"
                onClick={() => {
                  formData.seriesPrizeList.push(deepCopy(PRIZE_INFO));
                  setData(formData);
                }}
              >
                +添加奖品（{formData.seriesPrizeList.length}/1）
              </Button>
            </FormItem>
          </FormItem>
        </Form>
      </LzPanel>

      <LzDialog
        title={false}
        visible={visible}
        footer={false}
        onClose={() => setVisible(false)}
        style={{ width: '670px' }}
      >
        <ChoosePrize
          formData={formData}
          editValue={editValue}
          onChange={onPrizeChange}
          onCancel={onCancel}
          hasProbability={false}
          hasLimit={false}
          typeList={[2]}
          defaultTarget={2}
        />
      </LzDialog>
      {/*  sku列表 */}
      <LzDialog
        title={false}
        visible={skuVisible}
        footer={false}
        onClose={() => setSkuVisible(false)}
        style={{ width: '1000px', height: '700' }}
      >
        {seriesSkuList.length && (
          <div style={{ margin: '10px' }}>
            <Table dataSource={seriesSkuList} fixedHeader>
              <Table.Column title="系列名称" dataIndex="seriesName" />
              <Table.Column title="试用装SKU" dataIndex="sampleSkuId" />
              <Table.Column title="试用装名称" dataIndex="sampleSkuName" />
              <Table.Column
                title="试用装图"
                cell={(_, index, row) => <img style={{ width: '30px' }} src={row.sampleSkuPicture} alt="" />}
              />
              <Table.Column title="正装SKU" dataIndex="formalSkuId" />
              <Table.Column title="正装名称" dataIndex="formalSkuName" />
              <Table.Column
                title="正装图"
                cell={(_, index, row) => <img style={{ width: '30px' }} src={row.formalSkuPicture} alt="" />}
              />
              <Table.Column title="正装罐数" dataIndex="formalNum" />
              <Table.Column title="返还京豆数" dataIndex="prizeNum" />
            </Table>
          </div>
        )}
      </LzDialog>
    </div>
  );
};
