/**
 * Author: z<PERSON><PERSON><PERSON>
 * Date: 2023-06-06 15:48
 * Description:
 */

import React, { useImperativeHandle, useReducer, useEffect } from 'react';
import { Form, Button, Field, Input } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import {
  checkActivityData,
  FormLayout,
  generateMembershipString,
  PageData,
  PrizeInfo,
} from '@/pages/activity/92012/2004/util';
import format from '@/utils/format';

const FormItem = Form.Item;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
  checkForm: () => boolean;
}

export default ({ onChange, defaultValue, value, sRef, checkForm }: Props) => {
  const field: Field = Field.useField();
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: object | null = null;
      field.validate((errors: Object[]): void => {
        err = errors;
      });
      return err;
    },
  }));
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);

  /**
   * 自动生成规则说明
   */
  return (
    <div>
      <LzPanel title="活动规则">
        <Form {...formItemLayout} field={field}>
          <FormItem label="规则内容" required requiredMessage="请输入规则内容">
            <Input.TextArea
              value={formData.rules}
              name="rules"
              onChange={(rules) => setData({ rules })}
              autoHeight={{ minRows: 8, maxRows: 40 }}
              placeholder="请输入活动规则说明"
              maxLength={5000}
              showLimitHint
              className="form-input-ctrl"
            />
          </FormItem>
          {/* <FormItem label=" " colon={false}> */}
          {/*  <Button */}
          {/*    type="primary" */}
          {/*    className="table-cell-btn" */}
          {/*    onClick={autoCreateRuleDesc} */}
          {/*    style={{ marginRight: '15px' }} */}
          {/*    text */}
          {/*  > */}
          {/*    自动生成规则说明 */}
          {/*  </Button> */}
          {/*  <span style={{ color: 'red', fontSize: '12px' }}> */}
          {/*    提示：点击按钮自动生成规则，也可以自己编辑信息，手机端规则处显示。 */}
          {/*  </span> */}
          {/* </FormItem> */}
        </Form>
      </LzPanel>
    </div>
  );
};
