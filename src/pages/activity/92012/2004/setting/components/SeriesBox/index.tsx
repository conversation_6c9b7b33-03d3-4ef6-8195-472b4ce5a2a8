/**
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * Date: 2024-4-8 9:20
 * Description:
 */
import React, { useReducer, useState, useEffect, useImperativeHandle } from 'react';
import LzPanel from '@/components/LzPanel';
import LzDialog from '@/components/LzDialog';
import { PageData, SeriesList } from '@/pages/activity/92012/2004/util';
import { Button, Dialog, Message, Upload, Table } from '@alifd/next';
// 奖品信息
import PrizesInfo from '../Prizes';
import { deepCopy, downloadExcel, activityEditDisabled } from '@/utils';
import { seriesTemplateExport } from '@/api/v92012';
import { config } from 'ice';
import CONST from '@/utils/constant';

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}
export default ({ sRef, onChange, defaultValue, value }: Props) => {
  const [fileList, setFileList] = useState<File[]>([]);
  const [uploaderRef, setUploaderRef] = useState(false);
  const saveUploaderRef = (ref) => {
    if (!ref) {
      return;
    }
    setUploaderRef(ref.getInstance());
  };

  const downloadTemplate = async () => {
    try {
      const data: any = await seriesTemplateExport();
      downloadExcel(data, '伊利尝鲜试喝模板');
    } catch (error) {
      Message.error(error.message);
    }
  };

  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);

  // sku列表
  const [seriesSkuList, setSeriesSkuList] = useState<[]>([]);
  // 是否展示sku列表
  const [skuVisible, setSkuVisible] = useState(false);
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };

  const prizesRef: any[] = [];
  useImperativeHandle(sRef, (): { submit: () => object | null } => ({
    submit: () => {
      console.log(1213213);
      console.log(prizesRef);
      let err: object | null = null;
      for (let i = 0; i < prizesRef.length; i++) {
        const e = prizesRef[i].submit();
        if (e) {
          err = e;
        }
      }
      return err;
    },
  }));
  // <PrizesInfo sRef={prizesRef} {...settingProps} />
  const downloadImg = async () => {
    const imageUrl = 'https://img10.360buyimg.com/imgzone/jfs/t1/319497/26/6571/53003/6840fd30F683da845/d05884aea9258530.png';
    const fileName = '试用装示例图.png';

    try {
      const response = await fetch(imageUrl);
      const blob = await response.blob();
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(downloadUrl);
    } catch (error) {
      console.error('下载失败:', error);
      Message.error('图片下载失败，请重试');
    }
  };
  return (
    <LzPanel>
      <div>
        <Message type="notice" style={{ marginBottom: 10 }}>
          导入须知： <br />
          1.导入前请下载模板，将你要上传的数据放入模板中，使用模板进行导入。
          <br />
          2.单次导入最大5M，导入中请不要关闭此页面。
          <br />
          <div style={{ display: 'flex' }}>
            <Button text type="primary" onClick={downloadTemplate}>
              下载模板
            </Button>
            <div style={{ marginLeft: '10px' }}>
              <Button text type="primary" onClick={() => {
                downloadImg();
              }}>
                下载试用装示例图
              </Button>
              <span style={{ color: 'gray', fontSize: '12px' }}>（图片推荐尺寸140px*180px）</span>
            </div>
          </div>
        </Message>
        <Upload
          action={`${config.baseURL}/92012/importSeriesExcel`}
          name="file"
          method="post"
          headers={{
            token: localStorage.getItem(CONST.LZ_SSO_TOKEN),
            prd: localStorage.getItem(CONST.LZ_SSO_PRD),
          }}
          ref={saveUploaderRef}
          value={fileList}
          limit={1}
          listType="text"
          accept=".xls,.xlsx"
          onChange={(info) => {
            if (info.length) {
              if (info[0].size > 5 * 1024 * 1024) {
                Message.error('文件大小不能超过5M');
                return;
              }
            }
            prizesRef.splice(0);
            setFileList(info);
          }}
          onError={(res) => {
            if (res.state === 'error') {
              if (res.response?.message) {
                Message.error(res.response?.message);
              } else {
                Message.error('文件错误，请上传正确的文件');
              }
            }
          }}
          onSuccess={(res) => {
            console.log(res);
            if (res.response.code === 200) {
              console.log(res, '上传数据');
              // setTemporarySeriesList(res.response.data);
              const seriesList = [];
              const seriesSkuList = [];
              res.response.data.forEach((item, index) => {
                seriesList.push({
                  seriesName: item.seriesName,
                  moreMum: item.moreMum,
                  seriesPrizeList: [],
                  seriesSkuList: item.seriesSkuInfoList,
                  sortId: index + 1,
                });
                seriesSkuList.push(...item.seriesSkuInfoList);
              });
              setData({ seriesList, seriesSkuList });
              Dialog.success({
                title: '导入结果',
                content: (
                  <div>
                    <p>导入成功</p>
                  </div>
                ),
                onOk: () => {
                  console.log('导入成功');
                },
              });
            } else if (res.response?.message) {
              setFileList([]);
              Message.error(res.response?.message);
            } else {
              setFileList([]);
              Message.error('文件错误，请上传正确的文件');
            }
          }}
          style={{ marginBottom: 10 }}
        >
          <div className="next-upload-drag">
            <p className="next-upload-drag-icon">
              <Button type="primary">上传机制及系列数据</Button>
            </p>
            <p className="next-upload-drag-hint">支持xls类型的文件</p>
          </div>
        </Upload>
      </div>
      {formData.seriesList &&
        formData.seriesList.map((item, index) => (
          <PrizesInfo
            sRef={(ref) => {
              prizesRef[index] = ref;
            }}
            value={item}
            defaultValue={defaultValue}
            onChange={(val) => {
              const seriesList = deepCopy(formData.seriesList);
              seriesList[index] = val;
              setData({ seriesList });
            }}
          />
        ))}
    </LzPanel>
  );
};
