/**
 * Author: <PERSON><PERSON><PERSON>e
 * Date: 2023-06-07 11:37
 * Description:
 */
import React from 'react';
import dayjs, { Dayjs } from 'dayjs';
import { Dialog, Message } from '@alifd/next';
import format from '@/utils/format';
import { getParams } from '@/utils';
import Preview from '@/components/LzCrowdBag/preview';
import { getShop } from '@/utils/shopUtil';

export interface FormLayout {
  labelCol: {
    span?: number;
    fixedSpan?: number;
  };
  wrapperCol: {
    span: number;
  };
  labelAlign: 'left' | 'top';
  colon: boolean;
}

export interface PrizeInfo {
  potNum: string;
  prizeName: string;
  prizeImg: string;
  prizeType: number;
  unitCount: number;
  unitPrice: number;
  sendTotalCount: number;
  ifPlan: number;
  startTime?: string;
  endTime?: string;
  startDate?: string;
  endDate?: string;
  planId?: string;
}

export interface seriesSkuList {
  potNum: string;
  skuId: string;
}

export interface SeriesList {
  seriesName: string;
  seriesPic: string;
  seriesPrizeList: PrizeInfo[];
  seriesSkuList: seriesSkuList[];
  seriesUrl: string;
}

export interface CustomValue {
  pageBg: string; // 主页背景图
  actBg: string; // 主页背景色
  actBgColor: string;
  shopNameColor: string; // 店铺名称颜色
  orderBtn: string;
  ruleBtn: string;
  myPrizeBtn: string;
  thresholdBg: string; // 机制背景图
  receivePrizeBg: string; // 领取按钮
  seriesTabAct: string; // 系列tab选中
  seriesTabNotAct: string; // 系列tab未选中
  sampleBg: string; // 试用装系列背景框
  sampleGoodsBg: string; // 试用装商品背景框
  formalBg: string; // 正装商品背景框
  formalGoodsBg: string; // 正装商品背景框
  productExample: string; // 商品示例
  returnTopBtn: string; // 返回顶部按钮
  cmdImg: string; // 命令图片
  h5Img: string; // h5图片
  mpImg: string; // 小程序图片
}

export interface PageData {
  shopName: string;
  activityName: string;
  rangeDate: [Dayjs, Dayjs];
  startTime: string;
  endTime: string;
  threshold: number;
  supportLevels: string;
  limitJoinTimeType: number;
  joinTimeRange: [string, string] | [Dayjs, Dayjs];
  joinStartTime: string;
  joinEndTime: string;
  noBuyTimeLimitNum: number;
  moreBuyTimeLimitNum: number;
  prizeDay: PrizeInfo[];
  seriesList: SeriesList[];
  seriesSkuList: [];
  shareStatus: number;
  shareTitle: string;
  rules: string;
  templateCode: string;
  cmdImg: string;
  h5Img: string;
  mpImg: string;
  gradeLabel: string[];
  crowdBag: any;
  orderFilterToggle: number;
  orderStatus: number;
  orderRangeDate: [];
  receiveLimit: number;
  jdTimeLimitNum: number;
  isExposure: number;
  exposureSkuList: any[];
  receivePointRangeDate: [];
  firstOrderRange: []; // 首购订单时间范围
  firstOrderStartTime: string;// 首购订单开始时间
  firstOrderEndTime: string; // 首购订单结束时间
  skuIds?: string;
}

interface PrizeType {
  [key: number]: string;
}

export const PRIZE_TYPE: PrizeType = {
  12: '京元宝权益',
  2: '京豆',
  1: '优惠券',
  3: '实物',
  4: '积分',
  6: '红包',
  7: '礼品卡',
  8: '京东E卡',
  9: 'PLUS会员卡',
  10: '爱奇艺会员卡',
};
// form格式
export const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};
// 步骤文案
export const STEPS = ['① 设置模板', '② 活动设置', '③ 活动预览', '④ 活动完成'];
// 装修默认数据
export const DEFAULT_CUSTOM_VALUE: CustomValue = {
  pageBg: '', // 页面背景图
  actBg: '//img10.360buyimg.com/imgzone/jfs/t1/305670/4/7727/326442/683fb795F398bd434/9f237d8ba6cb20b2.png', // 主KV
  actBgColor: '#ffffff',
  shopNameColor: '#72421f', // 店铺名称颜色
  orderBtn: '//img10.360buyimg.com/imgzone/jfs/t1/279862/40/21586/3255/67ff59e3Fa019cc21/bb9622a7bcf31278.png', // 订单按钮
  ruleBtn: '//img10.360buyimg.com/imgzone/jfs/t1/272775/1/22248/3500/67ff59e3F7af419cb/6b12162fa9b5226a.png', // 活动规则按钮
  myPrizeBtn: '//img10.360buyimg.com/imgzone/jfs/t1/278061/39/21995/3818/67ff59e3F4ca20e78/fc9578ee4760d880.png', // 我的奖品按钮
  thresholdBg: '//img10.360buyimg.com/imgzone/jfs/t1/283413/4/21009/9796/67ff6ec3Fd4bce6df/bdedf23e5dbddcbe.png', // 机制背景图
  receivePrizeBg: '//img10.360buyimg.com/imgzone/jfs/t1/273727/2/21486/6792/67ff59fbFbb711691/6f5d13194bae105b.png', // 领取奖品按钮
  seriesTabAct: '//img10.360buyimg.com/imgzone/jfs/t1/270852/22/22573/3324/67ff5b88F6ede515e/9014d8f6ce756359.png', // 系列tab选中
  seriesTabNotAct: '//img10.360buyimg.com/imgzone/jfs/t1/274168/12/21346/2790/67ff5b88Fd31736ab/cff5d344a79f5429.png', // 系列tab未选中
  sampleBg: '//img10.360buyimg.com/imgzone/jfs/t1/275429/34/21322/167999/67ff5b8aFc4bda772/fa5e65ab0af25b0b.png', // 试用装背景框
  sampleGoodsBg: '//img10.360buyimg.com/imgzone/jfs/t1/279133/21/21338/51021/67ff5b89F9038ffb3/b88a93f3b60bc7f7.png', // 试用装商品背景框
  formalBg: '//img10.360buyimg.com/imgzone/jfs/t1/279688/19/21580/168622/67ff5b87F5d6e18ca/a7bf8351feeb7bdd.png', // 正装背景框
  formalGoodsBg: '//img10.360buyimg.com/imgzone/jfs/t1/283437/27/21129/33602/67ff5b87F991210ae/405d8234d1fbdea2.png', // 正装商品背景框
  productExample: '//img10.360buyimg.com/imgzone/jfs/t1/278723/39/21171/13732/67ff5b89F0216c76a/9acbeb9ac20a4ad1.png', // 商品示例
  returnTopBtn: '//img10.360buyimg.com/imgzone/jfs/t1/274085/12/16974/7437/67f4b716F8e135d0c/d7ef521769219caf.png', // 返回顶部按钮
  cmdImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/302722/35/11758/40480/683fecdfF0fde2914/fb7cc8830b56b6a8.png',
  h5Img: 'https://img10.360buyimg.com/imgzone/jfs/t1/286082/15/7045/8305/683fecdeF1336928a/e8a0499629008adf.png',
  mpImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/293603/37/6611/55139/683fecdeF6194f50f/d8d0154f1a417a17.png',
};
// 活动设置默认数据

export const INIT_PAGE_DATA = (): PageData => {
  const now = (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00');
  const after30 = (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59');
  return {
    // 店铺名称
    shopName: getShop().shopName,
    // 活动名称
    activityName: `尝鲜试喝-${dayjs().format('YYYY-MM-DD')}`,
    // 日期区间（不提交）
    rangeDate: [now, after30],
    // 活动开始时间
    startTime: format.formatDateTimeDayjs(now),
    // 活动结束时间
    endTime: format.formatDateTimeDayjs(after30),
    // 活动门槛（不提交）
    threshold: 1,
    // 支持的会员等级(-1:无门槛；1,2,3,4,5,-9以逗号做分割(-9为关注店铺用户))
    supportLevels: '1,2,3,4,5',
    // 限制入会时间 0：不限制；1：入会时间
    limitJoinTimeType: 0,
    // 入会时间
    joinTimeRange: [] as any,
    joinStartTime: '',
    joinEndTime: '',
    // 未购时间
    noBuyTimeLimitNum: 180,
    moreBuyTimeLimitNum: 1,
    // 每日签到奖品
    prizeDay: [],
    // 机制列表
    seriesList: [],
    // 系列正装试用装列表
    seriesSkuList: [],
    // 是否开启分享
    shareStatus: 1,
    // 分享标题
    shareTitle: '尝鲜试喝返好礼，超多惊喜大奖等你来领！',
    // 分享图片
    cmdImg: '',
    h5Img: '',
    mpImg: '',
    // 活动规则
    rules: '',
    templateCode: '',
    gradeLabel: [],
    crowdBag: null,
    orderFilterToggle: 1,
    orderRangeDate: [],
    receiveLimit: 0,
    jdTimeLimitNum: 7,
    orderStatus: 2,
    // 是否开启曝光
    isExposure: 0,
    // 商品列表
    exposureSkuList: [],
    receivePointRangeDate: [],
    firstOrderRange: [], // 首购订单时间范围
    firstOrderStartTime: '',// 首购订单开始时间
    firstOrderEndTime: '', // 首购订单结束时间
    skuIds: '', // 首购需要排除skuId
  };
};

export const PRIZE_INFO: PrizeInfo = {
  // 奖品名称
  prizeName: '',
  // 奖品图
  prizeImg: '',
  // 奖品类型
  prizeType: 0,
  // 单位数量
  unitCount: 0,
  // 单位价值
  unitPrice: 0,
  // 发放份数
  sendTotalCount: 0,
  // 签到天数
  potNum: '',
  ifPlan: 0,
};
// 预览二维码大小
export const QRCODE_SIZE = 74;
// 预览二维码配置项
export const QRCODE_SETTING = {
  src: require('@/assets/images/jd-logo.webp'),
  height: QRCODE_SIZE / 7.4,
  width: QRCODE_SIZE / 7.4,
  excavate: false,
};

// 生成会员等级字符串
export const generateMembershipString = (formData: PageData, type?: string) => {
  if (formData.threshold === 1) {
    const levels = formData.supportLevels.split(',');
    const LEVEL_MAP = formData.gradeLabel;
    const memberLevelsList = levels.filter((e) => Number(e) > 0);
    const membershipLevels = memberLevelsList.map((l, index) => LEVEL_MAP[index]).join('，');
    const membershipString = memberLevelsList.length ? `店铺会员（${membershipLevels}）` : '';
    const extraLevelsString = levels
      .filter((e) => Number(e) < 0)
      .map((l, index) => LEVEL_MAP[index + memberLevelsList.length])
      .join('、');

    const joinTimeLimitString =
      formData.limitJoinTimeType === 1 ? `。限制入会时间：${formData.joinStartTime}至${formData.joinEndTime}` : '';

    return (
      membershipString +
      (extraLevelsString && `${memberLevelsList.length > 0 ? '、' : ''}${extraLevelsString}`) +
      joinTimeLimitString
    );
  }
  if (formData.threshold === 2) {
    return !type ? <Preview value={formData.crowdBag} /> : `人群包<${formData.crowdBag.packName}>`;
  }
  return '不限制';
};
// 是否未编辑
const isProcessingEditType = (): boolean => {
  return getParams('type') === 'edit';
};
// 校验开始时间是否符合规则
const isStartTimeValid = (startTime: string): boolean => {
  const isLessThanTenMinutes: boolean = dayjs(startTime).isBefore(dayjs().startOf('day'));
  if (isLessThanTenMinutes) {
    Message.error('活动开始时间应大于当天时间');
    return false;
  }
  return true;
};
// 校验结束时间是否符合规则
const isEndTimeValid = (startTime: string, endTime: string): boolean => {
  const isGreaterThanNow: boolean = dayjs(endTime).isAfter(dayjs());
  const isGreaterThanStart: boolean = dayjs(endTime).isAfter(dayjs(startTime));

  if (!isGreaterThanStart) {
    Message.error('活动结束时间应大于活动开始时间');
    return false;
  }
  if (!isGreaterThanNow) {
    Message.error('活动结束时间应大于当前时间');
    return false;
  }
  return true;
};
// 校验活动结束时间比京豆计划结束时间小
// const checkAwardStartTime = (endTime: string, seriesList: any[]) => {
//   console.log(dayjs(endTime).valueOf(), '结束时间');
//   console.log(seriesList, '机制数组');
//   console.log(dayjs(1746019638000).format('YYYY-MM-DD HH:mm:ss'));
//   const planList = seriesList.map((item) => {
//     return {
//       ...item.seriesPrizeList.map((dataItem) => {
//         return {
//           seriesName: item.seriesName,
//           endDate: dataItem.endDate,
//         };
//       }),
//     };
//   });
//   console.log(planList, 'planList');
//   return false;
// };
const checkAwardStartTime = (endTime: string, seriesList: SeriesList[]): boolean => {
  const activityEndDate = dayjs(endTime);
  for (const series of seriesList) {
    for (const prize of series.seriesPrizeList) {
      if (prize.endDate) {
        const prizeEndDate = dayjs(prize.endDate);
        if (activityEndDate.isAfter(prizeEndDate)) {
          Message.error(
            `活动结束时间应小于或等于奖品 ${prize.prizeName} 的结束时间 ${prizeEndDate.format('YYYY-MM-DD HH:mm:ss')}`,
          );
          return false;
        }
      }
    }
  }

  return true;
};
// 校验奖品时间是否符合规则
const isPrizeValid = (prize: PrizeInfo, formData: PageData): boolean => {
  // console.log('formData.jdTimeLimitNum', formData.jdTimeLimitNum);
  const type = prize.prizeType;
  // 0-谢谢参与 3-实物 4-积分 没有时间直接通过
  if (type === 0 || type === 3 || type === 4) {
    return true;
  }
  // 开始时间
  const start = prize.startTime || prize.startDate;
  // 结束时间
  const end = prize.endTime || prize.endDate;

  if (start && end) {
    // 奖品的开始时间间是否小于活动的开始时间
    // const isAfter: boolean = dayjs(formData.startTime).add(formData.jdTimeLimitNum, 'day').isAfter(dayjs(start));
    // if (isAfter) {
    //   Message.error(
    //     `奖品${PRIZE_TYPE[prize.prizeType]}起始时间需要晚于或等于活动开始时间${
    //       formData.jdTimeLimitNum ? `+延迟发奖天数` : ''
    //     }`,
    //   );
    //   return false;
    // }
    // 京豆计划开始时间减去活动开始时间大于延迟发奖天数
    // if (Math.abs(dayjs(formData.startTime).valueOf() - dayjs(start).valueOf()) > formData.jdTimeLimitNum * 24 * 60 * 60 * 1000) {
    //   Message.error(
    //     `奖品${PRIZE_TYPE[prize.prizeType]}起始时间与活动开始时间间隔已经大于延迟发奖天数，可能会导致奖品发放失败`,
    //   );
    // }
    const acTimeDiffInMs = Math.abs(dayjs(formData.startTime).valueOf() - dayjs(formData.endTime).valueOf());
    const acDaysDiff = Math.floor(acTimeDiffInMs / (1000 * 60 * 60 * 24)); // 活动天数
    if (acDaysDiff + formData.jdTimeLimitNum > 90) {
      Message.error('活动天数+延迟发奖天数不能大于90天');
      return false;
    }
    const isBeforeAc: boolean = dayjs(formData.startTime).isBefore(dayjs(start));
    if (isBeforeAc) {
      Message.error(`奖品${PRIZE_TYPE[prize.prizeType]}开始时间需要早于或等于活动开始时间`);
      return false;
    }
    // 奖品的结束时间间是否大于活动的结束时间
    const isBefore: boolean = dayjs(formData.endTime).add(formData.jdTimeLimitNum, 'day').isAfter(dayjs(end));
    if (isBefore) {
      Message.error(
        `奖品${PRIZE_TYPE[prize.prizeType]}结束时间需要晚于或等于活动结束时间${formData.jdTimeLimitNum ? `+延迟发奖天数` : ''
        }`,
      );
      return false;
    }
  }
  return true;
};
const showConfirm = (prizeType: any): Promise<boolean> => {
  return new Promise((resolve) => {
    Dialog.confirm({
      v2: true,
      title: '提示',
      content: `奖品${PRIZE_TYPE[prizeType]}起始时间与活动开始时间间隔已经大于延迟发奖天数，可能会导致奖品发放失败`,
      messageProps: {
        type: 'warning',
      },
      closeable: false,
      onOk: () => {
        resolve(true); // 用户点击确认，继续执行
      },
      onCancel: undefined,
    });
  });
};
const isPrizeValid1 = async (prize: PrizeInfo, formData: PageData): Promise<boolean> => {
  // console.log('formData.jdTimeLimitNum', formData.jdTimeLimitNum);
  const type = prize.prizeType;
  // 0-谢谢参与 3-实物 4-积分 没有时间直接通过
  if (type === 0 || type === 3 || type === 4) {
    return true;
  }
  // 开始时间
  const start = prize.startTime || prize.startDate;
  // 结束时间
  const end = prize.endTime || prize.endDate;

  if (start && end) {
    // 京豆计划开始时间减去活动开始时间大于延迟发奖天数
    if (Math.abs(dayjs(formData.startTime).valueOf() - dayjs(start).valueOf()) > formData.jdTimeLimitNum * 24 * 60 * 60 * 1000) {
      // console.log('formData.jdTimeLimitNum===========');
      const confirmed = await showConfirm(prize.prizeType);
      if (!confirmed) {
        return false;
      }
    }
  };
  return true;
}
// 校验奖品时间是否符合规则
const arePrizesValid = (formData: PageData): boolean => {
  for (let i = 0; i < formData.seriesList.length; i++) {
    const { seriesPrizeList } = formData.seriesList[i];
    if (!seriesPrizeList.length) {
      Message.error(`请填写${formData.seriesList[i].seriesName}下的奖品信息`);
      return false;
    }
    for (let j = 0; j < seriesPrizeList.length; j++) {
      if (!seriesPrizeList[j].prizeName) {
        Message.error(`请填写${formData.seriesList[i].seriesName}下的奖品信息`);
        return false;
      }
      if (!isPrizeValid(seriesPrizeList[j], formData)) {
        return false;
      }
    }
  }
  //  for (const item of formData.seriesList) {
  //   const { seriesPrizeList } = item;
  //   for (const item1 of seriesPrizeList) {
  //     const isValid = await isPrizeValid1(item1, formData);
  //     if (isValid === true) {
  //       return true;
  //     }
  //   }
  // }
  return true;
};
// 校验京豆计划绑定重复
const checkJdBeanPlan = (formData: PageData): boolean => {
  // console.log('formData.seriesList111111111111', formData.seriesList);
  const planIds = new Set<string>();
  for (const series of formData.seriesList) {
    for (const prize of series.seriesPrizeList) {
      if (prize.planId) {
        if (planIds.has(prize.planId)) {
          return false; // 发现重复的 planId
        }
        planIds.add(prize.planId);
        console.log('planIds', planIds);
      }
    }
  }
  return true; // 没有发现重复的 planId
};

export const checkActivityData = (formData: PageData): boolean => {
  if (!isProcessingEditType()) {
    // 开始时间异常
    if (!isStartTimeValid(formData.startTime)) {
      return false;
    }
  }
  // 结束时间异常
  if (!isEndTimeValid(formData.startTime, formData.endTime)) {
    return false;
  }
  // // 结束时间要比机制内京豆计划时间小
  // if (!checkAwardStartTime(formData.endTime, formData.seriesList)) {
  //   return false;
  // }
  // 未上传系列信息
  if (!formData.seriesList.length) {
    Message.error('请上传系列数据');
    return false;
  }
  // 奖品时间与活动时间冲突
  if (!arePrizesValid(formData)) {
    return false;
  }
  // 判断选择的京豆计划是否相同
  if (!checkJdBeanPlan(formData)) {
    // console.log('checkJdBeanPlan', formData);
    Message.error('京豆计划绑定重复');
    return false;
  }
  return true;
};
