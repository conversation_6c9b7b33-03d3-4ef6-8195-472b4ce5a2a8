import React, { useReducer, useImperativeHandle, useEffect } from 'react';
import LzPanel from '@/components/LzPanel';
import { DatePicker2, Field, Form, NumberPicker, Radio } from '@alifd/next';
import { FormLayout, PageData } from '../../../util';
import { activityEditDisabled, validateActivityThreshold } from '@/utils';

const FormItem = Form.Item;
const { RangePicker } = DatePicker2;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}

export default ({ onChange, defaultValue, value, sRef }: Props) => {
  // 用于 Fusion Form 表单校验
  const field: Field = Field.useField({ values: value || defaultValue });
  // 同装修收集数据
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  // 同步活动设置数据
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };

  // 通过Ref触发表单校验
  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: any = null;

      field.validate((errors: Object[]): void => {
        err = errors;
      });
      return validateActivityThreshold(formData, err, field);
    },
  }));

  return (
    <div>
      <LzPanel title="领奖设置">
        <Form {...formItemLayout} field={field} disabled={activityEditDisabled()}>
          <FormItem label="领取次数限制">
            <Radio.Group disabled value={formData.receiveLimit}>
              <Radio value={0}>单次领取</Radio>
            </Radio.Group>
          </FormItem>
          <FormItem label="奖品发放时间" required requiredMessage="请输入奖品发放时间">
            订单完成后
            <span> </span>
            <NumberPicker
              min={0}
              max={30}
              step={1}
              type="inline"
              value={formData.jdTimeLimitNum}
              onChange={(jdTimeLimitNum) => {
                setData({ jdTimeLimitNum });
              }}
              name="jdTimeLimitNum"
            />
            <span> </span>
            天后发放
          </FormItem>
        </Form>
      </LzPanel>
    </div>
  );
};
