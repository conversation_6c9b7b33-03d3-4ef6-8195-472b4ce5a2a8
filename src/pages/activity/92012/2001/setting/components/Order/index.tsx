import React, { useReducer, useImperativeHandle, useEffect, useState } from 'react';
import LzPanel from '@/components/LzPanel';
import { DatePicker2, Field, Form, Input, NumberPicker, Radio, Select } from '@alifd/next';
import { FormLayout, PageData } from '../../../util';
import { activityEditDisabled, validateActivityThreshold } from '@/utils';
import constant from '@/utils/constant';
import format from '@/utils/format';
const dateFormat: string = constant.DATE_FORMAT_TEMPLATE;

const FormItem = Form.Item;
const { RangePicker } = DatePicker2;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

const ORDER_STATUS = [
  { label: '已付款', value: 1 },
  { label: '已完成', value: 2 },
];

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}

export default ({ onChange, defaultValue, value, sRef }: Props) => {
  // 用于 Fusion Form 表单校验
  const field: Field = Field.useField();
  // 同装修收集数据
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  // 同步活动设置数据
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };

  // 通过Ref触发表单校验
  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: any = null;

      field.validate((errors): void => {
        err = errors;
      });
      return validateActivityThreshold(formData, err, field);
    },
  }));

  // 日期改变，处理提交数据
  const firstOrderRangeChange = (firstOrderRange): void => {
    setData({
      firstOrderRange,
      firstOrderStartTime: format.formatDateTimeDayjs(firstOrderRange[0]),
      firstOrderEndTime: format.formatDateTimeDayjs(firstOrderRange[1]),
    });
  };
  const skuIdsChange = (barcodeString) => {
    const filteredValue = barcodeString.replace(/[^,A-Z0-9]/g, '');
    // console.log('filteredValue', filteredValue);
    setData({ skuIds: filteredValue });
  };

  // 失去焦点时转换数组
  const onBlurSkuIdsChange = (e): void => {
    const finalValue = e.target.value.replace(/[^,A-Z0-9]/g, '');
    // console.log('finalValue', finalValue);
    setData({ skuIds: finalValue });
  };
  return (
    <div>
      <LzPanel title="订单限制">
        <Form {...formItemLayout} field={field} disabled={activityEditDisabled()}>
          <FormItem label="订单限制" disabled={false}>
            <Radio.Group value={formData.orderFilterToggle}>
              <Radio value={1}>限制</Radio>
            </Radio.Group>
          </FormItem>
          {!!formData.orderFilterToggle && (
            <>
              <FormItem label="首购订单时间" required requiredMessage="请设置首购订单时间">
                <RangePicker
                  className="w-300"
                  name="firstOrderRange"
                  inputReadOnly
                  format={dateFormat}
                  hasClear={false}
                  showTime
                  disabled={[activityEditDisabled(), false]}
                  value={formData.firstOrderRange || [new Date(formData.firstOrderStartTime), new Date(formData.firstOrderEndTime)]}
                  onChange={firstOrderRangeChange}

                />

              </FormItem>
              <FormItem label="首购排除SKU">
                <Input.TextArea
                  value={formData.skuIds}
                  name="skuIds"
                  onChange={skuIdsChange}
                  onBlur={onBlurSkuIdsChange}
                  autoHeight={{ minRows: 8, maxRows: 40 }}
                  placeholder="请输入skuId，以英文逗号分隔"
                  className="form-input-ctrl"
                />
              </FormItem>
              <FormItem
                label="完成复购时间"
                required
                requiredMessage="请输入完成复购时间"
                extra={<div className="next-form-item-help">注：包含0天 最小值 1</div>}
              >
                首购订单后
                <span> </span>
                <NumberPicker
                  min={1}
                  max={30}
                  step={1}
                  type="inline"
                  value={formData.moreBuyTimeLimitNum}
                  onChange={(moreBuyTimeLimitNum) => {
                    setData({ moreBuyTimeLimitNum });
                  }}
                  name="moreBuyTimeLimitNum"
                />
                <span> </span>天内下单
              </FormItem>
              <FormItem
                label="订单状态"
                extra={
                  <div className="next-form-item-help">
                    已付款：用户付款后即可参与活动 <br></br>
                    已完成：(1)用户订单完成以后才可参与活动;(2)预售商品需要支付尾款方可参与活动
                  </div>
                }
              >
                <Select
                  dataSource={ORDER_STATUS}
                  value={formData.orderStatus}
                  onChange={(orderStatus) => {
                    setData({ orderStatus });
                  }}
                  popupContainer={(triggerNode) => triggerNode.parentNode}
                />
              </FormItem>
            </>
          )}
        </Form>
      </LzPanel>
    </div>
  );
};
