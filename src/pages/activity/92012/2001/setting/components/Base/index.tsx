/**
 * Author: z<PERSON><PERSON><PERSON>
 * Date: 2023-06-02 11:30
 * Description:
 */
import React, { useReducer, useImperativeHandle, useEffect } from 'react';
import LzPanel from '@/components/LzPanel';
import { Form, Input, DatePicker2, Field, Radio, Dialog } from '@alifd/next';
import dayjs from 'dayjs';
import format from '@/utils/format';
import constant from '@/utils/constant';
import { activityEditDisabled, calcDateDiff, validateActivityThreshold } from '@/utils';
import { FormLayout, PageData } from '../../../util';
import { getMarketExpireTime } from '@/utils/shopUtil';
// import LzThreshold from '@/components/LzThreshold';

const FormItem = Form.Item;
const RadioGroup = Radio.Group;

const dateFormat: string = constant.DATE_FORMAT_TEMPLATE;
const { RangePicker } = DatePicker2;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}
export default ({ onChange, defaultValue, value, sRef }: Props) => {
  // 用于 Fusion Form 表单校验
  const field: Field = Field.useField({ values: value || defaultValue });
  // 同装修收集数据
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  // 同步活动设置数据
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  // 日期改变，处理提交数据
  const onDataRangeChange = (rangeDate): void => {
    const marketEndTime = getMarketExpireTime();
    const endTime = dayjs(rangeDate[1]).valueOf();
    if (Number(endTime) > Number(marketEndTime)) {
      // Message.error('活动结束时间需小于店铺订购有效期');
      Dialog.confirm({
        v2: true,
        title: '店铺订购超期提示',
        content: '活动结束时间需小于店铺订购有效期',
        onOk: () => console.log('ok'),
      });
    }
    if (formData.receiveTimeType === 0) {
      setData({
        rangeDate,
        startTime: format.formatDateTimeDayjs(rangeDate[0]),
        endTime: format.formatDateTimeDayjs(rangeDate[1]),
        receivePointRangeDate: rangeDate,
      });
    } else {
      setData({
        rangeDate,
        startTime: format.formatDateTimeDayjs(rangeDate[0]),
        endTime: format.formatDateTimeDayjs(rangeDate[1]),
      });
    }
  };
  // 获取持续时间
  const DateRangeDuration = ({ rangeDate }) => {
    const duration: string = calcDateDiff(rangeDate);
    return (
      <span style={{ fontSize: '12px', marginLeft: '5px' }}>
        {
          <span>
            活动持续<span style={{ color: 'red' }}>{duration || '不足1小时'}</span>
          </span>
        }
      </span>
    );
  };
  // 通过Ref触发表单校验
  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: any = null;

      field.validate((errors: Object[]): void => {
        err = errors;
      });
      return validateActivityThreshold(formData, err, field);
    },
  }));
  return (
    <div>
      <LzPanel title="活动基本信息">
        <Form {...formItemLayout} field={field}>
          <FormItem label="活动名称" required requiredMessage="请输入活动名称" disabled={false}>
            <Input
              value={formData.activityName}
              placeholder="请输入活动名称"
              name="activityName"
              maxLength={20}
              showLimitHint
              className="w-300"
              onChange={(activityName) => setData({ activityName })}
            />
          </FormItem>
          <FormItem
            label="活动时间"
            required
            requiredMessage="请选择活动时间"
            extra={<div className="next-form-item-help">注：活动时间需设置在软件的订购有效期内</div>}
          >
            <RangePicker
              className="w-300"
              name="rangeDate"
              inputReadOnly
              format={dateFormat}
              hasClear={false}
              showTime
              disabled={[activityEditDisabled(), false]}
              value={formData.rangeDate || [new Date(formData.startTime), new Date(formData.endTime)]}
              onChange={onDataRangeChange}
              disabledDate={(date) => {
                return date.valueOf() < dayjs().subtract(1, 'day').valueOf();
              }}
            />
            <DateRangeDuration
              rangeDate={formData.rangeDate || [new Date(formData.startTime), new Date(formData.endTime)]}
            />
          </FormItem>
          <FormItem label="活动门槛" required>
            <RadioGroup
              value={formData.threshold}
              disabled={activityEditDisabled()}
              onChange={(val) => {
                setData({
                  threshold: val,
                });
              }}
            >
              {/* <Radio value={0}>无门槛</Radio> */}
              <Radio value={1}>店铺会员</Radio>
            </RadioGroup>
          </FormItem>
        </Form>
      </LzPanel>
    </div>
  );
};
