import React, { useEffect, useState } from 'react';
import { Form, Input, Field, Table, DatePicker2, Select, Button } from '@alifd/next';
import LzPagination from '@/components/LzPagination';
import Utils, { deepCopy, downloadExcel, getParams } from '@/utils';
import dayJs from 'dayjs';
import { dataFirstExport, reportFirstPage } from '@/api/v92012Data';

import constant from '@/utils/constant';

const { RangePicker } = DatePicker2;

const FormItem = Form.Item;
const defaultPage = {
  pageNum: 1,
  pageSize: 10,
  total: 0,
};
const STATUS = [
  { label: '全部', value: '' },
  { label: '失败', value: 0 },
  { label: '成功', value: 1 },
];

const defaultTableColumns = () => {
  return [
    {
      title: '活动日期',
      dataIndex: 'activityTime',
      lock: true,
      width: 120,
    },
    {
      title: '总数据',
      children: [
        {
          title: 'PV',
          dataIndex: 'pv',
          width: 90,
        },
        {
          title: 'UV',
          dataIndex: 'uv',
          width: 90,
        },
        {
          title: '购买人数汇总',
          dataIndex: 'buyUserNum',
          width: 90,
        },
        {
          title: '购买金额汇总',
          dataIndex: 'buyNum',
          width: 90,
        },
        {
          title: '新入会人数',
          dataIndex: 'openCardUser',
          width: 90,
        },
        {
          title: '领取京豆人数',
          dataIndex: 'receiveUser',
          width: 90,
        },
        {
          title: '领取京豆数量',
          dataIndex: 'receiveNum',
          width: 90,
        },
      ],
    },
  ];
};

export default (props) => {
  const field = Field.useField();
  const [pageInfo, setPage] = useState(deepCopy(defaultPage));
  const [tableCloumns, setTableColumns] = useState<any[]>(defaultTableColumns());
  const [tableData, setTableData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const defaultRangeVal = [
    dayJs(new Date(getParams('startTime'))).format('YYYY-MM-DD'),
    dayJs(new Date(getParams('endTime'))).format('YYYY-MM-DD'),
  ];
  const [packVisible, setPackVisible] = useState(false);

  const handleSubmit = (e) => {
    e.preventDefault();
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  };
  const loadData = (query: any): void => {
    setLoading(true);
    query.activityId = getParams('id');
    query.startDate = dayJs(query.dateRange[0]).format('YYYY-MM-DD');
    query.endDate = dayJs(query.dateRange[1]).format('YYYY-MM-DD');
    reportFirstPage(query)
      .then((data): void => {
        if (data[0]) {
          const columns = [];
          data[0].stepDataResponseList.forEach((item) => {
            const colmIndex = columns.findIndex((col) => col.title === item.typeName);
            const stepColum = {
              title: item.seriesName,
              stepSort: item.stepSort,
              children: [
                {
                  title: '人数',
                  dataIndex: `seriesUserNum${item.seriesId}`,
                  width: 90,
                },
                {
                  title: '金额',
                  dataIndex: `seriesPrizeNum${item.seriesId}`,
                  width: 90,
                },
              ],
            };
            if (colmIndex === -1) {
              columns.push({
                title: item.typeName,
                seriesSort: item.seriesSort,
                children: [stepColum],
              });
            } else {
              columns[colmIndex].children.push(stepColum);
            }
          });
          columns.sort((a, b) => a.seriesSort - b.seriesSort);
          columns.forEach((item) => {
            item.children.sort((a, b) => a.stepSort - b.stepSort);
          });
          setTableColumns([...defaultTableColumns(), ...columns]);
        }
        data.forEach((item) => {
          item.stepDataResponseList.forEach((step) => {
            item[`seriesUserNum${step.seriesId}`] = step.seriesUserNum;
            item[`seriesPrizeNum${step.seriesId}`] = step.seriesPrizeNum;
          });
        });
        setTableData(data);
        // pageInfo.total = +res.total!;
        // pageInfo.pageSize = +res.size!;
        // pageInfo.pageNum = +res.current!;
        // setPage(pageInfo);
        setLoading(false);
      })
      .catch((e) => {
        setLoading(false);
      });
  };
  const handlePage = ({ pageSize, pageNum }) => {
    setPage({
      ...pageInfo,
      pageSize,
      pageNum,
    });
    const formValue: any = field.getValues();
    loadData({ ...formValue, pageSize, pageNum });
  };

  const exportData = () => {
    const query = field.getValues();
    query.activityId = getParams('id');
    query.startDate = dayJs(query.dateRange[0]).format('YYYY-MM-DD');
    query.endDate = dayJs(query.dateRange[1]).format('YYYY-MM-DD');
    dataFirstExport(query).then((data: any) => downloadExcel(data, '满额有礼活动数据'));
  };

  useEffect(() => {
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  }, []);

  return (
    <div style={{ minHeight: '350px' }}>
      <Form className="lz-query-criteria" field={field} colon labelAlign={'top'} onSubmit={handleSubmit}>
        <FormItem name="dateRange" label="时间范围">
          <RangePicker hasClear={false} defaultValue={defaultRangeVal} format={'YYYY-MM-DD'} />
        </FormItem>
        {/* <Form.Item name="pin" label="用户pin"> */}
        {/*   <Input placeholder="请输入用户pin" /> */}
        {/* </Form.Item> */}
        {/* <Form.Item name="status" label="状态"> */}
        {/*   <Select followTrigger mode="single" defaultValue="" style={{ marginRight: 8 }} dataSource={STATUS} /> */}
        {/* </Form.Item> */}
        <FormItem colon={false}>
          <Form.Submit type="primary" htmlType="submit">
            查询
          </Form.Submit>
          <Form.Reset
            toDefault
            onClick={() => {
              const formValue: any = field.getValues();
              loadData({ ...formValue, ...defaultPage });
            }}
          >
            重置
          </Form.Reset>
          <Button onClick={exportData}>导出</Button>
        </FormItem>
      </Form>
      {/* <div style={{ margin: '10px 0', textAlign: 'right' }}> */}
      {/*   <Button disabled={!tableData.length} onClick={() => setPackVisible(true)} style={{ marginLeft: '10px' }}> */}
      {/*     生成人群包 */}
      {/*   </Button> */}
      {/* </div> */}
      <Table columns={tableCloumns} dataSource={tableData} loading={loading} />
      <LzPagination
        pageNum={pageInfo.pageNum}
        pageSize={pageInfo.pageSize}
        total={pageInfo.total}
        onChange={handlePage}
      />
      {/* <LzDialog */}
      {/*   title="生成人群包" */}
      {/*   className="lz-dialog-mini" */}
      {/*   visible={packVisible} */}
      {/*   footer={false} */}
      {/*   onCancel={() => setPackVisible(false)} */}
      {/*   onClose={() => setPackVisible(false)} */}
      {/* > */}
      {/*   <LzGenerateCrowdBag */}
      {/*     dataUploadPin={userPrizeRecordPageUploadPin} */}
      {/*     formValue={field.getValues()} */}
      {/*     cancel={() => setPackVisible(false)} */}
      {/*   /> */}
      {/* </LzDialog> */}
    </div>
  );
};
