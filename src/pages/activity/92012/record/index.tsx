/**
 * 伊利尝鲜试喝数据报表
 */
import React, { useState } from 'react';
import { Tab } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import FirstPurchaseActivityData from './components/FirstPurchaseActivityData';
import RepurchaseActivityData from './components/RepurchaseActivityData';

export default () => {
  const [activeKey, setActiveKey] = useState('1');
  return (
    <div className="crm-container">
      <LzPanel title="尝鲜试喝数据报表">
        <Tab defaultActiveKey="1" activeKey={activeKey} onChange={(key) => setActiveKey(key)} unmountInactiveTabs>
          <Tab.Item title="首购活动数据" key="1">
            <FirstPurchaseActivityData />
          </Tab.Item>
          <Tab.Item title="复购活动数据" key="2">
            <RepurchaseActivityData />
          </Tab.Item>
        </Tab>
      </LzPanel>
    </div>
  );
};
