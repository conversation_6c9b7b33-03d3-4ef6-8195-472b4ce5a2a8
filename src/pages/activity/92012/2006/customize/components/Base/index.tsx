/**
 * Author: z<PERSON><PERSON><PERSON>
 * Date: 2023-05-29 18:02
 * Description: 基础元素
 */
import React, { useReducer, useEffect } from 'react';
import styles from './style.module.scss';
import { Form, Button, Grid } from '@alifd/next';
import LzColorPicker from '@/components/LzColorPicker';
import LzImageSelector from '@/components/LzImageSelector';
import { CustomValue, FormLayout } from '../../../util';
import LzPanel from '@/components/LzPanel';

const formLayout: Omit<FormLayout, 'wrapperCol' | 'labelAlign'> = {
  labelCol: {
    fixedSpan: 5,
  },
  colon: true,
};

interface Props {
  defaultValue: Partial<CustomValue>;
  value: CustomValue | undefined;
  onChange: (formData: Required<CustomValue>) => void;
}

export default ({ defaultValue, value, onChange }: Props) => {
  // 装修数据 || 初始数据
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);

  // 更新数据，向上传递
  const setForm = (obj): void => {
    setFormData(obj);
    onChange({ ...formData, ...obj });
  };
  // 用于处理装修组件重新挂载赋值问题
  // 重置接口返回默认值
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value, defaultValue]);
  return (
    <div className={styles.base}>
      <LzPanel title="活动背景">
        <Form {...formLayout} className={styles.form}>
          <Form.Item label="活动主KV">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={750}
                  value={formData.actBg}
                  onChange={(actBg) => {
                    setForm({ actBg });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：宽度750px、推荐图片高度1508px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ actBg: defaultValue?.actBg });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item label="页面背景图">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={750}
                  value={formData.pageBg}
                  onChange={(pageBg) => {
                    setForm({ pageBg });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：宽度750px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ pageBg: defaultValue?.pageBg });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
        </Form>
      </LzPanel>
      <LzPanel title="基础元素">
        <Form {...formLayout} className={styles.form}>
          {/* <Form.Item label="店铺名颜色"> */}
          {/*  <LzColorPicker value={formData.shopNameColor} onChange={(shopNameColor) => setForm({ shopNameColor })} /> */}
          {/*  <Button */}
          {/*    type="primary" */}
          {/*    text */}
          {/*    onClick={() => { */}
          {/*      setForm({ shopNameColor: defaultValue.shopNameColor }); */}
          {/*    }} */}
          {/*  > */}
          {/*    重置 */}
          {/*  </Button> */}
          {/* </Form.Item> */}
          <Form.Item label="我的订单按钮">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={43}
                  height={110}
                  value={formData.orderBtn}
                  onChange={(orderBtn) => {
                    setForm({ orderBtn });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：宽度65px、推荐图片高度165px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ orderBtn: defaultValue?.orderBtn });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item label="活动规则按钮">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={43}
                  height={110}
                  value={formData.ruleBtn}
                  onChange={(ruleBtn) => {
                    setForm({ ruleBtn });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：宽度43px、推荐图片高度110px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ ruleBtn: defaultValue?.ruleBtn });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item label="我的奖品按钮">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={43}
                  height={110}
                  value={formData.myPrizeBtn}
                  onChange={(myPrizeBtn) => {
                    setForm({ myPrizeBtn });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：宽度43px、推荐图片高度110px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ myPrizeBtn: defaultValue?.myPrizeBtn });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item label="机制背景图">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={700}
                  height={184}
                  value={formData.thresholdBg}
                  onChange={(thresholdBg) => {
                    setForm({ thresholdBg });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：宽度43px、推荐图片高度110px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ thresholdBg: defaultValue?.thresholdBg });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item label="领奖按钮">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={181}
                  height={42}
                  value={formData.receivePrizeBg}
                  onChange={(receivePrizeBg) => {
                    setForm({ receivePrizeBg });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：宽度181px、高度42px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ receivePrizeBg: defaultValue?.receivePrizeBg });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item label="系列tab选中">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={166}
                  height={83}
                  value={formData.seriesTabAct}
                  onChange={(seriesTabAct) => {
                    setForm({ seriesTabAct });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：宽度166px、高度83px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ seriesTabAct: defaultValue?.seriesTabAct });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item label="系列tab未选中">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={166}
                  height={83}
                  value={formData.seriesTabNotAct}
                  onChange={(seriesTabNotAct) => {
                    setForm({ seriesTabNotAct });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：宽度166px、高度83px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ seriesTabNotAct: defaultValue?.seriesTabNotAct });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item label="试用装背景框">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={553}
                  height={106}
                  value={formData.sampleBg}
                  onChange={(sampleBg) => {
                    setForm({ sampleBg });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：宽度553px 高度106px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ sampleBg: defaultValue?.sampleBg });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item label="试用装商品背景框">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={317}
                  height={308}
                  value={formData.sampleGoodsBg}
                  onChange={(sampleGoodsBg) => {
                    setForm({ sampleGoodsBg });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：宽度317px、高度308px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ sampleGoodsBg: defaultValue?.sampleGoodsBg });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item label="正装背景框">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={553}
                  height={106}
                  value={formData.formalBg}
                  onChange={(formalBg) => {
                    setForm({ formalBg });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：宽度553px、高度106px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ formalBg: defaultValue?.formalBg });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item label="正装商品背景框">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={317}
                  height={428}
                  value={formData.formalGoodsBg}
                  onChange={(formalGoodsBg) => {
                    setForm({ formalGoodsBg });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：宽度317px、高度428px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ formalGoodsBg: defaultValue?.formalGoodsBg });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item label="表格内商品图示例">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={100}
                  height={160}
                  value={formData.productExample}
                  onChange={(productExample) => {
                    setForm({ productExample });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：宽度100px、高度160px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      window.location.href =
                        'https://lz-yl-saas.oss-cn-zhangjiakou.aliyuncs.com/yili/%E8%AF%95%E7%94%A8%E8%A3%85%E5%95%86%E5%93%81%E5%9B%BE(1).png';
                    }}
                  >
                    下载
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item label="返回顶部按钮">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={300}
                  height={60}
                  value={formData.returnTopBtn}
                  onChange={(returnTopBtn) => {
                    setForm({ returnTopBtn });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：宽度300px、高度60px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ returnTopBtn: defaultValue?.returnTopBtn });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
        </Form>
      </LzPanel>
    </div>
  );
};
