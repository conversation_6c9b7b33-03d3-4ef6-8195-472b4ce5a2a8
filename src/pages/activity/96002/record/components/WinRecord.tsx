import React, { useEffect, useState } from 'react';
import { Form, Input, Field, Table, Button, Select, Message } from '@alifd/next';
import LzPagination from '@/components/LzPagination';
import { dataWinningLog, dataWinningLogExport, dataWinningUploadPin } from '@/api/v96002';
import Utils, { deepCopy, downloadExcel, getParams } from '@/utils';
import LzDialog from '@/components/LzDialog';
import LzGenerateCrowdBag from '@/components/LzGenerateCrowdBag';

const FormItem = Form.Item;
const defaultPage = {
  pageNum: 1,
  pageSize: 10,
  total: 0,
};

export default (props) => {
  const field = Field.useField();
  const [pageInfo, setPage] = useState(deepCopy(defaultPage));
  const [tableData, setTableData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [packVisible, setPackVisible] = useState(false);

  const IS_GETPRIZE = [
    { label: '全部', value: 2 },
    { label: '领取成功', value: 0 },
    { label: '领取失败', value: 1 },
  ];
  const IS_GRADELABEL = [
    { label: '全部', value: '-1' },
    { label: '银卡会员', value: '2' },
    { label: '金卡会员', value: '3' },
    { label: '铂金卡会员', value: '4' },
    { label: '白钻卡会员', value: '5' },
  ];

  const handleSubmit = (e) => {
    e.preventDefault();
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  };

  const onRowSelectionChange = (selectKey: string[]): void => {
    console.log(selectKey);
  };
  const rowSelection: {
    mode: 'single' | 'multiple' | undefined;
    onChange: (selectKey: string[]) => void;
  } = {
    mode: 'multiple',
    onChange: (selectKey: string[]) => onRowSelectionChange(selectKey),
  };
  const loadData = (query: any): void => {
    setLoading(true);
    query.activityId = getParams('id');
    dataWinningLog(query)
      .then((res: any): void => {
        setTableData(res.records as any[]);
        pageInfo.total = +res.total!;
        pageInfo.pageSize = +res.size!;
        pageInfo.pageNum = +res.current!;
        setPage(pageInfo);
        setLoading(false);
      })
      .catch((e) => {
        setLoading(false);
      });
  };
  const handlePage = ({ pageSize, pageNum }) => {
    setPage({
      ...pageInfo,
      pageSize,
      pageNum,
    });
    const formValue: any = field.getValues();
    loadData({ ...formValue, pageSize, pageNum });
  };
  const exportData = () => {
    const formValue: any = field.getValues();
    formValue.activityId = getParams('id');
    dataWinningLogExport(formValue).then((data: any) => downloadExcel(data, '馥蕾诗生日礼奖品领取记录'));
  };
  useEffect(() => {
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  }, []);

  return (
    <div>
      <Form className="lz-query-criteria" field={field} colon labelAlign={'top'} onSubmit={handleSubmit}>
        <Form.Item name="pin" label="用户pin">
          <Input placeholder="请输入用户pin" />
        </Form.Item>
        <FormItem name="level" label="会员等级" requiredMessage="请选择会员等级">
          <Select
            followTrigger
            mode="single"
            showSearch
            hasClear
            style={{ marginRight: 8 }}
            dataSource={IS_GRADELABEL}
            defaultValue={-1}
          />
        </FormItem>
        <FormItem name="status" label="领取状态" requiredMessage="请选择领取状态">
          <Select
            followTrigger
            mode="single"
            showSearch
            hasClear
            style={{ marginRight: 8 }}
            dataSource={IS_GETPRIZE}
            defaultValue={2}
          />
        </FormItem>
        <FormItem colon={false}>
          <Form.Submit type="primary" htmlType="submit">
            查询
          </Form.Submit>
          <Form.Reset
            toDefault
            onClick={() => {
              const formValue: any = field.getValues();
              loadData({ ...formValue, ...defaultPage });
            }}
          >
            重置
          </Form.Reset>
        </FormItem>
      </Form>
      <div style={{ margin: '10px 0', textAlign: 'right' }}>
        <Button onClick={exportData}>导出</Button>
        <Button disabled={!tableData.length} onClick={() => setPackVisible(true)} style={{ marginLeft: '10px' }}>
          生成人群包
        </Button>
      </div>
      <Table rowSelection={rowSelection} dataSource={tableData} loading={loading}>
        <Table.Column
          title="用户昵称"
          cell={(_, index, row) => (
            <div>
              {
                <div className="lz-copy-text">
                  {row.nickName ? Utils.mask(row.nickName) : '-'}
                  {row.nickName && (
                    <span
                      className={'iconfont icon-fuzhi'}
                      style={{ marginLeft: 5 }}
                      onClick={() => {
                        Utils.copyText(row.nickName).then(() => {
                          Message.success('用户昵称已复制到剪切板');
                        });
                      }}
                    />
                  )}
                </div>
              }
            </div>
          )}
        />
        <Table.Column
          title="用户pin"
          cell={(_, index, row) => (
            <div>
              {
                <div className="lz-copy-text">
                  {row.encryptPin ? Utils.mask(row.encryptPin) : '-'}
                  {row.encryptPin && (
                    <span
                      className={'iconfont icon-fuzhi'}
                      style={{ marginLeft: 5 }}
                      onClick={() => {
                        Utils.copyText(row.encryptPin).then(() => {
                          Message.success('用户pin已复制到剪切板');
                        });
                      }}
                    />
                  )}
                </div>
              }
            </div>
          )}
        />
        <Table.Column title="奖品类型" dataIndex="prizeType" />
        <Table.Column title="奖品名称" dataIndex="prizeName" />
        <Table.Column title="会员等级" dataIndex="memberLevel" />
        <Table.Column title="领取状态" dataIndex="receiveStatus" />
        <Table.Column title="备注" dataIndex="details" />
      </Table>
      <LzPagination
        pageNum={pageInfo.pageNum}
        pageSize={pageInfo.pageSize}
        total={pageInfo.total}
        onChange={handlePage}
      />
      <LzDialog
        title="生成人群包"
        className="lz-dialog-mini"
        visible={packVisible}
        footer={false}
        onCancel={() => setPackVisible(false)}
        onClose={() => setPackVisible(false)}
      >
        <LzGenerateCrowdBag
          dataUploadPin={dataWinningUploadPin}
          formValue={field.getValues()}
          cancel={() => setPackVisible(false)}
        />
      </LzDialog>
    </div>
  );
};
