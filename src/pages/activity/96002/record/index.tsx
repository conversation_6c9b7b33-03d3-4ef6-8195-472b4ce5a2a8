import React, { useState } from 'react';
import { Tab } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import PromotionRecord from './components/PromotionRecord';
import WinRecord from './components/WinRecord';

export default () => {
  const [activeKey, setActiveKey] = useState('1');
  return (
    <div className="crm-container">
      <LzPanel title="fresh生日礼数据报表">
        <Tab defaultActiveKey="1" activeKey={activeKey} onChange={(key) => setActiveKey(key)} unmountInactiveTabs>
          <Tab.Item title="奖品领取记录" key="1">
            <WinRecord />
          </Tab.Item>
          <Tab.Item title="令牌操作记录" key="2">
            <PromotionRecord />
          </Tab.Item>
        </Tab>
      </LzPanel>
    </div>
  );
};
