.complete {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  width: 100%;
  padding: 15px;
  background: white;
  border-radius: 5px;

  .content {
    display: flex;

    .qrcode {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;

      span {
        margin-top: 10px;
        font-size: 12px;
      }
    }

    .tip {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      margin-left: 20px;

      div {
        display: flex;
        align-items: center;

        img {
          width: 50px;
          height: 50px;
        }

        span {
          font-size: 16px;
          margin-top: -10px;
        }
      }

      button {
        margin-top: 15px;
        width: 100px;
      }
    }
  }

  .warning {
    background: #f0faff;
    padding: 10px;
    width: 100%;
    margin-top: 15px;
    font-size: 12px;
    font-weight: bold;

    & > div {
      &:not(:first-child) {
        margin-top: 15px;
        font-weight: 400;
      }
    }

    .link {
      color: #389bff;
      margin-left: 5px;
      cursor: pointer;
    }
  }


}
