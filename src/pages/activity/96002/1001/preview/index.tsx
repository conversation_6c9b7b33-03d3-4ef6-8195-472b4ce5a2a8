import React, { useReducer } from 'react';
import { Form, Table } from '@alifd/next';
import { formItemLayout, PageData, GRADE_LABEL, PRIZE_TYPE } from '../util';
import styles from './style.module.scss';
import { goToPropertyList } from '@/utils';
import format from '@/utils/format';
import ChoosePromotion from '@/components/ChoosePromotion';
import SkuList from '@/components/SkuList';

const FormItem = Form.Item;

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  labelAlign?: 'left' | 'top';
}

export default ({ defaultValue, value, labelAlign = 'left' }: Props) => {
  const [formData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);

  formItemLayout.labelAlign = labelAlign;
  return (
    <div className={styles.preview}>
      <Form {...formItemLayout} isPreview>
        <FormItem label="活动标题">{formData.activityName}</FormItem>
        <FormItem label="活动时间">{`${format.formatDateTimeDayjs(formData.rangeDate[0])}至${format.formatDateTimeDayjs(
          formData.rangeDate[1],
        )}`}</FormItem>
        <FormItem label="活动门槛">店铺会员</FormItem>
        {formData.taskRequestList.map((items, index1) => {
          return (
            <div key={index1}>
              <div {...formItemLayout} key={index1}>
                <div className={styles.awardTitle}>{GRADE_LABEL[index1].gradeName}会员</div>
                <div>
                  <div className={styles.skuListDiv}>
                    <div className={styles.labelTitle}>奖品类型：</div>
                    <div style={{ marginLeft: '8px' }}> {items.prizeType === 11 ? '单品促销令牌' : '实物'}</div>
                  </div>
                </div>
                {items.prizeType === 11 && (
                  <div>
                    <div className={styles.prizeListDiv11}>
                      <div className={styles.labelTitle}>添加令牌：</div>
                      <div className={styles.labelContent} style={{ marginLeft: '128px' }}>
                        {' '}
                        <ChoosePromotion
                          disabled
                          promoType={'1'}
                          index={index1}
                          value={formData.taskRequestList[index1].prizeList[0] ?? null}
                        />
                      </div>
                    </div>
                  </div>
                )}
                {
                  items.prizeType === 11 && (
                    <div className={styles.skuListDiv}>
                      <div className={styles.labelTitle}>奖品图：</div>
                      <div className={styles.labelContent} style={{ marginLeft: '8px' }}>
                        {formData.taskRequestList[index1].prizeList[0]?.prizeImg && (
                          <img style={{ width: '30px' }} src={formData.taskRequestList[index1].prizeList[0].prizeImg} alt="" />
                        )}
                      </div>
                    </div>
                  )
                }
                {items.prizeType === 11 && (
                  <div className={styles.skuListDiv}>
                    <div className={styles.labelTitle}>发放份数：</div>
                    <div className={styles.labelContent} style={{ marginLeft: '8px' }}>
                      {formData.taskRequestList[index1].prizeList[0].sendTotalCount}人
                    </div>
                  </div>
                )}
                {items.prizeType === 11 && (
                  <div className={styles.skuListDiv}>
                    <div className={styles.labelTitle}>参与活动正装商品：</div>
                    <div className={styles.labelContent} style={{ marginLeft: '8px' }}>
                        <div>
                          指定商品
                          <SkuList skuList={formData.taskRequestList[index1].mainSkuList} />
                        </div>
                    </div>
                  </div>
                )}
                {items.prizeType === 3 && (
                  <div>
                    <div className={styles.skuListDiv}>
                      <div className={styles.labelTitle}>实物奖品列表：</div>
                      <div className={styles.labelContent} style={{ marginLeft: '8px' }}>
                        <Table dataSource={items.prizeList} style={{ marginTop: '15px' }}>
                          <Table.Column
                            title="位置"
                            dataIndex="prizeName"
                            width={50}
                            cell={(_, index, row) => <div>{index + 1}</div>}
                          />
                          <Table.Column title="奖品名称" dataIndex="prizeName" />
                          <Table.Column
                            title="奖品类型"
                            cell={(_, index, row) => (
                              <div style={{ cursor: 'pointer' }} onClick={() => goToPropertyList(row.prizeType)}>
                                {PRIZE_TYPE[row.prizeType]}
                              </div>
                            )}
                            dataIndex="prizeType"
                          />
                          <Table.Column
                            title="单位数量"
                            cell={(_, index, row) => <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>}
                          />
                          <Table.Column
                            title="发放份数"
                            cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>}
                          />
                          <Table.Column
                            title="单份价值(元)"
                            cell={(_, index, row) => <div>{row.unitPrice ? Number(row.unitPrice).toFixed(2) : ''}</div>}
                          />
                          <Table.Column
                            title="奖品图"
                            cell={(_, index, row) => <img style={{ width: '30px' }} src={row.prizeImg} alt="" />}
                          />
                        </Table>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          );
        })}
        <FormItem label="活动分享">{formData.shareStatus === 0 ? '关闭' : '开启'}</FormItem>
        {formData.shareStatus !== 0 && (
          <>
            <FormItem label="分享标题">{formData.shareTitle}</FormItem>
            <FormItem label="图文分享图片">
              <img src={formData.h5Img} style={{ width: '200px' }} alt="" />
            </FormItem>
            <FormItem label="京口令分享图片">
              <img src={formData.cmdImg} style={{ width: '200px' }} alt="" />
            </FormItem>
            <FormItem label="小程序分享图片">
              <img src={formData.mpImg} style={{ width: '200px' }} alt="" />
            </FormItem>
          </>
        )}
        <FormItem label="规则内容">
          <div className="rule-word-break" style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-all' }}>
            {formData.rules}
          </div>
        </FormItem>
      </Form>
    </div>
  );
};
