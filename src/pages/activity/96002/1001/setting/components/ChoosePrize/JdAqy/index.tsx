import { prizeFormLayout } from '@/components/ChoosePrize';
import LzImageSelector from '@/components/LzImageSelector';
import { numRegularCheckInt } from '@/utils';
import { Button, Dialog, Field, Form, Grid, Input, Message, NumberPicker, Radio } from '@alifd/next';
import React, { useReducer, useState } from 'react';
import format from '@/utils/format';
import active from '../assets/active.png';
import delIcon from '../assets/del-icon.png';
import notActive from '../assets/not-active.png';
import Plan from './components/Plan';
import styles from './index.module.scss';

export interface PrizeInfo {
  prizeName: string;
  prizeImg: string;
  prizeType: number;
  probability: number | string;
  unitCount: number;
  unitPrice: number;
  sendTotalCount: number;
  dayLimit: number;
  dayLimitType: number;
  ifPlan: number;
  startTime?: string;
  endTime?: string;
  startDate?: string;
  endDate?: string;
}

interface PropertyAqyProps {
  editValue: PrizeInfo | null;
  onChange: (data: PrizeInfo) => void;
  onCancel: () => void;
  planList?: string[];
  hasProbability: boolean;
  hasLimit: boolean;
  width: number;
  height: number;
}

const PropertyAqy = ({
  editValue,
  onChange,
  onCancel,
  planList = [],
  hasProbability = true,
  hasLimit = true,
  width,
  height,
}: PropertyAqyProps) => {
  const equityImg = '//img10.360buyimg.com/imgzone/jfs/t1/122532/31/36227/18809/648a7fd6F5d05087e/733b2edfb5b48840.png';
  const planImg = require('../assets/9.jpg');
  const defaultValue = {
    prizeKey: null,
    prizeType: 10,
    dayLimitType: 1,
    dayLimit: 1,
    prizeImg: equityImg,
    prizeName: '爱奇艺会员卡',
  };
  const [prizeData, setPrizeData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, editValue || defaultValue);
  const field: Field = Field.useField();

  const [winJdShow, setWinJdShow] = useState(false); // 京豆详情
  // 发放份数/单次发放量
  const setData = (data: Partial<PrizeInfo>): void => {
    setPrizeData({
      ...prizeData,
      ...data,
    });
  };
  const submit = (values, errors): boolean | void => {
    if (prizeData.sendTotalCount > prizeData.quantityRemain) {
      Message.error(`发放份数不能大于库存${prizeData.quantityRemain}`);
      return false;
    }
    if (hasLimit && prizeData.dayLimitType === 2 && prizeData.sendTotalCount < prizeData.dayLimit) {
      Message.error(`每日发放限额份数不能大于发放总份数，请重新设置`);
      return false;
    }
    if (hasProbability && +prizeData.probability <= 0) {
      Message.error(`中奖概率必须大于0`);
      return false;
    }
    !errors && onChange(prizeData);
  };
  const delPlan = (): void => {
    const data = { ...prizeData };
    data.prizeKey = null;
    setPrizeData(data);
    // 删除计划
    if (planList.indexOf(prizeData.planId) > -1) {
      planList.splice(planList.indexOf(prizeData.planId), 1);
    }
  };
  const onSubmit = (resource: any): any => {
    if (!resource) {
      return;
    }
    // 选择计划判断
    if (planList.indexOf(resource.planId) === -1) {
      // 添加计划记录
      planList.push(resource.planId);
      resource.prizeKey = resource.planId;
      resource.unitPrice = resource.amount / 100;
      setPrizeData(resource);
      setWinJdShow(false);
      field.setErrors({ prizeKey: '' });
    } else {
      Message.error('该计划已被选择，请重新选择');
      return false;
    }
  };
  const itemMap = {
    8: '月',
    12: '季',
    13: '半年',
    14: '年',
  };
  return (
    <div className={styles.PropertyAqy}>
      <Form field={field} {...prizeFormLayout}>
        <Form.Item required requiredMessage="请选择爱奇艺会员" style={{ paddingTop: '15px' }}>
          <Input htmlType="hidden" name="prizeKey" value={prizeData.prizeKey} />
          {!prizeData.prizeKey && (
            <div className={styles.selectActivity} style={{ marginTop: 10 }} onClick={() => setWinJdShow(true)}>
              <img style={{ width: '35%' }} src={planImg} alt="" />
            </div>
          )}
          {!!prizeData.prizeKey && (
            <div className={prizeData.planStatus === 2 ? styles.beanPrizeBg : styles.beanPrizeBgy}>
              <img className={styles.prizeBg} src={prizeData.planStatus === 2 ? active : notActive} alt="" />
              <div
                onClick={() => delPlan()}
                style={{ backgroundImage: `url(${delIcon})` }}
                className={styles.delIcon}
              />
              <div style={{ width: 210 }}>
                <p>爱奇艺计划名称：{prizeData.planName}</p>
                <p>爱奇艺计划ID：{prizeData.planId}</p>
                <p>创建时间：{format.formatDateTimeDayjs(prizeData.createTime)}</p>
              </div>
              <div style={{ paddingLeft: 60 }}>
                <p>
                  有效期：{format.formatDateTimeDayjs(prizeData.startDate)}至
                  {format.formatDateTimeDayjs(prizeData.endDate)}
                </p>
                <p>
                  面额：{(prizeData.amount / 100).toFixed(2)}（{itemMap[prizeData.itemId]}卡）
                </p>
                <p>剩余数量：{prizeData.quantityRemain}份</p>
              </div>
            </div>
          )}
        </Form.Item>

        <Form.Item label="单份价值" required requiredMessage="请输入单份价值">
          <NumberPicker
            className={styles.formNumberPicker}
            onChange={(unitPrice) => setData({ unitPrice })}
            name="unitPrice"
            type="inline"
            min={0}
            precision={2}
            max={9999999}
            value={prizeData.unitPrice}
          />
          元
        </Form.Item>
        <Form.Item label="发放份数" required requiredMessage="请输入发放份数" validator={numRegularCheckInt}>
          <NumberPicker
            className={styles.formNumberPicker}
            type="inline"
            min={1}
            max={9999999}
            step={1}
            name="sendTotalCount"
            value={prizeData.sendTotalCount}
            onChange={(sendTotalCount) => setData({ sendTotalCount, unitCount: 1 })}
          />
          份
        </Form.Item>
        {hasProbability && (
          <Form.Item label="中奖概率" required requiredMessage="请输入中奖概率">
            <Input
              name="probability"
              value={prizeData.probability}
              addonTextAfter="%"
              onChange={(probability) => {
                if (probability) {
                  const regex = /^(\d|[1-9]\d|100)(\.\d{0,3})?$/;
                  if (regex.test(probability)) {
                    setData({ probability });
                  }
                } else {
                  setData({ probability: '' });
                }
              }}
              className={styles.formInputCtrl}
            />
          </Form.Item>
        )}
        {hasLimit && (
          <Form.Item label="每日限额" required requiredMessage="请输入每日限额">
            {prizeData.dayLimitType === 2 && <Input htmlType="hidden" name="dayLimit" value={prizeData.dayLimit} />}
            <div>
              <Radio.Group
                defaultValue={prizeData.dayLimitType}
                onChange={(dayLimitType: number) => setData({ dayLimitType })}
              >
                <Radio id="1" value={1}>
                  不限制
                </Radio>
                <Radio id="2" value={2}>
                  限制
                </Radio>
              </Radio.Group>
              {prizeData.dayLimitType === 2 && (
                <div className={styles.panel}>
                  <NumberPicker
                    value={prizeData.dayLimit}
                    onChange={(dayLimit) => {
                      setData({ dayLimit });
                      field.setErrors({ dayLimit: '' });
                    }}
                    type="inline"
                    min={1}
                    max={9999999}
                    className={styles.number}
                  />
                  份
                </div>
              )}
            </div>
          </Form.Item>
        )}

        <Form.Item label="奖品名称" required requiredMessage="请输入奖品名称">
          <Input
            className={styles.formInputCtrl}
            maxLength={10}
            showLimitHint
            placeholder="请输入奖品名称"
            name="prizeName"
            trim
            value={prizeData.prizeName}
            onChange={(prizeName) => setData({ prizeName })}
          />
        </Form.Item>
        <Form.Item label="奖品图片">
          <Grid.Row>
            <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
              <LzImageSelector
                width={width}
                height={height}
                value={prizeData.prizeImg}
                onChange={(prizeImg) => {
                  setData({ prizeImg });
                }}
              />
            </Form.Item>
            <Form.Item style={{ marginBottom: 0 }}>
              <div className={styles.tip}>
                <p>
                  图片尺寸：{width}px*{height || '任意'}px
                </p>
                <p>图片大小：不超过1M</p>
                <p>图片格式：JPG、JPEG、PNG</p>
              </div>
              <div>
                <Button
                  type="primary"
                  text
                  onClick={() => {
                    setData({ prizeImg: equityImg });
                  }}
                >
                  重置
                </Button>
              </div>
            </Form.Item>
          </Grid.Row>
        </Form.Item>
        <Form.Item>
          <Grid.Row>
            <Grid.Col style={{ textAlign: 'right' }}>
              <Form.Submit className="form-btn" validate type="primary" onClick={submit}>
                确认
              </Form.Submit>
              <Form.Reset className="form-btn" onClick={onCancel}>
                取消
              </Form.Reset>
            </Grid.Col>
          </Grid.Row>
        </Form.Item>
      </Form>
      <Dialog
        title="选择爱奇艺会员"
        footer={false}
        shouldUpdatePosition
        visible={winJdShow}
        onClose={() => setWinJdShow(false)}
      >
        <Plan onSubmit={onSubmit} />
      </Dialog>
    </div>
  );
};

export default PropertyAqy;
