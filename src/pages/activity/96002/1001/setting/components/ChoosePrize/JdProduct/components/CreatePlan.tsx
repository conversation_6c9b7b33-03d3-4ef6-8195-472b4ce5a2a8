import styles from '../index.module.scss';
import { Button, Form, Input, NumberPicker, Message, Loading } from '@alifd/next';
import { useState } from 'react';
import * as React from 'react';
import { ComponentProps, CreateProduct } from '../types';
import { addResPrizeSku } from '@/api/prize';

export default ({ handleCancel, handleSubmit }: ComponentProps) => {
  const formItemLayout = {
    labelCol: { span: 5 },
    wrapperCol: { span: 19 },
  };
  const [loading, setLoading] = React.useState<boolean>(false);

  const [productValue, setProductValue] = useState(0);
  const [wmsCode, setWmsCode] = useState(); // 商家内部编号

  const validateWmsCode = (value) => {
    if (!/^[0-9a-zA-Z]*$/g.test(value)) {
      value = wmsCode;
    }
    console.log(value);
    setWmsCode(value);
  };

  const onOkCreate = (value: CreateProduct, errors: unknown): void | boolean => {
    if (!errors) {
      if (+value.quantityTotal <= 0) {
        Message.error('库存总量必须大于0');
        return false;
      }
      setLoading(true);
      addResPrizeSku(value)
        .then(() => {
          Message.success('添加成功');
          setLoading(false);
          handleSubmit();
        })
        .catch((err) => {
          setLoading(false);
          Message.error(err.message);
        });
    }
  };
  // 关闭弹窗
  const onCloseCreate = () => {
    handleCancel();
  };

  return (
    <div className={styles.CreateJdBeanPlan}>
      <Loading visible={loading}>
        <Form {...formItemLayout} style={{ width: 600 }}>
          <Form.Item label="实物名称：" required className={styles.item} requiredMessage="请输入实物名称">
            <Input trim placeholder="请输入实物名称" maxLength={20} showLimitHint name="skuName" />
          </Form.Item>
          <Form.Item label="商家内部编号：" className={styles.item}>
            <Input
              trim
              placeholder="用于与商家发货系统对接时使用，作为唯一发货标识"
              maxLength={20}
              showLimitHint
              name="wmsCode"
              value={wmsCode}
              onChange={(e) => validateWmsCode(e)}
            />
          </Form.Item>
          <Form.Item label="实物描述：" className={styles.item}>
            <Input.TextArea placeholder="请输入实物描述" maxLength={200} showLimitHint name="demo" />
          </Form.Item>
          <Form.Item label="库存总量：" required requiredMessage="请输入库存总量">
            <NumberPicker
              value={productValue}
              onChange={(quantityTotal) => setProductValue(quantityTotal)}
              type="inline"
              min={0}
              max={9999999}
              name="quantityTotal"
              style={{ width: '200px' }}
            />
            件
          </Form.Item>
          <Form.Item label=" ">
            <Form.Submit validate type="primary" className={styles.form_btn} onClick={onOkCreate}>
              提交
            </Form.Submit>
            <Button className={styles.form_btn} onClick={onCloseCreate} style={{ marginLeft: '15px' }}>
              取消
            </Button>
          </Form.Item>
        </Form>
      </Loading>
    </div>
  );
};
