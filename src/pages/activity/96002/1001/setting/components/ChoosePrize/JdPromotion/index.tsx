import { Form, NumberPicker, Grid, Field, Message, Icon } from '@alifd/next';
import React, { useReducer, useState } from 'react';
import styles from './index.module.scss';
import ChoosePromotion from '@/components/ChoosePromotion';
import ChooseGoods from '@/components/ChooseGoods';
import LzDialog from '@/components/LzDialog';
import { FormLayout } from '@/pages/activity/20003/2002/util';

interface ComponentProps {
  [propName: string]: any;
}

const formItemLayout: FormLayout = {
  labelCol: {
    span: 5,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

const PropertyJdPromotion = ({
  editValue,
  onChange,
  onCancel,
  hasLimit = true,
  hasProbability = true,
  formData,
  sendTotalCountMax = 999999999,
  model,
}: ComponentProps) => {
  const equityImg = '';
  const defaultValue = {
    prizeKey: null,
    prizeType: 11,
    dayLimitType: 1,
    dayLimit: 1,
    prizeImg: equityImg,
    unitCount: 1,
  };
  const field = Field.useField();

  const [showIconDialog, setShowIconDialog] = useState(false);
  const [prizeData, setPrizeData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, editValue || defaultValue);

  // 发放份数/单次发放量
  const setData = (data: any) => {
    setPrizeData({
      ...prizeData,
      ...data,
    });
  };

  const submit = (values: any, errors: any): boolean | void => {
    if (!prizeData.tokenPrize) {
      Message.error(`请选择令牌`);
      return false;
    }

    if (!prizeData.skuList) {
      Message.error(`请选择商品`);
      return false;
    }

    if (hasLimit && prizeData.dayLimitType === 2 && prizeData.sendTotalCount < prizeData.dayLimit) {
      Message.error(`每日发放限额份数不能大于发放总份数，请重新设置`);
      return false;
    }
    if (hasProbability && +prizeData.probability <= 0) {
      Message.error(`中奖概率必须大于0`);
      return false;
    }
    if (hasProbability && +prizeData.probability + +formData.totalProbability >= 100) {
      Message.error(`中奖总概率不能超过100`);
      return false;
    }
    !errors && onChange(prizeData);
  };

  // 选择SKU
  const chooseSku = (skuList) => {
    setData({
      prizeImg: skuList[0]?.skuMainPicture,
      skuId: skuList[0].skuId,
      skuMainPicture: skuList[0].skuMainPicture,
      skuName: skuList[0].skuName,
      skuList,
    });
  };

  // 展示弹窗
  const toIconDialog = () => {
    setShowIconDialog(true);
  };

  // 选择令牌
  const onTokenChange = (data): boolean | void => {
    if (data) {
      prizeData.tokenPrize = data;
      setData({ prizeName: data.prizeName, prizeKey: data.prizeKey });
      // console.log('prizeData1', prizeData.tokenPrize);
    } else {
      prizeData.tokenPrize = null;
    }
    setData(prizeData);
    setData({ prizeKey: data.prizeKey });
  };

  return (
    <div className={styles.PropertyJdPromotion}>
      <Form field={field} {...formItemLayout}>
        <Form.Item label="选择令牌" required>
          <ChoosePromotion promoType={'1'} submit={onTokenChange} value={prizeData.tokenPrize ?? null} />
          <div className={styles.formItemExtra}>
            注意：在申请令牌时，注意勾选允许使用1次，避免一个令牌多次使用
            <Icon
              style={{ color: '#FF3333', marginLeft: '10px', cursor: 'pointer' }}
              type="help"
              onClick={toIconDialog}
            />
          </div>
        </Form.Item>
        <Form.Item label="选择商品" required requiredMessage={'添加商品'}>
          <ChooseGoods mode={model} value={prizeData.skuList} onChange={chooseSku} max={1} />
          <div className={styles.formItemExtra}>
            注意：商品必须选择与令牌绑定的SKU，且设置了SKU的限购数量，否则用户会无限购买
          </div>
        </Form.Item>
        <Form.Item label="划线价" required requiredMessage="请输入划线价">
          <NumberPicker
            className={styles.formNumberPicker}
            name="originalPrize"
            min={1}
            max={9999999}
            step={1}
            precision={2}
            type="inline"
            value={prizeData.originalPrize}
            onChange={(originalPrize) => {
              setData({ originalPrize });
            }}
          />
          元
        </Form.Item>
        <Form.Item label="折扣价" required requiredMessage="请输入折扣价">
          <NumberPicker
            className={styles.formNumberPicker}
            name="discountedPrize"
            min={1}
            max={9999999}
            step={1}
            precision={2}
            type="inline"
            value={prizeData.discountedPrize}
            onChange={(discountedPrize) => {
              setData({ discountedPrize });
            }}
          />
          元
        </Form.Item>
        {/* <Form.Item label="单份价值" required requiredMessage="请输入单份价值"> */}
        {/*  <NumberPicker */}
        {/*    className={styles.formNumberPicker} */}
        {/*    onChange={(unitPrice: any) => setData({ unitPrice })} */}
        {/*    name="unitPrice" */}
        {/*    type="inline" */}
        {/*    precision={2} */}
        {/*    max={9999999} */}
        {/*    min={1} */}
        {/*    value={prizeData.unitPrice} */}
        {/*  /> */}
        {/*  元 */}
        {/* </Form.Item> */}
        <Form.Item label="令牌发放总份数" required requiredMessage="请输入令牌发放总份数">
          <NumberPicker
            className={styles.formNumberPicker}
            name="sendTotalCount"
            min={1}
            max={sendTotalCountMax}
            step={1}
            type="inline"
            value={prizeData.sendTotalCount}
            onChange={(sendTotalCount) => {
              setData({ sendTotalCount });
            }}
          />
          份
        </Form.Item>
        <Form.Item>
          <Grid.Row>
            <Grid.Col style={{ textAlign: 'right' }}>
              <Form.Submit className="form-btn" validate type="primary" onClick={submit}>
                确认
              </Form.Submit>
              <Form.Reset className="form-btn" onClick={onCancel}>
                取消
              </Form.Reset>
            </Grid.Col>
          </Grid.Row>
        </Form.Item>
      </Form>
      <LzDialog
        title={false}
        visible={showIconDialog}
        footer={false}
        onClose={() => setShowIconDialog(false)}
        style={{ width: '80%' }}
      >
        <img
          style={{ width: '100%' }}
          src="//img10.360buyimg.com/imgzone/jfs/t1/112695/34/41067/44228/65e19000F65eb2b97/3f6c3723766cb9c8.png"
          alt=""
        />
      </LzDialog>
    </div>
  );
};

export default PropertyJdPromotion;
