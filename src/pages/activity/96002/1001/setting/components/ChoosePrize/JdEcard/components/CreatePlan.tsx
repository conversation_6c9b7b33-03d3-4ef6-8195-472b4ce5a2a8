import styles from '../index.module.scss';
import { Button, Form, Input, NumberPicker, Message, DatePicker, Select, Loading } from '@alifd/next';
import { useState, useEffect } from 'react';
import * as React from 'react';
import { addResPrizeECard, getPrizeQuantityBalance } from '@/api/prize';
import { GetPrizeQuantityBalanceResponse } from '@/api/types';

const { RangePicker } = DatePicker;

interface GetPrizeQuantityBalanceResponseExtend extends GetPrizeQuantityBalanceResponse {
  key: number;
}

export default ({ handleCancel, handleSubmit }) => {
  const [loading, setLoading] = React.useState<boolean>(false);
  const formItemLayout = {
    labelCol: { span: 5 },
    wrapperCol: { span: 19 },
  };
  const [field, setField] = useState({
    value: 2,
  });
  // 总发放份数
  const [redNum, setRedNum] = useState('');
  // 可用数量
  const [accountData, setAccountData] = useState<GetPrizeQuantityBalanceResponseExtend[]>([]);

  const changeField = (data) => {
    setField({
      ...field,
      ...data,
    });
  };

  const onOkCreate = (value, errors: unknown): void | boolean => {
    if (!errors) {
      const valueForm = {
        ...value,
        quantity: redNum,
        sendRule: 1,
        beginDate: value.putTime[0].format('YYYY-MM-DD HH:mm:ss'),
        endDate: value.putTime[1].format('YYYY-MM-DD HH:mm:ss'),
        itemId: field.value,
      };
      if (new Date(valueForm.beginDate).getTime() < new Date().getTime()) {
        Message.error('投放起始时间要大于当前时间');
        return;
      }
      setLoading(true);
      addResPrizeECard(valueForm)
        .then(() => {
          Message.success('创建成功');
          setLoading(false);
          handleSubmit();
        })
        .catch((err) => {
          setLoading(false);
          Message.error(err.message);
        });
    }
  };
  // 关闭弹窗
  const onCloseCreate = () => {
    handleCancel();
  };
  // 校验时间
  const checkPutDate = (rule, value, callback) => {
    console.log(value);
    if (value[0] !== null && new Date().getTime() > new Date(value[0]).getTime()) {
      callback('投放时间需要大于当前时间');
    } else if (value[1] === null) {
      callback('请选择投放结束时间');
    } else {
      callback();
    }
  };
  // 校验计划总量
  const checkJH = (rule, value, callback) => {
    console.log(`当前面值卡余额：${(accountData[field.value - 2] as any).quantityBalance}`);
    if (value !== '' && value > (accountData[field.value - 2] as any).quantityBalance) {
      callback('计划总量不能超过可用张数');
    } else if (value === '') {
      callback('请输入计划总量');
    } else {
      callback();
    }
  };
  const testValueZ = (val) => {
    // val = val.replace(/[^\\.\d]/g, '').replace('.', '');
    setRedNum(val);
  };

  const getAccountData = (resType) => {
    getPrizeQuantityBalance({ resType }).then((res) => {
      res.forEach((e: any) => {
        e.key = Number(e.itemName.replace('元', ''));
      });
      console.log(res);
      setAccountData(res as any);
    });
  };

  useEffect(() => {
    getAccountData(2);
  }, []);

  return (
    <div className={styles.CreateJdBeanPlan}>
      <Loading visible={loading}>
        <Form {...formItemLayout} style={{ width: 600 }}>
          <Form.Item label="计划名称：" required className={styles.item} requiredMessage="请输入计划名称">
            <Input trim placeholder="请输入计划名称" maxLength={20} showLimitHint name="planName" />
          </Form.Item>
          <Form.Item
            label="投放时间："
            required
            className="bean_btn"
            requiredMessage="请选择投放时间"
            validator={checkPutDate}
          >
            <RangePicker showTime name="putTime" />
          </Form.Item>
          <Form.Item label="计划内容：" className={styles.item}>
            <Input.TextArea placeholder="" maxLength={200} showLimitHint name="planContent" />
          </Form.Item>
          <Form.Item
            label="京东E卡会员："
            required
            className="bean_btn"
            requiredMessage="请选择京东E卡会员"
            help={
              <div style={{ color: 'red', textAlign: 'left' }}>
                可用量：{accountData.length ? accountData[field.value - 2].quantityBalance : 0}张
              </div>
            }
          >
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <Select
                className={styles.itemc}
                name="quantity"
                style={{ width: 120 }}
                defaultValue={2}
                value={field.value}
                onChange={(value) => {
                  changeField({ value });
                }}
              >
                <Select.Option value={2}>1元</Select.Option>
                <Select.Option value={3}>5元</Select.Option>
                <Select.Option value={4}>20元</Select.Option>
                <Select.Option value={5}>50元</Select.Option>
                <Select.Option value={6}>100元</Select.Option>
              </Select>
            </div>
          </Form.Item>
          <Form.Item
            label="计划总量："
            required
            className="bean_btn"
            requiredMessage="请输入京东E卡会员计划总量"
            validator={checkJH}
          >
            <NumberPicker
              style={{ width: 200 }}
              placeholder="请输入京东E卡会员计划总量"
              value={redNum}
              onChange={(value) => testValueZ(value)}
              name="quantity"
              min={1}
              max={accountData.length ? accountData[field.value - 2].quantityBalance : 0}
            />
            &nbsp;张
            <span style={{ color: 'red', textAlign: 'left', marginLeft: 10 }}>
              总费用：{accountData.length && redNum ? accountData[field.value - 2].key * +redNum : 0}元
            </span>{' '}
          </Form.Item>

          <Form.Item label=" " className="bean_btn">
            <Form.Submit validate type="primary" onClick={onOkCreate}>
              提交
            </Form.Submit>
            <Button onClick={onCloseCreate} style={{ marginLeft: 15 }}>
              取消
            </Button>
          </Form.Item>
        </Form>
      </Loading>
    </div>
  );
};
