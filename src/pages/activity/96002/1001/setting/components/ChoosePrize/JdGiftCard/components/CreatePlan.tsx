import styles from '../index.module.scss';
import { Button, Form, Input, DatePicker, Radio, Checkbox, Upload, Message } from '@alifd/next';
import React, { useState } from 'react';
import CONST from '@/utils/constant';
import ImageChoosePic from './assets/image-select.png';
import { config } from 'ice';
import moment from 'moment';
import LzJDImageSelector from '@/components/LzJDImageSelector';
import LzDialog from '@/components/LzDialog';
import { prizeCardTemplateExport } from '@/api/prize';
import { downloadExcel } from '@/utils';

export default ({ handleCancel, handleSubmit }) => {
  const [infoXlsx, setInfoXlsx] = useState<any>({});
  const [uploaderRef, setUploaderRef] = useState<any>({});
  const [imageWinShow, setImageWinShow] = useState<boolean | null>();
  const [createform, setCreateform] = useState({
    validNoEndTime: 0,
    cardName: '',
    time: [],
    cardType: 0,
    fileName: '',
    cardDesc: '',
    prizePicture: '',
  });
  const formItemLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 20 },
  };
  // 关闭弹窗
  const onCloseCreate = () => {
    handleCancel();
  };
  // 新建礼品卡数据赋值
  const onChange = (value) => {
    setCreateform({
      ...createform,
      ...value,
    });
  };
  const selectImage = (index = 0) => {
    setImageWinShow(true);
  };

  const saveUploaderRef = (ref) => {
    if (!ref) {
      return;
    }
    setUploaderRef(ref.getInstance());
  };

  const afterSelect = (info) => {
    console.log('afterSelect callback : ', info);
    setInfoXlsx(info.originFileObj);
    if (
      info.type !== 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' &&
      info.type !== 'application/vnd.ms-excel'
    ) {
      Message.error('请上传xlsx类型的文件');
      return false;
    } else {
      return true;
    }
  };
  // 下载模板
  const downLoadFile = () => {
    prizeCardTemplateExport({
      cardType: createform.cardType,
    }).then((data: any) => {
      downloadExcel(data, `激活码录入模板${moment().format('MMDD')}`);
    });
  };

  const onCreate = (value) => {
    if (!createform.cardName) {
      Message.error('请输入礼品卡名称');
      return;
    }
    if (createform.validNoEndTime === 0 && (!createform.time[0] || !createform.time[1])) {
      Message.error('请输入有效期');
      return;
    }
    if (!createform.prizePicture) {
      Message.error('请上传卡片背景图');
      return;
    }
    if (!infoXlsx.name || uploaderRef.state.value.length === 0) {
      Message.error('请导入激活码');
      return;
    }
    if (!createform.cardDesc) {
      Message.error('请输入礼品卡描述');
      return;
    }
    if (uploaderRef?.state.value[0]?.state === 'error') {
      const msg = uploaderRef?.state.value[0]?.response.message;
      Message.error(msg);
      return;
    }
    uploaderRef.startUpload();
  };
  return (
    <div className={styles.CreateJdBeanPlan}>
      <Form {...formItemLayout} className={styles.dialogForm} style={{ width: '600px' }}>
        <Form.Item label=" 礼品卡名称：" required requiredMessage="礼品卡名称是必填字段" className={styles.item}>
          <Input
            className={styles.itemc}
            placeholder="请输入礼品卡名称"
            maxLength={20}
            showLimitHint
            name="cardName"
            value={createform.cardName}
            onChange={(cardName) => {
              onChange({ cardName });
            }}
          />
        </Form.Item>
        <Form.Item
          label="有效期："
          required={!createform.validNoEndTime}
          className={styles.item}
          requiredMessage="请输入有效期"
        >
          <DatePicker.RangePicker
            style={{ width: '375px' }}
            name="time"
            showTime
            disabled={!!createform.validNoEndTime}
            value={createform.time}
            onChange={(time) => {
              onChange({ time });
            }}
          />
          <Checkbox
            style={{ marginLeft: 10 }}
            checked={!!createform.validNoEndTime}
            onChange={(validNoEndTime) => {
              onChange({ validNoEndTime: +validNoEndTime });
            }}
          >
            永久有效
          </Checkbox>
        </Form.Item>
        <Form.Item
          label="卡片背景图："
          required
          className={styles.item}
          requiredMessage="请设置卡片背景图"
          validator={(rule, value) => {
            return new Promise((resolve, reject) => {
              if (createform.prizePicture !== '') {
                resolve(1);
              } else {
                reject(new Error('请设置卡片背景图'));
              }
            });
          }}
        >
          <Input name="prizePicture" style={{ display: 'none' }} value={JSON.stringify(createform.prizePicture)} />
          <div className="image-previews">
            {createform.prizePicture !== '' ? (
              <div
                className="image-preview"
                style={{
                  marginLeft: 0,
                  backgroundImage: `url(${CONST.IMAGE_PREFIX}${createform.prizePicture})`,
                }}
              >
                <div className="del-box">
                  <Button type="primary" warning onClick={() => onChange({ prizePicture: '' })}>
                    删除
                  </Button>
                </div>
              </div>
            ) : (
              <div
                className="image-preview"
                onClick={() => selectImage()}
                style={{
                  marginLeft: 0,
                  backgroundImage: `url(${ImageChoosePic})`,
                }}
              />
            )}
          </div>
          <p className="tip">注：图片尺寸为690*351像素</p>
        </Form.Item>
        <Form.Item label="激活方式：" required placeholder="请选择激活方式" className={styles.item}>
          <Radio.Group
            name="type"
            value={createform.cardType}
            onChange={(cardType) => {
              onChange({ cardType });
            }}
          >
            <Radio value={0}>仅卡号</Radio>
            <Radio value={1}>卡号+密码</Radio>
          </Radio.Group>
        </Form.Item>
        <Form.Item label="导入激活码：" required className={styles.item}>
          <div style={{ display: 'flex' }}>
            <Upload
              style={{ textOverflow: 'ellipsis', overflow: 'hidden', display: 'block' }}
              action={`${config.baseURL}/prize/addResPrizeCard`}
              autoUpload={false}
              withCredentials={false}
              name="upload"
              method="post"
              headers={{
                token: localStorage.getItem(CONST.LZ_SSO_TOKEN),
                prd: localStorage.getItem(CONST.LZ_SSO_PRD),
              }}
              data={{
                ...createform,
                prizePicture: `${CONST.IMAGE_PREFIX}${createform.prizePicture}`,
                startTime:
                  createform.validNoEndTime === 1
                    ? ''
                    : createform.time[0]
                    ? `${moment(createform.time[0]).format('YYYY-MM-DD HH:mm:ss')}`
                    : '',
                endTime:
                  createform.validNoEndTime === 1
                    ? ''
                    : createform.time[1]
                    ? `${moment(createform.time[1]).format('YYYY-MM-DD HH:mm:ss')}`
                    : '',
                cardFileInputStream: infoXlsx,
                fileName: infoXlsx.name,
              }}
              listType="text"
              ref={saveUploaderRef}
              afterSelect={afterSelect}
              useDataURL
              limit={1}
              onError={(res) => {
                console.log(res);
                if (res.state === 'error') {
                  if (res.response?.message) {
                    Message.error(res.response?.message);
                  } else {
                    Message.error('文件错误，请上传正确的文件');
                  }
                }
              }}
              onSuccess={(res) => {
                Message.success('创建成功');
                handleSubmit();
              }}
            >
              <Button className="table-cell-btn" type="secondary">
                上传文件
              </Button>
            </Upload>
            <Button type="primary" style={{ marginLeft: '10px', marginTop: 6 }} text onClick={downLoadFile}>
              下载模板
            </Button>
          </div>
          <div style={{ marginTop: '10px', color: '#999' }}>
            注：导入文件为xlsx格式，请下载模板后用模板导入激活码，导入激活码数量不可超过10万条
          </div>
        </Form.Item>
        <Form.Item label="数量：" required className={styles.item}>
          <p>-</p>
        </Form.Item>
        <Form.Item label="礼品卡描述：" required requiredMessage="礼品卡描述是必填字段" className={styles.item}>
          <Input.TextArea
            maxLength={200}
            showLimitHint
            placeholder="请输入礼品卡描述"
            name="cardDesc"
            value={createform.cardDesc}
            onChange={(cardDesc) => {
              onChange({ cardDesc });
            }}
          />
        </Form.Item>
        <Form.Item className="item allBtns" label="">
          <Form.Submit type="primary" onClick={onCreate} style={{ marginLeft: 124, marginRight: 15 }}>
            提交
          </Form.Submit>
          <Button onClick={onCloseCreate}>关闭</Button>
        </Form.Item>
      </Form>
      <LzDialog
        footer={false}
        shouldUpdatePosition
        title="选择图片"
        className="lz-dialog-larger"
        visible={imageWinShow}
        onClose={() => setImageWinShow(false)}
      >
        <LzJDImageSelector
          width={690}
          height={351}
          onSelect={(url, pixel) => {
            if (pixel !== '690x351') {
              Message.error('请上传图片尺寸为690*351像素的图片');
              return;
            }
            const prizePicture = [createform.prizePicture];
            prizePicture[0] = url;
            onChange({ prizePicture });
            setImageWinShow(false);
          }}
          onCloseTree={() => setImageWinShow(false)}
        />
      </LzDialog>
    </div>
  );
};
