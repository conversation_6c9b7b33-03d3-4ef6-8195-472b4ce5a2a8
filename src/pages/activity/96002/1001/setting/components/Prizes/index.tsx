import React, { useReducer, useEffect, useImperativeHandle, useState } from 'react';
import {
  Form,
  Field,
  Input,
  Radio,
  Grid,
  Button,
  Table,
  Dialog, NumberPicker,
} from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import ChoosePromotion from '@/components/ChoosePromotion';
import {activityEditDisabled, deepCopy, getParams, isDisableSetPrize} from '@/utils';
import {
  FormLayout,
  PageData,
  GRADE_LABEL,
  GIVEAWAY_INFO,
  PRIZE_INFO,
  PRIZE_TYPE,
  TaskRequestInfo,
} from '../../../util';
import ChooseGoods from '@/components/ChooseGoods';
import styles from '../../style.module.scss';
import LzDialog from '@/components/LzDialog';
import ChoosePrize from '../ChoosePrize';
import SkuList from '@/components/SkuList';
import LzImageSelector from "@/components/LzImageSelector";
import { getPrizeRemain } from "@/api/v96002";

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}

const FormItem = Form.Item;
const RadioGroup = Radio.Group;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

export default ({ sRef, onChange, defaultValue, value }: Props) => {
  // 选择奖品
  const [visible, setVisible] = useState(false);
  // 当前编辑行数据
  const [editValue, setEditValue] = useState(null);
  // 当前编辑赠品行index
  const [giveaTarget, setGiveaTarget] = useState(0);
  // 已经选择的奖品
  const [plan, setPlan] = useState<any[]>([]);
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  const field: Field = Field.useField();

  // 同步/更新数据
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  /**
   * 选择令牌
   * @param data 当前编辑奖品信息
   */
  const onPrizeChange = (data, index1): boolean | void => {
    // console.log(data, '选择令牌data==========');
    if (data) {
      data.sendTotalCount = formData.taskRequestList[index1].prizeList[0].sendTotalCount;
      // 更新指定index 奖品信息
      formData.taskRequestList[index1].prizeList[0] = data;
    } else {
      formData.taskRequestList[index1].prizeList[0] = PRIZE_INFO;
    }
    // 需要将所有会员等级下的prizeList合并到一个数组总，为了做选择奖品时候的去重校验
    let list1 = [];
    formData.taskRequestList.forEach((item: any, index: number) => {
      list1 = list1.concat(item.prizeList[0]);
    });
    const listNew = list1.map((item: any) => item.prizeKey);
    setPlan(listNew);
    setData(formData);
    setVisible(false);
  };

  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);

  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: object | null = null;
      field.validate((errors): void => {
        err = errors;
      });
      return err;
    },
  }));

  // 订单商品相关   令牌
  const handleOrderSkuChange = (data, index1) => {
    formData.taskRequestList[index1].mainSkuList = data;
    setData(formData);
  };

  // 奖品类型选择
  const radioPrizeGroupChange = (prizeType, items, index1) => {
    formData.taskRequestList[index1].prizeList[0] = deepCopy(PRIZE_INFO);
    let list1 = [];
    formData.taskRequestList.forEach((item: any, index: number) => {
      list1 = list1.concat(item.prizeList[0]);
    });
    const listNew = list1.map((item: any) => item.prizeKey);
    setPlan(listNew);
    formData.taskRequestList[index1].prizeType = prizeType;
    setData(formData);
  };

  // 选择除令牌外的奖品
  const onPrizeChangeOther = (data): boolean | void => {
    // 更新指定index 奖品信息
    formData.taskRequestList[giveaTarget].prizeList[0] = data;
    // 需要将所有会员等级下的prizeList合并到一个数组总，为了做选择奖品时候的去重校验
    let list1 = [];
    formData.taskRequestList.forEach((item: any, index: number) => {
      list1 = list1.concat(item.prizeList[0]);
    });
    const listNew = list1.map((item: any) => item.prizeKey);
    setPlan(listNew);
    setData(formData);
    setVisible(false);
  };

  const onCancel = (): void => {
    setVisible(false);
  };


  // 添加令牌时传递的值（为了降低圈复杂度，拿出来了）
  const getValue = (index1) => {
    return formData.taskRequestList[index1].prizeList &&
      formData.taskRequestList[index1].prizeList[0] &&
      formData.taskRequestList[index1].prizeList[0].prizeKey
      ? formData.taskRequestList[index1].prizeList[0]
      : null;
  };

  useEffect((): void => {
    if (formData.taskRequestList.length === 0) {
      // 生成默认奖品列表
      const taskRequestList: TaskRequestInfo[] = [...formData.taskRequestList];
      // 补齐奖品数到4
      for (let i = 0; i < 4; i++) {
        GIVEAWAY_INFO.prizeList[0] = deepCopy(PRIZE_INFO);
        taskRequestList[i] = deepCopy(GIVEAWAY_INFO);
        taskRequestList[i].level = GRADE_LABEL[i].gradeLevel;
      }
      console.log(taskRequestList, 'taskRequestList[i].level');
      setData({ taskRequestList: taskRequestList.length ? taskRequestList : formData.taskRequestList });
    }
  }, []);

  const editPrizes = async (prize: any, index) => {
    let row = prize[0];
    if (row.prizeName === '谢谢参与' || !row.prizeName) {
      row = null;
    } else if (getParams('type') === 'edit') {
      try {
        const data = await getPrizeRemain({
          prizeType: row.prizeType,
          activityId: getParams('id'),
          prizeKey: row.prizeKey,
        });
        const keyMap = {
          1: 'quantityRemain',
          2: 'quantityRemain',
          3: 'quantityAvailable',
          6: 'quantityRemain',
          7: 'cardSurplus',
          8: 'quantityRemain',
          9: 'quantityRemain',
          10: 'quantityRemain',
          12: 'quantityRemain',
        };
        row[keyMap[row.prizeType]] = +data >= 0 ? +data : 0;
      } catch (error) {
        console.error(error);
      }
    }
    setEditValue(row);
    setGiveaTarget(index);
    setVisible(true);
  };

  const editPrizeList = async (index: number) => {
    await editPrizes(formData.taskRequestList[index].prizeList, index);
    setVisible(true);
  };

  const handlePreview = (data, index1, prizeType) => {
    formData.taskRequestList[index1].skuListPreview = data;
    setData(formData);
  };
  return (
    <div>
      <LzPanel
        title="会员奖品设置"
        actions={
          <Button
            type="primary"
            text
            onClick={() => {
              window.open('https://www.yuque.com/luzekeji/bfc3uk/zm9hot5lmex11zc2');
            }}
          >
            令牌使用攻略
          </Button>
        }
      >
        <Form {...formItemLayout}>
          <FormItem label={' '} colon={false}>
            <div style={{color:'red'}}>
              【奖品选择重要提醒】<br/>
              1.专属匹配原则 → 您挑选的奖品必须为本活动单独准备，不可与其他活动共用！<br/>
              2.活动启动后 → 已绑定的奖品不可更换！<br/>
              3.库存调整规则 → 活动上线后，已绑定的奖品库存只能追加，不能下调！<br/>
            </div>
          </FormItem>
        </Form>
        {formData.taskRequestList.map((items, index1) => {
          return (
            <div key={index1}>
              <Form {...formItemLayout} field={field} key={index1}>
                <FormItem disabled={activityEditDisabled()} label="" required className={styles.awardTitle}>
                  {GRADE_LABEL[index1].gradeName}会员
                </FormItem>
                <FormItem label="奖品类型">
                  <RadioGroup
                    value={items.prizeType}
                    disabled={activityEditDisabled()}
                    onChange={(prizeType: number) => {
                      radioPrizeGroupChange(prizeType, items, index1);
                    }}
                  >
                    <Radio id="1" value={11}>
                      单品促销令牌（满减）
                    </Radio>
                    <Radio id="2" value={3}>
                      实物
                    </Radio>
                  </RadioGroup>
                </FormItem>
                {items.prizeType === 11 && (
                  // <div>
                  <FormItem label="添加令牌" required>
                    <ChoosePromotion
                      promoType={'10'}
                      submit={(val) => {
                        onPrizeChange(val, index1);
                      }}
                      index={index1}
                      planList={plan}
                      value={getValue(index1)}
                      disabled={activityEditDisabled()}
                    />
                  </FormItem>
                )}
                {items.prizeType === 11 && (
                  <Form.Item label="发放份数" required requiredMessage="请输入发放份数">
                    <NumberPicker
                      className={styles.formNumberPicker}
                      value={formData.taskRequestList[index1].prizeList[0].sendTotalCount}
                      onChange={(val) => {
                        formData.taskRequestList[index1].prizeList[0].sendTotalCount = val;
                        setData(formData);
                      }}
                      type="inline"
                      min={1}
                      max={9999999}
                    />
                    <div className={styles.tip}>
                      <div>1. 达到上限后，用户将不可参与此促销活动</div>
                      <div>2. 请根据累计最大参与人数预留赠品，请保证赠品库存充足；</div>
                    </div>
                  </Form.Item>
                )}
                {items.prizeType === 11 && (
                  <FormItem label="奖品图" required>
                    <Grid.Row>
                      <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                        <LzImageSelector
                          disabled={activityEditDisabled()}
                          value={formData.taskRequestList[index1].prizeList[0].prizeImg}
                          onChange={(prizeImg) => {
                            formData.taskRequestList[index1].prizeList[0].prizeImg = prizeImg;
                            setData(formData);
                          }}
                        />
                      </Form.Item>
                    </Grid.Row>
                  </FormItem>
                )}
                {items.prizeType === 11 && (
                  <FormItem label="参与活动正装商品" required>
                    <Grid.Row>
                        <FormItem required requiredMessage={'请选择指定商品'} style={{ marginTop: '15px' }}>
                          <ChooseGoods
                            disabled={activityEditDisabled()}
                            value={items.mainSkuList}
                            onChange={(data) => handleOrderSkuChange(data, index1)}
                            sRef={null}
                          />
                          <div className={styles.container}>
                            <SkuList
                              skuList={items.mainSkuList}
                              handlePreview={(data) => {
                                handlePreview(data, index1, items.prizeType);
                              }}
                            />
                          </div>
                          <Input className="validateInput" name={`mainSkuList${index1}`} value={items.mainSkuList} />
                        </FormItem>
                      {items.mainGoodsFlag === 2 && (
                        <FormItem required requiredMessage={'请选择排除商品'} style={{ marginTop: '15px' }}>
                          <ChooseGoods
                            disabled={activityEditDisabled()}
                            value={items.mainSkuList}
                            onChange={(data) => handleOrderSkuChange(data, index1)}
                            sRef={null}
                          />
                          <div className={styles.container}>
                            <SkuList
                              skuList={items.mainSkuList}
                              handlePreview={(data) => {
                                handlePreview(data, index1, items.prizeType);
                              }}
                            />
                          </div>
                          <Input className="validateInput" name={`mainSkuList${index1}`} value={items.mainSkuList} />
                        </FormItem>
                      )}
                    </Grid.Row>
                    <div className="next-form-item-help">
                      <div>
                        此部分商品填写的是申请令牌时关联的商品，不在C端展示，是为用户加入令牌和移除令牌（使用户只能参加一次活
                      </div>
                      <div>
                        动）提供依据，请仔核对令牌促销活动中的商品信息，
                        <span style={{ color: 'red' }}>此处所填写参与活动的正装商品必须和令牌关联的商品保持</span>
                      </div>
                      <div style={{ color: 'red' }}>一致，填错将会有客诉风险</div>
                    </div>
                  </FormItem>
                )}
                {items.prizeType === 3 && (
                  <FormItem disabled={activityEditDisabled()} label="添加实物" required>
                    <Table dataSource={items.prizeList} style={{ marginTop: '15px' }}>
                      <Table.Column title="奖品名称" dataIndex="prizeName" />
                      <Table.Column
                        title="奖品类型"
                        cell={(_, index, row) => <div>{PRIZE_TYPE[row.prizeType]}</div>}
                        dataIndex="prizeType"
                      />
                      <Table.Column
                        title="单位数量"
                        cell={(_, index, row) => <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>}
                      />
                      <Table.Column
                        title="发放份数"
                        cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>}
                      />
                      <Table.Column
                        title="单份价值(元)"
                        cell={(_, index, row) => <div>{row.unitPrice ? Number(row.unitPrice).toFixed(2) : ''}</div>}
                      />
                      <Table.Column
                        title="奖品图"
                        cell={(_, index, row) => <img style={{ width: '30px' }} src={row.prizeImg} alt="" />}
                      />
                        <Table.Column
                          title="操作"
                          width={80}
                          cell={(val, index, _) => (
                            <FormItem disabled={isDisableSetPrize(items.prizeList, index)}>
                              <Button
                                text
                                type="primary"
                                onClick={() => editPrizeList(index1)}
                              >
                                <i className={`iconfont icon-chaojiyingxiaoicon-25`} />
                              </Button>
                              {!activityEditDisabled() && (
                              <Button
                                text
                                type="primary"
                                onClick={() => {
                                  if (_.prizeType) {
                                    Dialog.confirm({
                                      v2: true,
                                      title: '提示',
                                      centered: true,
                                      content: '确认清空该奖品？',
                                      onOk: () => {
                                        items.prizeList.splice(index, 1, PRIZE_INFO);
                                        setData(formData);
                                      },
                                      onCancel: () => console.log('cancel'),
                                    } as any);
                                  }
                                }}
                              >
                                <i className={`iconfont icon-shanchu`} />
                              </Button>
                              )}
                            </FormItem>
                          )}
                        />
                    </Table>
                  </FormItem>
                )}
              </Form>
            </div>
          );
        })}
      </LzPanel>
      <LzDialog
        title={false}
        visible={visible}
        footer={false}
        onClose={() => setVisible(false)}
        style={{ width: '670px' }}
      >
        <ChoosePrize
          hasProbability={false}
          hasLimit={false}
          editValue={editValue}
          onChange={onPrizeChangeOther}
          onCancel={onCancel}
          typeList={[3]}
          initTarget={3}
        />
      </LzDialog>
    </div>
  );
};
