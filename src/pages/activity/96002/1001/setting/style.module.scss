.setting {
  margin-top: 15px;
}

.panel {
  box-sizing: border-box;
  background: #f5f7f9;
  padding: 15px;
  border: 1px solid #d7dde4;
  min-width: 350px;
}

.number {
  margin: 0 10px;
}

.tip {
  font-size: 12px;
  color: gray;
  margin-top: 15px;
}

.tips {
  font-size: 12px;
}

.topTip {
  font-size: 12px;
  color: gray;
  line-height: 28px;
}

.addButton {
  margin-top: 15px;
}

.formNumberPicker {
  width: 200px !important;
  margin-right: 10px;
}

.balloon {
  max-width: fit-content;
}
.container {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 10px;
  margin-top: 15px;

  .skuContainer {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 10px;
    border: 1px solid lightgray;
    position: relative;

    &:hover {
      .del {
        display: block;
      }
    }

    .skuImg {
      width: 60px;
      height: 60px;
    }

    .skuName {
      max-width: 120px;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .skuId {
      color: lightgray;
      font-size: 12px;
    }

    .price {
      color: red;
      font-weight: bold;
      margin-bottom: 5px;
    }
  }
}

.del {
  position: absolute;
  right: -5px;
  top: -5px;
  line-height: 16px;
  color: #9CA7B6;
  display: none;
  cursor: pointer;
}

.awardTitle{
  font-size: 14px!important;
  color: #333333!important;
  padding-left: 8px!important;
}
.prizesBox{
  margin-left: 60px;
}
.inline{
  display: inline-flex;
}
