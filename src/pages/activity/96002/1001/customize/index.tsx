import React, { useEffect, useState } from 'react';
import { CustomValue } from '../util';
// 基础信息
import Base from './components/Base';
import Prizes from './components/Prizes';
import { Tab } from '@alifd/next';

interface Props {
  target: number;
  defaultValue: Required<CustomValue>;
  value: CustomValue | undefined;
  handleChange: (formData: Required<CustomValue>) => void;
  handleChangeActiveKey: (activeKey: string) => void;
  isScreen: boolean;
}

interface EventProps extends Pick<Props, 'defaultValue' | 'value'> {
  onChange: (formData: Required<CustomValue>) => void;
}

export default (props: Props) => {
  const { defaultValue, value, handleChange } = props;
  const [activeKey, setActiveKey] = useState('1');
  const handleTabChange = (data) => {
    setActiveKey(data);
    props.handleChangeActiveKey(data);
  };
  // 判断是否正在截图
  useEffect(() => {
    if (props.isScreen) {
      handleTabChange('1');
    }
  }, [props.isScreen]);
  // 各装修自组件抛出的数据
  // 只负责转发，不作处理
  const onChange = (formData): void => {
    handleChange(formData);
  };
  const eventProps: EventProps = {
    defaultValue,
    value,
    onChange,
  };
  return (
    <div>
      <Tab activeKey={activeKey} defaultActiveKey="1" onChange={handleTabChange}>
        <Tab.Item title="活动主页" key="1" />
        <Tab.Item title="领奖页" key="2" />
      </Tab>
      {activeKey === '1' && <Base {...eventProps} />}
      {activeKey === '2' && <Prizes {...eventProps} />}
    </div>
  );
};
