import React from 'react';
import dayjs, { Dayjs } from 'dayjs';
import { Message } from '@alifd/next';
import { getParams } from '@/utils';
import Preview from '@/components/LzCrowdBag/preview';

export interface PrizeInfo {
  prizeKey: string;
  prizeName: string;
  prizeImg: string;
  prizeType: number;
  probability: number | string;
  unitCount: number;
  unitPrice: number;
  sendTotalCount: number;
  dayLimit: number;
  dayLimitType: number;
  ifPlan: number;
  startTime?: string;
  endTime?: string;
  startDate?: string;
  endDate?: string;
  beginTime?: string;
}

export const PRIZE_INFO: PrizeInfo = {
  // 奖品名称
  prizeName: '',
  // 奖品图
  prizeImg: '',
  // 奖品类型
  prizeType: 0,
  // 中奖概率
  probability: 0,
  // 单位数量
  unitCount: 0,
  // 单位价值
  unitPrice: 0,
  // 发放份数
  sendTotalCount: 0,
  // 每日发放限额
  dayLimit: 0,
  // 每日发放限额类型 1 不限制 2限制
  dayLimitType: 1,
  ifPlan: 0,
  prizeKey: '',
};

export interface FormLayout {
  labelCol: {
    span?: number;
    fixedSpan?: number;
  };
  wrapperCol: {
    span: number;
  };
  labelAlign: 'left' | 'top';
  colon: boolean;
}
class Sku {
  jdPrice: string;
  seq: number;
  skuId: number;
  skuMainPicture: string;
  skuName: string;
}

export const GRADE_LABEL: any = [
  {
    gradeName: '银卡',
    gradeLevel: '2',
    prizeTypeName: '单品促销令牌',
  },
  {
    gradeName: '金卡',
    gradeLevel: '3',
    prizeTypeName: '单品促销令牌',
  },
  {
    gradeName: '铂金卡',
    gradeLevel: '4',
    prizeTypeName: '单品促销令牌',
  },
  {
    gradeName: '白钻卡',
    gradeLevel: '5',
    prizeTypeName: '单品促销令牌',
  },
];

export interface CustomValue {
  // 活动主图
  actBg: string;
  // 页面背景图
  pageBg: string;
  getBtnBg: string;
  // 页面背景色
  actBgColor: string;
  getPrizePageBg: string;
  // 银卡奖品图
  silverCardPrizesImg: string;
  // 金卡奖品图
  goldCardPrizesImg: string;
  // 铂金卡奖品图
  platinumCardPrizesImg: string;
  // 白钻卡奖品图
  diamondCardPrizesImg: string;
  // 完善信息链接
  finishInfoLink: string;
  cmdImg: string;
  h5Img: string;
  mpImg: string;
}
export interface TaskRequestInfo {
  level: string;
  gradeValueName: string;
  position: number;
  sendTotalCount: Number; // 令牌发放份数
  prizeType: number; // 11 单品促销令牌 3 实物
  mainSkuList: Sku[]; // 令牌（指定商品）
  skuList: Sku[]; // 实物（指定商品）
  prizeList: PrizeInfo[]; // 根据实际情况，可能需要定义奖品的类型
  threshold: number; // 满赠门槛
}
export const GIVEAWAY_INFO: TaskRequestInfo = {
  level: '',
  gradeValueName: '',
  position: 1,
  sendTotalCount: 1,
  mainSkuList: [], // 令牌指定商品
  skuList: [], // 实物指定商品
  prizeType: 11,
  prizeList: [], // 根据实际情况，可能需要定义奖品的类型
  threshold: 1,
};

export interface PageData {
  taskRequestList: TaskRequestInfo[];
  activityName: string;
  rangeDate: [Dayjs, Dayjs] | [string, string];
  startTime: string;
  endTime: string;
  threshold: number;
  supportLevels: string;
  limitJoinTimeType: number;
  joinTimeRange: [string, string] | [Dayjs, Dayjs];
  joinStartTime: string;
  joinEndTime: string;
  crowdBag: any;
  shareStatus: number;
  shareTitle: string;
  templateCode: string;
  cmdImg: string;
  h5Img: string;
  mpImg: string;
  gradeLabel: string[];
  rules: string;
}

interface PrizeType {
  [key: number]: string;
}

export const PRIZE_TYPE: PrizeType = {
  12: '京元宝权益',
  2: '京豆',
  1: '优惠券',
  3: '实物',
  4: '积分',
  6: '红包',
  7: '礼品卡',
  8: '京东E卡',
  9: 'PLUS会员卡',
  10: '爱奇艺会员卡',
  11: '总价促销令牌',
};

// form格式
export const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};
// 步骤文案
export const STEPS = ['① 设置模板', '② 活动设置', '③ 活动预览', '④ 活动完成'];
// 装修默认数据
export const DEFAULT_CUSTOM_VALUE: CustomValue = {
  // 活动主页图
  actBg: '',
  // 页面背景图
  pageBg: '',
  getBtnBg: '',
  // 页面背景色
  actBgColor: '',
  // 领奖页面背景图
  getPrizePageBg: '',
  // 银卡奖品图
  silverCardPrizesImg: '',
  // 金卡奖品图
  goldCardPrizesImg: '',
  // 铂金卡奖品图
  platinumCardPrizesImg: '',
  // 白钻卡奖品图
  diamondCardPrizesImg: '',
  // 完善信息链接
  finishInfoLink: '',
  cmdImg: '',
  h5Img: '',
  mpImg: '',
};
// 活动设置默认数据

export const INIT_PAGE_DATA = (): PageData => {
  return {
    taskRequestList: [],
    // 活动名称
    activityName: `馥蕾诗生日礼-${dayjs().format('YYYY-MM-DD')}`,
    // 日期区间（不提交）
    rangeDate: [
      (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
      (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    ],
    // 活动开始时间
    startTime: (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
    // 活动结束时间
    endTime: (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    // 活动门槛（不提交）
    threshold: 1,
    // 支持的会员等级(-1:无门槛；1,2,3,4,5,-9以逗号做分割(-9为关注店铺用户))
    supportLevels: '1,2,3,4,5',
    // 限制入会时间 0：不限制；1：入会时间
    limitJoinTimeType: 0,
    // 入会时间
    joinTimeRange: [] as any,
    joinStartTime: '',
    joinEndTime: '',
    // 是否开启分享
    shareStatus: 1,
    // 分享标题
    shareTitle: '会员生日礼',
    // 分享图片
    cmdImg: '',
    h5Img: '',
    mpImg: '',
    templateCode: '',
    gradeLabel: [],
    crowdBag: null,
    rules: '',
  };
};
// 预览二维码大小
export const QRCODE_SIZE = 74;
// 预览二维码配置项
export const QRCODE_SETTING = {
  src: require('@/assets/images/jd-logo.webp'),
  height: QRCODE_SIZE / 7.4,
  width: QRCODE_SIZE / 7.4,
  excavate: false,
};

// 生成会员等级字符串
export const generateMembershipString = (formData: PageData, type?: string) => {
  if (formData.threshold === 1) {
    const levels = formData.supportLevels.split(',');
    const LEVEL_MAP = formData.gradeLabel;
    const memberLevelsList = levels.filter((e) => Number(e) > 0);
    const membershipLevels = memberLevelsList.map((l, index) => LEVEL_MAP[index]).join('，');
    const membershipString = memberLevelsList.length ? `店铺会员（${membershipLevels}）` : '';
    const extraLevelsString = levels
      .filter((e) => Number(e) < 0)
      .map((l, index) => LEVEL_MAP[index + memberLevelsList.length])
      .join('、');

    const joinTimeLimitString =
      formData.limitJoinTimeType === 1 ? `。限制入会时间：${formData.joinStartTime}至${formData.joinEndTime}` : '';

    return (
      membershipString +
      (extraLevelsString && `${memberLevelsList.length > 0 ? '、' : ''}${extraLevelsString}`) +
      joinTimeLimitString
    );
  }
  if (formData.threshold === 2) {
    return !type ? <Preview value={formData.crowdBag} /> : `人群包<${formData.crowdBag.packName}>`;
  }
  return '不限制';
};
// 是否未编辑
const isProcessingEditType = (): boolean => {
  return getParams('type') === 'edit';
};
// 校验开始时间是否符合规则
const isStartTimeValid = (startTime: string): boolean => {
  const isLessThanTenMinutes: boolean = dayjs(startTime).isBefore(dayjs().startOf('day'));
  if (isLessThanTenMinutes) {
    Message.error('活动开始时间应大于当天时间');
    return false;
  }
  return true;
};
// 校验结束时间是否符合规则
const isEndTimeValid = (startTime: string, endTime: string): boolean => {
  const isGreaterThanNow: boolean = dayjs(endTime).isAfter(dayjs());
  const isGreaterThanStart: boolean = dayjs(endTime).isAfter(dayjs(startTime));

  if (!isGreaterThanStart) {
    Message.error('活动结束时间应大于活动开始时间');
    return false;
  }
  if (!isGreaterThanNow) {
    Message.error('活动结束时间应大于当前时间');
    return false;
  }
  return true;
};

// 校验奖品时间是否符合规则
// eslint-disable-next-line complexity
const isPrizeValid = (prize: PrizeInfo, formData: PageData, level): boolean => {
  const type = prize.prizeType;
  // 0-谢谢参与 3-实物 4-积分 没有时间直接通过
  if (type === 0 || type === 3 || type === 4) {
    return true;
  }
  // 开始时间
  const start = prize.startTime || prize.startDate || prize.beginTime;
  // 结束时间
  const end = prize.endTime || prize.endDate;

  if (start && end) {
    let gradeValueName = '';
    GRADE_LABEL.forEach((item) => {
      if (item.gradeLevel === level) {
        gradeValueName = item.gradeName;
      }
    });
    // 奖品的开始时间间是否小于活动的开始时间
    const isStart: boolean = dayjs(formData.startTime).isAfter(dayjs(start));
    if (!isStart) {
      Message.error(`${gradeValueName}会员${PRIZE_TYPE[prize.prizeType]}开始时间应小于活动开始时间`);
      return false;
    }
    // 奖品的结束时间间是否大于活动的结束时间
    const isEnd: boolean = dayjs(formData.endTime).isAfter(dayjs(end));
    if (isEnd) {
      console.log(prize.prizeType);
      Message.error(`${gradeValueName}会员${PRIZE_TYPE[prize.prizeType]}结束时间需要晚于或等于活动结束时间`);
      return false;
    }
  }
  return true;
};

// 校验奖品时间是否符合规则
const arePrizesValid = (level: string, prizeList: PrizeInfo[], formData: PageData): boolean => {
  for (let i = 0; i < prizeList.length; i++) {
    if (!isPrizeValid(prizeList[i], formData, level)) {
      return false;
    }
  }
  return true;
};

// 检查奖品是否选择
const checkPrizes = (list: any, formData: PageData): boolean => {
  console.log(list, 'list==========');
  GRADE_LABEL.forEach((item, index) => {
    if (item.gradeLevel === list[index].level) {
      list[index].gradeValueName = item.gradeName;
    }
  });
  for (let i = 0; i < list.length; i++) {
    if (list[i].prizeList.length <= 0) {
      Message.error(`请选择${list[i].gradeValueName}会员的奖励`);
      return false;
    }
    for (let j = 0; j < list[i].prizeList.length; j++) {
      if (!list[i].prizeList[j].prizeType) {
        Message.error(`请选择${list[i].gradeValueName}会员的${PRIZE_TYPE[list[i].prizeType]}`);
        return false;
      }
      if (!list[i].prizeList[j].sendTotalCount) {
        Message.error(`请选择${list[i].gradeValueName}会员的${PRIZE_TYPE[list[i].prizeType]}发放份数`);
        return false;
      }

      // 奖品时间与活动时间冲突
      if (!arePrizesValid(list[i].level, list[i].prizeList, formData)) {
        return false;
      }
    }
  }
  return true;
};

export const checkActivityData = (formData: PageData): boolean => {
  // 编辑直接通过，不走校验
  if (isProcessingEditType()) {
    return true;
  }
  // 没有选择奖品
  if (!checkPrizes(formData.taskRequestList, formData)) {
    return false;
  }
  // 开始时间异常
  if (!isStartTimeValid(formData.startTime)) {
    return false;
  }
  // 结束时间异常
  if (!isEndTimeValid(formData.startTime, formData.endTime)) {
    return false;
  }
  return true;
};
