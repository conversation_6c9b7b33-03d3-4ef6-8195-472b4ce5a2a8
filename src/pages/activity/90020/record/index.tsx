import React, { useState } from 'react';
import { Tab, But<PERSON> } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import WinRecord from './components/WinRecord';
import BornCertificateRecord from './components/BornCertificateRecord';
import LzDocGuide from '@/components/LzDocGuide';

export default () => {
  const [activeKey, setActiveKey] = useState('1');

  return (
    <div className="crm-container">
      <LzPanel title="全渠道新客礼活动记录" actions={<LzDocGuide />}>
        <Tab defaultActiveKey="1" activeKey={activeKey} onChange={(key) => setActiveKey(key)} unmountInactiveTabs>
          <Tab.Item title="领取记录" key="1">
            <WinRecord />
          </Tab.Item>
          {/* <Tab.Item title="出生证明绑定记录" key="2">
            <BornCertificateRecord />
          </Tab.Item> */}
        </Tab>
      </LzPanel>
    </div>
  );
};
