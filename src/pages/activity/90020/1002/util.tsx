import dayjs, { Dayjs } from 'dayjs';
import { getParams } from '@/utils';
import { Message } from '@alifd/next';
import CONST from '@/utils/constant';

const { shopName } = JSON.parse(localStorage.getItem(CONST.LZ_CURRENT_SHOP) as string);

export interface FormLayout {
  labelCol: {
    span?: number;
    fixedSpan?: number;
  };
  wrapperCol: {
    span: number;
  };
  labelAlign: 'left' | 'top';
  colon: boolean;
}

export interface CustomValue {
  actBg: string;
  pageBg: string;
  actBgColor: string;
  stepImg: string;
  canNotJoinKv: string;
  jumpLink: string;
  cmdImg: string;
  h5Img: string;
  mpImg: string;
  disableShopName: number;
}

export interface PrizeInfo {
  prizeName: string;
  prizeImg: string;
  prizeType: number;
  probability: number | string;
  unitCount: number;
  unitPrice: number;
  sendTotalCount: number;
  dayLimit: number;
  dayLimitType: number;
  ifPlan: number;
  startTime?: string;
  endTime?: string;
  startDate?: string;
  endDate?: string;
  stepAmount: number;
  key?: string;
  prizeTypeName: string;
  type: 1 | 2;
  skuId: string;
  skuInfo: any;
  sortId: number;
}

export interface PageData {
  activityName: string;
  id?: string;
  rangeDate: [Dayjs, Dayjs] | string[] | Date[] | null;
  startTime: string;
  endTime: string;
  threshold: number;
  supportLevels: string;
  limitJoinTimeType: number;
  shareStatus: number;
  shareTitle: string;
  rules: string;
  templateCode: string;
  cmdImg: string;
  h5Img: string;
  mpImg: string;
  crowdBag: any;
  gradeLabel: any;
  shopName: string;
  newCustomerOneRightsType: number;
  oldCustomerOneRightsType: number;
  orderSkuLimit: number;
  orderTime: number;
  prizeList: PrizeInfo[];
  barcodeString: string;
  barcodeList: string[];
  endActivity: number;
}

interface PrizeType {
  [key: number]: string;
}

export const PRIZE_TYPE: PrizeType = {
  12: '京元宝权益',
  2: '京豆',
  1: '优惠券',
  3: '实物',
  4: '积分',
  6: '红包',
  7: '礼品卡',
  8: '京东E卡',
  9: 'PLUS会员卡',
  10: '爱奇艺会员卡',
};
// form格式
export const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};
// 步骤文案
export const STEPS = ['① 设置模板', '② 活动设置', '③ 活动预览', '④ 活动完成'];
// 装修默认数据
export const DEFAULT_CUSTOM_VALUE: CustomValue = {
  actBg: '',
  pageBg: '',
  actBgColor: '',
  stepImg: '',
  canNotJoinKv: '',
  jumpLink: '',
  cmdImg: '',
  h5Img: '',
  mpImg: '',
  disableShopName: 0,
};

export const PRIZE_INFO: PrizeInfo = {
  // 奖品名称
  prizeName: '',
  // 奖品图
  prizeImg: '',
  // 奖品类型
  prizeType: 0,
  // 中奖概率
  probability: 0,
  // 单位数量
  unitCount: 0,
  // 单位价值
  unitPrice: 0,
  // 发放份数
  sendTotalCount: 0,
  // 每日发放限额
  dayLimit: 0,
  // 每日发放限额类型 1 不限制 2限制
  dayLimitType: 1,
  ifPlan: 0,
  // 金额
  stepAmount: 0,
  prizeTypeName: '', // 优惠券类型名称(新客礼1.1；老客礼1.1)
  type: 1, // 活动奖品类型 1-新客礼； 2-老客礼
  skuId: '', // 商品ID
  skuInfo: null, // 商品信息
  sortId: 1,
};
// 活动设置默认数据
export const INIT_PAGE_DATA = (): PageData => {
  return {
    // 基本信息
    // 活动名称
    activityName: `全渠道新客礼-${dayjs().format('YYYY-MM-DD')}`,
    // 日期区间（不提交）
    rangeDate: [
      (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
      (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    ],
    // 活动开始时间
    startTime: (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
    // 活动结束时间
    endTime: (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    // 活动门槛（不提交）
    threshold: 1,
    // 支持的会员等级(-1:无门槛；1,2,3,4,5,-9以逗号做分割(-9为关注店铺用户))
    supportLevels: '1,2,3,4,5',
    // 限制入会时间 0：不限制；1：入会时间
    limitJoinTimeType: 0,
    // 分享设置
    // 是否开启分享
    shareStatus: 0,
    // 分享标题
    shareTitle: '下单即有机会赢取好礼，快来看看吧！',
    // 分享图片
    cmdImg: '',
    h5Img: '',
    mpImg: '',
    // 活动规则
    rules: '',
    templateCode: '',
    crowdBag: null,
    gradeLabel: [],
    shopName,
    newCustomerOneRightsType: 1, // 新客礼1.0权益类型：1-新客礼1.1(全店商品)；2-新客礼1.2(指定商品)
    oldCustomerOneRightsType: 1, // 老客礼1.0权益类型：1-老客礼1.1(全店商品)；2-老客礼1.2(指定商品)
    orderSkuLimit: 1, // 订单包含商品：1-任意Barcode；2-指定Barcode
    orderTime: 365, // 订单时间：365-近365天；-1：历史以来
    prizeList: [],
    barcodeString: '',
    barcodeList: [],
    endActivity: 0,
  };
};
// 预览二维码大小
export const QRCODE_SIZE = 74;
// 预览二维码配置项
export const QRCODE_SETTING = {
  src: require('@/assets/images/jd-logo.webp'),
  height: QRCODE_SIZE / 7.4,
  width: QRCODE_SIZE / 7.4,
  excavate: false,
};

// 是否未编辑
// const isProcessingEditType = (): boolean => {
//   return getParams('type') === 'edit';
// };
// 校验奖品时间是否符合规则
const isPrizeValid = (prize: PrizeInfo, formData: PageData): boolean => {
  if (!prize) return true; // 如果奖品不存在，直接返回true

  const type = prize.prizeType;
  // 0-谢谢参与 3-实物 4-积分 没有时间直接通过
  if (type === 0 || type === 3 || type === 4) {
    return true;
  }
  // 开始时间
  const start = prize.startTime || prize.startDate;
  // 结束时间
  const end = prize.endTime || prize.endDate;

  if (start && end) {
    // 奖品的开始时间间是否小于活动的开始时间
    const isStart: boolean = dayjs(formData.startTime).isBefore(dayjs(start));
    if (isStart) {
      Message.error(`奖品${prize.prizeName}起始时间需要早于或等于活动开始时间`);

      return false;
    }
    // 奖品的结束时间间是否大于活动的结束时间
    const isEnd: boolean = dayjs(formData.endTime).isAfter(dayjs(end));
    if (isEnd) {
      Message.error(`奖品${prize.prizeName}结束时间需要晚于或等于活动结束时间`);
      return false;
    }
  }
  return true;
};
// 校验奖品时间是否符合规则
const arePrizesValid = (prizeList: PrizeInfo[], formData: PageData): boolean => {
  for (let i = 0; i < prizeList.length; i++) {
    if (!isPrizeValid(prizeList[i], formData)) {
      return false;
    }
  }
  return true;
};

// 校验所有奖品列表
const checkAllPrizes = (formData: PageData): boolean => {
  // @ts-ignore
  if (!formData.prizeList.find((item) => item.type === 1).prizeName) {
    Message.error('请选择新客礼1.0奖品');
    return false;
  }
  // @ts-ignore
  if (!formData.prizeList.find((item) => item.type === 2).prizeName) {
    Message.error('请选择老客礼1.0奖品');
    return false;
  }
  if (formData.prizeList && !arePrizesValid(formData.prizeList, formData)) {
    return false;
  }
  return true;
};

// 校验开始时间是否符合规则
// const isStartTimeValid = (startTime: Dayjs, name: string): boolean => {
//   const isLessThanTenMinutes: boolean = dayjs(startTime).isBefore(dayjs().startOf('day'));
//   if (isLessThanTenMinutes) {
//     Message.error(`${name}开始时间应大于当天时间`);
//     return false;
//   }
//   return true;
// };

// 校验结束时间是否符合规则
const isEndTimeValid = (startTime: any, endTime: any, name: string): boolean => {
  const isGreaterThanNow: boolean = dayjs(endTime).isAfter(dayjs());
  const isGreaterThanStart: boolean = dayjs(endTime).isAfter(dayjs(startTime));

  if (!isGreaterThanStart) {
    Message.error(`${name}结束时间应大于${name}开始时间`);
    return false;
  }
  if (!isGreaterThanNow) {
    Message.error(`${name}结束时间应大于当前时间`);
    return false;
  }
  return true;
};

const checkEditTime = (endTime) => {
  console.log(getParams('type'), endTime, dayjs(endTime).isAfter(dayjs()), '545454');
  if (getParams('type') !== 'edit') {
    return true;
  }
  const isGreaterThanNow: boolean = dayjs(endTime).isAfter(dayjs());
  if (!isGreaterThanNow) {
    Message.error('结束时间应大于当前时间');
    return false;
  }
  return true;
};

export const checkActivityData = (formData: PageData): boolean => {
  // if (isProcessingEditType()) {
  //   return true;
  // }
  const validations = [
    () => isEndTimeValid(formData.startTime, formData.endTime, '活动'),
    () => checkEditTime(formData.endTime),
    () => checkAllPrizes(formData),
  ];
  for (const isValid of validations) {
    if (!isValid()) {
      return false;
    }
  }

  return true;
};
