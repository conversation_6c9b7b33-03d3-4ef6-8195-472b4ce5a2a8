import React, { useEffect, useState } from 'react';
import { Form, DatePicker2, Field, Table, Button } from '@alifd/next';
import constant from '@/utils/constant';
import LzPagination from '@/components/LzPagination';
import { dataBaseData, dataBaseDataExport, dataByDayData, dataByDayDataExport } from '@/api/v90020';
import { deepCopy, downloadExcel, getParams } from '@/utils';
import dayJs from 'dayjs';
import LzPanel from '@/components/LzPanel';
import styles from './style.module.scss';

const FormItem = Form.Item;
const { RangePicker } = DatePicker2;
const defaultPage = {
  pageNum: 1,
  pageSize: 10,
  total: 0,
};

export default (props) => {
  const field = Field.useField();
  const bottomField = Field.useField();
  const [pageInfo, setPage] = useState(deepCopy(defaultPage));
  const [tableData, setTableData] = useState<any[]>([]);
  const [bottomTableData, setBottomTableData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [bottomLoading, setBottomLoading] = useState(false);

  const defaultRangeVal = [
    dayJs(new Date(getParams('startTime'))).format('YYYY-MM-DD 00:00:00'),
    dayJs(new Date(getParams('endTime'))).format('YYYY-MM-DD 23:59:59'),
  ];

  const handleSubmit = (e) => {
    e.preventDefault();
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  };

  const handleSubmitBottom = (e) => {
    e.preventDefault();
    const formValue: any = bottomField.getValues();
    loadBottomData({ ...formValue, ...defaultPage });
  };

  const loadData = (query: any): void => {
    setLoading(true);
    query.activityId = getParams('id');
    dataBaseData(query)
      .then((res: any): void => {
        setTableData(res as any[]);
        setLoading(false);
      })
      .catch((e) => {
        setLoading(false);
      });
  };
  const loadBottomData = (query: any): void => {
    setBottomLoading(true);
    query.activityId = getParams('id');
    dataByDayData(query)
      .then((res: any): void => {
        for (let i = 0; i < res.records.length; i++) {
          res.records[i].num = i + 1;
        }
        setBottomTableData(res.records as any[]);
        pageInfo.total = +res.total!;
        pageInfo.pageSize = +res.size!;
        pageInfo.pageNum = +res.current!;
        setPage(pageInfo);
        setBottomLoading(false);
      })
      .catch((e) => {
        setBottomLoading(false);
      });
  };
  const handlePage = ({ pageSize, pageNum }) => {
    setPage({
      ...pageInfo,
      pageSize,
      pageNum,
    });
    const formValue: any = bottomField.getValues();
    loadBottomData({ ...formValue, pageSize, pageNum });
  };
  const exportData = () => {
    const formValue: any = field.getValues();
    formValue.activityId = getParams('id');
    dataBaseDataExport(formValue).then((data: any) => downloadExcel(data, `${getParams('name')}活动数据`));
  };
  const exportBottomData = () => {
    const formValue: any = bottomField.getValues();
    formValue.activityId = getParams('id');
    dataByDayDataExport(formValue).then((data: any) => downloadExcel(data, `${getParams('name')}活动数据`));
  };

  useEffect(() => {
    const formValue: any = field.getValues();
    const bottomFormValue: any = bottomField.getValues();
    loadData({ ...formValue });
    loadBottomData({ ...bottomFormValue, ...defaultPage });
  }, []);

  return (
    <div>
      <LzPanel>
        <div className={styles.titleReport}>基本数据</div>
        <Form className="lz-query-criteria" field={field} colon labelAlign={'top'} onSubmit={handleSubmit}>
          <FormItem name="dateRange" label="日期">
            <RangePicker hasClear={false} defaultValue={defaultRangeVal} format={'YYYY-MM-DD'} />
          </FormItem>
          <FormItem colon={false}>
            <Form.Submit type="primary" htmlType="submit">
              查询
            </Form.Submit>
            <Form.Reset
              toDefault
              onClick={() => {
                const formValue: any = field.getValues();
                loadData({ ...formValue });
              }}
            >
              重置
            </Form.Reset>
            <Button onClick={exportData}>导出</Button>
          </FormItem>
        </Form>
        <Table dataSource={tableData} loading={loading}>
          <Table.Column width={200} align={'center'} title="累积活动PV" dataIndex="pv" />
          <Table.Column width={200} align={'center'} title="累计活动UV" dataIndex="uv" />
          <Table.Column width={200} align={'center'} title="入会人数" dataIndex="openCardMember" />
          <Table.Column width={200} align={'center'} title="新客礼1.0领取人数" dataIndex="newCustomerOneNum" />
          <Table.Column width={200} align={'center'} title="老客礼1.0领取人数" dataIndex="oldCustomerOneNum" />
        </Table>
      </LzPanel>
      <LzPanel>
        <div className={styles.titleReport}>详细数据</div>
        <Form className="lz-query-criteria" field={bottomField} colon labelAlign={'top'} onSubmit={handleSubmitBottom}>
          <FormItem name="dateRange" label="日期">
            <RangePicker hasClear={false} defaultValue={defaultRangeVal} format={'YYYY-MM-DD'} />
          </FormItem>
          <FormItem colon={false}>
            <Form.Submit type="primary" htmlType="submit">
              查询
            </Form.Submit>
            <Form.Reset
              toDefault
              onClick={() => {
                const formValue: any = bottomField.getValues();
                loadBottomData({ ...formValue, ...defaultPage });
              }}
            >
              重置
            </Form.Reset>
            <Button onClick={exportBottomData}>导出</Button>
          </FormItem>
        </Form>
        <Table dataSource={bottomTableData} loading={bottomLoading}>
          <Table.Column width={200} align={'center'} lock={'left'} title="日期" dataIndex="dt" />
          <Table.Column width={200} align={'center'} title="PV" dataIndex="pv" />
          <Table.Column width={200} align={'center'} title="UV" dataIndex="uv" />
          <Table.Column width={200} align={'center'} title="入会人数" dataIndex="openCardMember" />
          <Table.Column width={200} align={'center'} title="新客礼1.0领取人数" dataIndex="newCustomerOneNum" />
          <Table.Column
            width={200}
            align={'center'}
            title="新客礼1.0商品购买人数"
            dataIndex="newCustomerOneOrderUserNum"
          />
          <Table.Column width={200} align={'center'} title="老客礼1.0领取人数" dataIndex="oldCustomerOneNum" />
          <Table.Column
            width={200}
            align={'center'}
            title="老客礼1.0商品购买人数"
            dataIndex="oldCustomerOneOrderUserNum"
          />
          {/* <Table.Column
            width={200}
            align={'center'}
            title="新客礼2优惠券1领取人数"
            dataIndex="newCustomerTwoCouponOneNum"
          />
          <Table.Column
            width={200}
            align={'center'}
            title="新客礼2优惠券1购买人数"
            dataIndex="newCustomerTwoCouponOneOrderUserNum"
          />
          <Table.Column
            width={200}
            align={'center'}
            title="新客礼2优惠券2领取人数"
            dataIndex="newCustomerTwoCouponTwoNum"
          />
          <Table.Column
            width={200}
            align={'center'}
            title="新客礼2优惠券2购买人数"
            dataIndex="newCustomerTwoCouponTwoOrderUserNum"
          />
          <Table.Column
            width={200}
            align={'center'}
            title="新客礼2优惠券3领取人数"
            dataIndex="newCustomerTwoCouponThreeNum"
          />
          <Table.Column
            width={200}
            align={'center'}
            title="新客礼2优惠券3购买人数"
            dataIndex="newCustomerTwoCouponThreeOrderUserNum"
          />
          <Table.Column
            width={200}
            align={'center'}
            title="新客礼2优惠券4领取人数"
            dataIndex="newCustomerTwoCouponFourNum"
          />
          <Table.Column
            width={200}
            align={'center'}
            title="新客礼2优惠券4购买人数"
            dataIndex="newCustomerTwoCouponFourOrderUserNum"
          /> */}
        </Table>
        <LzPagination
          pageNum={pageInfo.pageNum}
          pageSize={pageInfo.pageSize}
          total={pageInfo.total}
          onChange={handlePage}
        />
      </LzPanel>
    </div>
  );
};
