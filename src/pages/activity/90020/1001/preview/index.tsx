import React, { useEffect, useReducer, useState } from 'react';
import { Button, Form, Input, Table } from '@alifd/next';
import { formItemLayout, PageData, PRIZE_TYPE } from '../util';
import styles from './style.module.scss';
import dayjs from 'dayjs';
import SkuList from '@/components/SkuList';
import LzDialog from '@/components/LzDialog';

const FormItem = Form.Item;

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  labelAlign?: 'left' | 'top';
}
export default ({ defaultValue, value, labelAlign = 'left' }: Props) => {
  const [formData, setFormData] = useReducer<React.Reducer<PageData, PageData>>((prevState, action) => {
    return { ...prevState, ...action };
  }, value || defaultValue);
  const [visible, setVisible] = useState(false);

  // 当value或defaultValue更新时，更新formData
  useEffect(() => {
    if (value || defaultValue) {
      setFormData(value || defaultValue);
    }
  }, [value, defaultValue]);

  formItemLayout.labelAlign = labelAlign;

  return (
    <div className={styles.preview}>
      <Form {...formItemLayout} isPreview>
        <FormItem label="活动标题">{formData.activityName}</FormItem>
        <FormItem label="活动时间">{`${dayjs(formData.rangeDate[0]).format('YYYY-MM-DD HH:mm:ss')}至${dayjs(
          formData.rangeDate[1],
        ).format('YYYY-MM-DD HH:mm:ss')}`}</FormItem>
        <FormItem label="活动门槛">{formData.threshold === 0 ? '无门槛' : '店铺会员'}</FormItem>

        <FormItem label="新客礼1.0配置">
          <div>{formData.newCustomerOneRightsType === 1 ? '新客礼1.1（全店商品）' : '新客礼1.2（指定商品）'}</div>
        </FormItem>
        <FormItem label="老客礼1.0配置">
          <div>{formData.oldCustomerOneRightsType === 1 ? '老客礼1.1（全店商品）' : '老客礼1.2（指定商品）'}</div>
        </FormItem>

        <FormItem label="订单时间">
          <div>{formData.orderTime === 365 ? '近365天' : '历史以来'}</div>
        </FormItem>
        <FormItem label="订单包含商品">
          <div>
            {formData.orderSkuLimit === 1 ? (
              '任意Barcode'
            ) : (
              <Button
                onClick={() => {
                  setVisible(true);
                }}
              >
                查看Barcode
              </Button>
            )}
          </div>
        </FormItem>

        {/* 新客礼1奖品 */}
        <FormItem label="新客礼1.0奖品">
          <Table dataSource={formData.prizeList.filter((item) => item.type === 1)} style={{ marginTop: '15px' }}>
            <Table.Column title="奖品名称" dataIndex="prizeName" />
            <Table.Column
              title="奖项类型"
              cell={(v, index, row) => <div>{PRIZE_TYPE[row.prizeType]}</div>}
              dataIndex="prizeType"
            />
            <Table.Column
              title="单位数量"
              cell={(v, index, row) => {
                if (row.prizeType === 1) {
                  return <div>{row.numPerSending ? `${row.numPerSending}张` : ''}</div>;
                } else {
                  return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
                }
              }}
            />
            <Table.Column title="价值(元)" dataIndex="unitPrice" />
            <Table.Column title="发放份数" dataIndex="sendTotalCount" />
            <Table.Column
              title="奖品图"
              dataIndex="prizeImg"
              cell={(_, index, row) => <img src={row.prizeImg} style={{ height: 50 }} alt="" />}
            />
          </Table>
          {/* 如果奖品有关联SKU，显示SKU列表 */}
          <div style={{ marginTop: '10px' }}>
            <div style={{ marginBottom: '15px' }}>
              <div style={{ fontWeight: 'bold', marginBottom: '5px' }}>新客礼1.0关联商品：</div>
              <SkuList skuList={[formData.prizeList.find((item) => item.type === 1).skuInfo]} />
            </div>
          </div>
        </FormItem>

        {/* 老客礼1奖品 */}
        <FormItem label="老客礼1.0奖品">
          <Table dataSource={formData.prizeList.filter((item) => item.type === 2)} style={{ marginTop: '15px' }}>
            <Table.Column title="奖品名称" dataIndex="prizeName" />
            <Table.Column
              title="奖项类型"
              cell={(v, index, row) => <div>{PRIZE_TYPE[row.prizeType]}</div>}
              dataIndex="prizeType"
            />
            <Table.Column
              title="单位数量"
              cell={(v, index, row) => {
                if (row.prizeType === 1) {
                  return <div>{row.numPerSending ? `${row.numPerSending}张` : ''}</div>;
                } else {
                  return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
                }
              }}
            />
            <Table.Column title="价值(元)" dataIndex="unitPrice" />
            <Table.Column title="发放份数" dataIndex="sendTotalCount" />
            <Table.Column
              title="奖品图"
              dataIndex="prizeImg"
              cell={(_, index, row) => <img src={row.prizeImg} style={{ height: 50 }} alt="" />}
            />
          </Table>
          {/* 如果奖品有关联SKU，显示SKU列表 */}
          <div style={{ marginTop: '10px' }}>
            <div style={{ marginBottom: '15px' }}>
              <div style={{ fontWeight: 'bold', marginBottom: '5px' }}>老客礼1.0关联商品：</div>
              <SkuList skuList={[formData.prizeList.find((item) => item.type === 2).skuInfo]} />
            </div>
          </div>
        </FormItem>

        {/* <FormItem label="活动分享">{formData.shareStatus === 0 ? '关闭' : '开启'}</FormItem> */}
        {/* {formData.shareStatus !== 0 && ( */}
        {/*  <> */}
        {/*    <FormItem label="分享标题">{formData.shareTitle}</FormItem> */}
        {/*    <FormItem label="图文分享图片"> */}
        {/*      <img src={formData.h5Img} style={{ width: '200px' }} alt="" /> */}
        {/*    </FormItem> */}
        {/*    <FormItem label="京口令分享图片"> */}
        {/*      <img src={formData.cmdImg} style={{ width: '200px' }} alt="" /> */}
        {/*    </FormItem> */}
        {/*    <FormItem label="小程序分享图片"> */}
        {/*      <img src={formData.mpImg} style={{ width: '200px' }} alt="" /> */}
        {/*    </FormItem> */}
        {/*  </> */}
        {/* )} */}
        <FormItem label="规则内容">
          <Input.TextArea className="rule-word-break" value={formData.rules} />
        </FormItem>
      </Form>
      <LzDialog
        title={'指定Barcode'}
        visible={visible}
        footer={false}
        onClose={() => setVisible(false)}
        style={{ width: '500px' }}
      >
        <Table dataSource={formData.barcodeList}>
          <Table.Column
            title="Barcode"
            cell={(val, index, row) => {
              return <div>{row}</div>;
            }}
          />
        </Table>
      </LzDialog>
    </div>
  );
};
