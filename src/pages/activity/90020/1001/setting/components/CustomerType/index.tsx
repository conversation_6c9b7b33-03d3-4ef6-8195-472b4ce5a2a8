import React, { useReducer, useImperativeHandle, useEffect, useState } from 'react';
import LzPanel from '@/components/LzPanel';
import { Form, Field, Radio, Input, Button, Upload, Message } from '@alifd/next';
import { activityEditDisabled, downloadExcel, validateActivityThreshold } from '@/utils';
import { FormLayout, PageData } from '../../../util';
import { barcodeTemplateExport } from '@/api/v90020';
import { config } from 'ice';
import CONST from '@/utils/constant';

const FormItem = Form.Item;

const RadioGroup = Radio.Group;

const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}
export default ({ onChange, defaultValue, value, sRef }: Props) => {
  // 用于 Fusion Form 表单校验
  const field: Field = Field.useField();
  // 同装修收集数据
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  const [fileList, setFileList] = useState<File[]>([]);
  const [uploaderRef, setUploaderRef] = useState(false);
  const saveUploaderRef = (ref) => {
    if (!ref) {
      return;
    }
    setUploaderRef(ref.getInstance());
  };
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  // 同步活动设置数据
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  // 改变Barcode
  const onBarcodeChange = (barcodeString): void => {
    setData({ barcodeString });
  };
  // 失去焦点时转换数组
  const onBlurBarcodeChange = (): void => {
    const barcodeList = formData.barcodeString
      .split(/[,，\s\n]+/) // 使用正则表达式匹配多种分隔符
      .filter((barcode) => barcode.trim()) // 过滤空字符串
      .map((barcode) => barcode.trim()); // 去除前后空格
    setData({ barcodeList });
  };
  // 下载模板
  const downloadTemplate = (): void => {
    barcodeTemplateExport().then((data: any) => {
      downloadExcel(data, 'Barcode上传模板');
    });
  };
  // 通过Ref触发表单校验
  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: any = null;

      field.validate((errors): void => {
        err = errors;
      });
      return validateActivityThreshold(formData, err, field);
    },
  }));

  const checkBarcode = (rule, val, callback): void => {
    if (!val) {
      callback('请输入订单包含的Barcode');
      return;
    }
    const barcodeList = val.split(/[,，\s\n]+/).filter((barcode) => barcode.trim());
    if (barcodeList.length > 5000) {
      callback('最多添加5000个Barcode');
      return;
    }
    // 检查是否有重复的Barcode
    const uniqueBarcodes = new Set(barcodeList);
    if (uniqueBarcodes.size !== barcodeList.length) {
      callback('Barcode不能重复');
      return;
    }
    callback();
  };
  return (
    <div>
      <LzPanel
        title="新老客订单判定配置"
        subTitle={
          <div style={{ color: 'red' }}>注意：此处配置为线上新老客判断使用，活动创建后无法修改，配置时请仔细检查</div>
        }
      >
        <Form {...formItemLayout} field={field} disabled={activityEditDisabled()}>
          <FormItem label="订单时间" required>
            <RadioGroup value={formData.orderTime} onChange={(orderTime) => setData({ orderTime })} name="orderTime">
              <Radio value={365}>近365天</Radio>
              <Radio value={-1}>历史以来</Radio>
            </RadioGroup>
          </FormItem>
          <FormItem label="订单包含商品" required>
            <FormItem>
              <RadioGroup
                value={formData.orderSkuLimit}
                onChange={(orderSkuLimit) => setData({ orderSkuLimit })}
                name="orderSkuLimit"
              >
                <Radio value={1}>任意Barcode</Radio>
                <Radio value={2}>指定Barcode</Radio>
              </RadioGroup>
            </FormItem>
            {formData.orderSkuLimit === 2 && (
              <FormItem required requiredMessage="请输入订单包含的Barcode" validator={checkBarcode}>
                <Input.TextArea
                  value={formData.barcodeString}
                  name="barcodeString"
                  onChange={onBarcodeChange}
                  onBlur={onBlurBarcodeChange}
                  autoHeight={{ minRows: 8, maxRows: 40 }}
                  placeholder="请输入订单包含的Barcode，以英文逗号分隔"
                  className="form-input-ctrl"
                />
                {!activityEditDisabled() && (
                  <div style={{ display: 'flex', alignItems: 'center', marginTop: '10px' }}>
                    <Upload
                      action={`${config.baseURL}/90020/barcodeFile/upload`}
                      name="file"
                      method="post"
                      headers={{
                        token: localStorage.getItem(CONST.LZ_SSO_TOKEN),
                        prd: localStorage.getItem(CONST.LZ_SSO_PRD),
                      }}
                      ref={saveUploaderRef}
                      value={fileList}
                      limit={1}
                      listType="text"
                      accept=".xls,.xlsx"
                      onChange={(info) => {
                        if (info.length) {
                          if (info[0].size > 5 * 1024 * 1024) {
                            Message.error('文件大小不能超过5M');
                            return;
                          }
                        }
                        setFileList(info);
                      }}
                      onError={(res) => {
                        if (res.state === 'error') {
                          if (res.response?.message) {
                            Message.error(res.response?.message);
                          } else {
                            Message.error('文件错误，请上传正确的文件');
                          }
                        }
                      }}
                      onSuccess={(res) => {
                        console.log(res);
                        if (res.response.code === 200) {
                          console.log(res.response.data, '上传数据');
                          setData({
                            barcodeString: res.response.data.barcodeList.join(','),
                            barcodeList: res.response.data.barcodeList,
                          });
                          field.setError('barcodeString', '');
                        } else if (res.response?.message) {
                          Message.error(res.response?.message);
                        } else {
                          Message.error('文件错误，请上传正确的文件');
                        }
                        setFileList([]);
                      }}
                    >
                      <div className="next-upload-drag">
                        <p className="next-upload-drag-icon">
                          <Button type="primary" disabled={activityEditDisabled()}>
                            批量导入
                          </Button>
                        </p>
                      </div>
                    </Upload>
                    <div className="next-form-item-help" style={{ margin: '0 10px' }}>
                      最多添加5000个Barcode
                    </div>
                    <Button type="primary" text onClick={downloadTemplate}>
                      下载模板
                    </Button>
                  </div>
                )}
              </FormItem>
            )}
          </FormItem>
        </Form>
      </LzPanel>
    </div>
  );
};
