/**
 * Author: z<PERSON><PERSON><PERSON>
 * Date: 2023-06-08 14:58
 * Description:
 */
import React, { useReducer, useState, useEffect, useImperativeHandle } from 'react';
import { Form, Field, Table, Button, Dialog, Message, Input } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import LzDialog from '@/components/LzDialog';
import ChoosePrize from '@/components/ChoosePrizeForDZ';
import { activityEditDisabled, deepCopy } from '@/utils';
import { FormLayout, PageData, PRIZE_INFO, PRIZE_TYPE, PrizeInfo } from '../../../util';
import ChooseGoods from '@/components/ChooseGoods';
import styles from './style.module.scss';

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}

const FormItem = Form.Item;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

export default ({ sRef, onChange, defaultValue, value }: Props) => {
  // 使用类型化的 useReducer
  const [formData, setFormData] = useReducer<React.Reducer<PageData, PageData>>((prevState, action) => {
    return { ...prevState, ...action };
  }, value || defaultValue);
  const field: Field = Field.useField();

  // 选择奖品
  const [visible, setVisible] = useState(false);
  const [new2Visible, setNew2Visible] = useState(false);
  // 当前编辑行数据
  const [editValue, setEditValue] = useState(null);
  const [editNew2Value, setEditNew2Value] = useState(null);
  // 当前编辑的奖品
  const [prizeNum, setPrizeNum] = useState(1);
  // 新客奖品2的奖品下标
  const [newPrize2Index, setNewPrize2Index] = useState(0);

  // 同步/更新数据
  const setData = (data: PageData): void => {
    setFormData(data);
    // 使用深拷贝确保所有嵌套属性都被正确更新
    onChange(JSON.parse(JSON.stringify(data)));
  };
  const onPrizeChange = (data: any): boolean | void => {
    // 更新奖品数据
    const newFormData = deepCopy(formData) as PageData;
    const newPrizeIndex = newFormData.prizeList.findIndex((item) => item.type === prizeNum);
    if (activityEditDisabled()) {
      if (defaultValue.prizeList[newPrizeIndex].sendTotalCount > data.sendTotalCount) {
        Message.error(`发放份数不能小于${defaultValue.prizeList[newPrizeIndex].sendTotalCount}`);
        return;
      }
    }
    if (newPrizeIndex !== -1) {
      data.skuId = newFormData.prizeList[newPrizeIndex].skuId;
      data.skuInfo = newFormData.prizeList[newPrizeIndex].skuInfo;
      data.type = prizeNum;
      newFormData.prizeList[newPrizeIndex] = data;
      setData(newFormData);
      setVisible(false);
    } else {
      return false;
    }
  };
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);

  useEffect((): void => {
    if (formData.prizeList.findIndex((item) => item.type === 1) === -1) {
      formData.prizeList.push({
        ...deepCopy(PRIZE_INFO),
        type: 1,
      });
    }
    if (formData.prizeList.findIndex((item) => item.type === 2) === -1) {
      formData.prizeList.push({
        ...deepCopy(PRIZE_INFO),
        type: 2,
      });
    }
  }, []);

  const handleNewSkuChange = (data) => {
    const newFormData = deepCopy(formData) as PageData;
    const newPrizeIndex = newFormData.prizeList.findIndex((item) => item.type === 1);
    if (newPrizeIndex !== -1) {
      newFormData.prizeList[newPrizeIndex].skuInfo = data[0];
      newFormData.prizeList[newPrizeIndex].skuId = data[0]?.skuId;
      setData(newFormData);
    }
  };

  const handleOldSkuChange = (data) => {
    const newFormData = deepCopy(formData) as PageData;
    const newPrizeIndex = newFormData.prizeList.findIndex((item) => item.type === 2);
    if (newPrizeIndex !== -1) {
      newFormData.prizeList[newPrizeIndex].skuInfo = data[0];
      newFormData.prizeList[newPrizeIndex].skuId = data[0]?.skuId;
      setData(newFormData);
    }
  };

  useImperativeHandle(sRef, (): { submit: () => object } => ({
    submit: () => {
      let err: object | null = null;
      field.validate((errors): void => {
        err = errors;
      });
      return err;
    },
  }));
  const onCancel = (): void => {
    setVisible(false);
  };

  return (
    <div>
      <LzPanel
        title="奖品配置"
        subTitle={
          <div className="next-form-item-help" style={{ color: 'red' }}>
            注意：此处需配置优惠券对应绑定的商品sku，用户领取优惠券后，点击【立即购买】按钮将跳转至该sku，如配置错误将导致客诉，请谨慎选择。
          </div>
        }
      >
        <Form {...formItemLayout} field={field}>
          <div>新客礼1.0奖品配置：</div>
          <FormItem label="优惠券奖品" required>
            <Table dataSource={formData.prizeList.filter((item) => item.type === 1)} style={{ marginTop: '15px' }}>
              <Table.Column title="奖项名称" dataIndex="prizeName" />
              <Table.Column
                title="奖项类型"
                cell={(_, index, row) => <div>{PRIZE_TYPE[row.prizeType]}</div>}
                dataIndex="prizeType"
              />
              <Table.Column
                title="单位数量"
                cell={(_, index, row) => {
                  if (row.prizeType === 1) {
                    return <div>{row.numPerSending ? `${row.numPerSending}张` : ''}</div>;
                  } else {
                    return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
                  }
                }}
              />
              <Table.Column
                title="发放份数"
                cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>}
              />
              <Table.Column
                title="单份价值(元)"
                cell={(_, index, row) => (
                  <div>
                    {PRIZE_TYPE[row.prizeType] && row.unitPrice === 0
                      ? row.unitPrice
                      : row.unitPrice
                      ? Number(row.unitPrice).toFixed(2)
                      : ''}
                  </div>
                )}
              />
              <Table.Column
                title="奖品图"
                cell={(_, index, row) => <img style={{ width: '30px' }} src={row.prizeImg} alt="" />}
              />

              <Table.Column
                title="操作"
                width={130}
                cell={(val, index, row) => (
                  <div>
                    <Button
                      text
                      type="primary"
                      onClick={() => {
                        setEditValue(row.prizeName ? row : null);
                        setPrizeNum(1);
                        setVisible(true);
                      }}
                    >
                      <i className={`iconfont icon-chaojiyingxiaoicon-25`} />
                    </Button>
                    {!activityEditDisabled() && (
                      <Button
                        text
                        type="primary"
                        onClick={() => {
                          if (row.prizeType) {
                            Dialog.confirm({
                              v2: true,
                              title: '提示',
                              centered: true,
                              content: '确认清空奖品？',
                              onOk: () => {
                                // 创建一个新对象，避免直接修改原对象
                                const newFormData = deepCopy(formData) as PageData;
                                const newPrizeIndex = newFormData.prizeList.findIndex((item) => item.type === 1);
                                console.log(newPrizeIndex, 'newPrizeIndex');

                                newFormData.prizeList[newPrizeIndex] = deepCopy(PRIZE_INFO);
                                newFormData.prizeList[newPrizeIndex].type = 1;
                                newFormData.prizeList[newPrizeIndex].skuId = formData.prizeList[newPrizeIndex].skuId;
                                newFormData.prizeList[newPrizeIndex].skuInfo =
                                  formData.prizeList[newPrizeIndex].skuInfo;
                                setData(newFormData);
                              },
                              onCancel: () => console.log('cancel'),
                            } as any);
                          }
                        }}
                      >
                        <i className={`iconfont icon-shanchu`} />
                      </Button>
                    )}
                  </div>
                )}
              />
            </Table>
          </FormItem>
          <FormItem label="优惠券商品配置" required requiredMessage="请选择商品">
            {!formData.prizeList.find((item) => item.type === 1)?.skuId && (
              <ChooseGoods
                max={1}
                value={
                  formData.prizeList.find((item) => item.type === 1)?.skuInfo
                    ? [formData.prizeList.find((item) => item.type === 1)?.skuInfo]
                    : []
                }
                onChange={handleNewSkuChange}
                sRef={null}
              />
            )}
            {formData.prizeList.find((item) => item.type === 1)?.skuId && (
              <Table
                dataSource={[formData.prizeList.find((item) => item.type === 1)?.skuInfo]}
                style={{ marginTop: '15px' }}
              >
                <Table.Column
                  title="商品名称"
                  dataIndex="skuName"
                  width={200}
                  cell={(val, index, row) => (
                    <div style={{ display: 'flex' }}>
                      <div className={styles.skuImg}>
                        <img src={row.skuMainPicture} alt="" style={{ width: '60px', height: '60px' }} />
                      </div>
                      <div className={styles.rightSku}>
                        <div className={styles.skuName}>{row.skuName}</div>
                        <div className={styles.gray}>商品编码{row.skuId}</div>
                      </div>
                    </div>
                  )}
                />
                <Table.Column title="京东价（元）" dataIndex="jdPrice" width={100} />
                {!activityEditDisabled() && (
                  <Table.Column
                    title="操作"
                    width={100}
                    cell={(val, index, _) => (
                      <Button
                        text
                        type="primary"
                        onClick={() => {
                          if (_.skuId) {
                            Dialog.confirm({
                              v2: true,
                              title: '提示',
                              centered: true,
                              content: '确认清空？',
                              onOk: () => {
                                // 创建一个新对象，避免直接修改原对象
                                const newFormData = deepCopy(formData) as PageData;
                                const newPrizeIndex = newFormData.prizeList.findIndex((item) => item.type === 1);
                                newFormData.prizeList[newPrizeIndex].skuInfo = null;
                                newFormData.prizeList[newPrizeIndex].skuId = '';
                                setData(newFormData);
                              },
                              onCancel: () => console.log('cancel'),
                            } as any);
                          }
                        }}
                      >
                        <i className={`iconfont icon-shanchu`} />
                      </Button>
                    )}
                  />
                )}
              </Table>
            )}
            <Input
              className="validateInput"
              name="newSkuList"
              value={formData.prizeList.find((item) => item.type === 1)?.skuId}
            />
          </FormItem>
          <div>老客礼1.0奖品配置：</div>
          <FormItem label="优惠券奖品" required>
            <Table dataSource={formData.prizeList.filter((item) => item.type === 2)} style={{ marginTop: '15px' }}>
              <Table.Column title="奖项名称" dataIndex="prizeName" />
              <Table.Column
                title="奖项类型"
                cell={(_, index, row) => <div>{PRIZE_TYPE[row.prizeType]}</div>}
                dataIndex="prizeType"
              />
              <Table.Column
                title="单位数量"
                cell={(_, index, row) => {
                  if (row.prizeType === 1) {
                    return <div>{row.numPerSending ? `${row.numPerSending}张` : ''}</div>;
                  } else {
                    return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
                  }
                }}
              />
              <Table.Column
                title="发放份数"
                cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>}
              />
              <Table.Column
                title="单份价值(元)"
                cell={(_, index, row) => (
                  <div>
                    {PRIZE_TYPE[row.prizeType] && row.unitPrice === 0
                      ? row.unitPrice
                      : row.unitPrice
                      ? Number(row.unitPrice).toFixed(2)
                      : ''}
                  </div>
                )}
              />
              <Table.Column
                title="奖品图"
                cell={(_, index, row) => <img style={{ width: '30px' }} src={row.prizeImg} alt="" />}
              />

              <Table.Column
                title="操作"
                width={130}
                cell={(val, index, row) => (
                  <div>
                    <Button
                      text
                      type="primary"
                      onClick={() => {
                        setEditValue(row.prizeName ? row : null);
                        setPrizeNum(2);
                        setVisible(true);
                      }}
                    >
                      <i className={`iconfont icon-chaojiyingxiaoicon-25`} />
                    </Button>
                    {!activityEditDisabled() && (
                      <Button
                        text
                        type="primary"
                        onClick={() => {
                          if (row.prizeType) {
                            Dialog.confirm({
                              v2: true,
                              title: '提示',
                              centered: true,
                              content: '确认清空奖品？',
                              onOk: () => {
                                // 创建一个新对象，避免直接修改原对象
                                const newFormData = deepCopy(formData) as PageData;
                                const newPrizeIndex = newFormData.prizeList.findIndex((item) => item.type === 2);
                                newFormData.prizeList[newPrizeIndex] = deepCopy(PRIZE_INFO);
                                newFormData.prizeList[newPrizeIndex].type = 2;
                                newFormData.prizeList[newPrizeIndex].skuId = formData.prizeList[newPrizeIndex].skuId;
                                newFormData.prizeList[newPrizeIndex].skuInfo =
                                  formData.prizeList[newPrizeIndex].skuInfo;
                                setData(newFormData);
                              },
                              onCancel: () => console.log('cancel'),
                            } as any);
                          }
                        }}
                      >
                        <i className={`iconfont icon-shanchu`} />
                      </Button>
                    )}
                  </div>
                )}
              />
            </Table>
          </FormItem>
          <FormItem label="优惠券商品配置" required requiredMessage="请选择商品">
            {!formData.prizeList.find((item) => item.type === 2)?.skuId && (
              <ChooseGoods
                max={1}
                value={
                  formData.prizeList.find((item) => item.type === 2)?.skuInfo
                    ? [formData.prizeList.find((item) => item.type === 2)?.skuInfo]
                    : []
                }
                onChange={handleOldSkuChange}
                sRef={null}
              />
            )}
            {formData.prizeList.find((item) => item.type === 2)?.skuId && (
              <Table
                dataSource={[formData.prizeList.find((item) => item.type === 2)?.skuInfo]}
                style={{ marginTop: '15px' }}
              >
                <Table.Column
                  title="商品名称"
                  dataIndex="skuName"
                  width={200}
                  cell={(val, index, row) => (
                    <div style={{ display: 'flex' }}>
                      <div className={styles.skuImg}>
                        <img src={row.skuMainPicture} alt="" style={{ width: '60px', height: '60px' }} />
                      </div>
                      <div className={styles.rightSku}>
                        <div className={styles.skuName}>{row.skuName}</div>
                        <div className={styles.gray}>商品编码{row.skuId}</div>
                      </div>
                    </div>
                  )}
                />
                <Table.Column title="京东价（元）" dataIndex="jdPrice" width={100} />
                {!activityEditDisabled() && (
                  <Table.Column
                    title="操作"
                    width={100}
                    cell={(val, index, _) => (
                      <Button
                        text
                        type="primary"
                        onClick={() => {
                          if (_.skuId) {
                            Dialog.confirm({
                              v2: true,
                              title: '提示',
                              centered: true,
                              content: '确认清空？',
                              onOk: () => {
                                // 创建一个新对象，避免直接修改原对象
                                const newFormData = deepCopy(formData) as PageData;
                                const newPrizeIndex = newFormData.prizeList.findIndex((item) => item.type === 2);
                                newFormData.prizeList[newPrizeIndex].skuInfo = null;
                                newFormData.prizeList[newPrizeIndex].skuId = '';
                                setData(newFormData);
                              },
                              onCancel: () => console.log('cancel'),
                            } as any);
                          }
                        }}
                      >
                        <i className={`iconfont icon-shanchu`} />
                      </Button>
                    )}
                  />
                )}
              </Table>
            )}
            <Input
              className="validateInput"
              name="odlSkuList"
              value={formData.prizeList.find((item) => item.type === 2)?.skuId}
            />
          </FormItem>
        </Form>
      </LzPanel>

      <LzDialog
        title={false}
        visible={visible}
        footer={false}
        onClose={() => setVisible(false)}
        style={{ width: '670px' }}
      >
        <ChoosePrize
          formData={formData}
          editValue={editValue}
          defaultEditValue={(defaultValue.prizeList.find((item) => item.type === prizeNum) as any) ?? null}
          onChange={onPrizeChange}
          onCancel={onCancel}
          hasProbability={false}
          hasLimit={false}
          typeList={[1]}
          defaultTarget={1}
        />
      </LzDialog>
    </div>
  );
};
