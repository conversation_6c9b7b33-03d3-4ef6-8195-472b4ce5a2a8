import React, { useState } from 'react';
import { CustomValue } from '../util';
// 基础信息
import Base from './components/Base';
import { Tab } from '@alifd/next';

interface Props {
  target: number;
  defaultValue: Required<CustomValue>;
  value: CustomValue | undefined;
  handleChange: (formData: Required<CustomValue>) => void;
}
interface EventProps extends Pick<Props, 'defaultValue' | 'value'> {
  onChange: (formData: Required<CustomValue>) => void;
}
export default (props: Props) => {
  const { target, defaultValue, value, handleChange } = props;
  const [activeKey, setActiveKey] = useState('1');
  const handleTabChange = (data) => {
    setActiveKey(data);
  };
  console.log('target', target);
  // 各装修自组件抛出的数据
  // 只负责转发，不作处理
  const onChange = (formData): void => {
    handleChange(formData);
  };
  const eventProps: EventProps = {
    defaultValue,
    value,
    onChange,
  };
  return (
    <div>
      <Tab activeKey={activeKey} defaultActiveKey="1" onChange={handleTabChange}>
        <Tab.Item title="页面装修" key="1" />
      </Tab>
      {activeKey === '1' && <>{<Base {...eventProps} />}</>}
    </div>
  );
};
