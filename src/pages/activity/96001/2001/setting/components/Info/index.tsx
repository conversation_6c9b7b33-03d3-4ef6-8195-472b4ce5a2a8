// import { Form, Checkbox, Field, Input, Button, Icon, Grid, Message } from '@alifd/next';
import { Form, Checkbox, Field, Grid } from '@alifd/next';
import { FormLayout, PageData } from '../../../util';
import React, { useEffect, useImperativeHandle, useReducer, useState } from 'react';
import { activityEditDisabled, validateActivityThreshold } from '@/utils';
import LzPanel from '../../../../../../../components/LzPanel';

const { Row, Col } = Grid;
const FormItem = Form.Item;

const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}

const options = [
  {
    num: '1',
    title: '姓名',
    type: 0,
    disabled: true,
  },
  {
    num: '2',
    title: '生日',
    type: 0,
    disabled: true,
  },
  {
    num: '3',
    title: '电话',
    type: 0,
    disabled: true,
  },
  {
    num: '4',
    title: '性别',
    type: 0,
    disabled: true,
  },
  {
    num: '5',
    title: '邮箱',
    type: 0,
    disabled: false,
  },
  {
    num: '6',
    title: '地址',
    type: 0,
    disabled: true,
  },
  {
    num: '7',
    title: '详细地址',
    type: 0,
    disabled: true,
  },
  // '姓名', '生日', '电话', '性别', '邮箱', '地址'
];

export default ({ onChange, defaultValue, value, sRef }: Props) => {
  // 用于 Fusion Form 表单校验
  const field: Field = Field.useField();
  // 同装修收集数据
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  // 同步活动设置数据
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  const [selectedOptions, setSelectedOptions] = useState<string[]>([]); // 用于跟踪已选中的选项
/*
  const handleAddCustomInput = () => {
    if (activityEditDisabled()) {
      Message.notice('进行中的活动不允许修改信息条目');
      return;
    }
    const updatedItemList = [
      ...formData.itemList,
      {
        num: '',
        title: '',
        type: 1,
      },
    ];
    let num = 7;
    updatedItemList.forEach((item) => {
      if (item.type === 1) {
        item.num = (num++).toString();
      }
    });
    if (formData.itemList.length >= 20) {
      Message.error('最多添加20个完善信息条目');
      return;
    }
    setData({ itemList: [...updatedItemList] });
  };
  const changeUserInput = (val, index) => {
    const itemList = JSON.parse(JSON.stringify(formData.itemList));
    itemList[index].title = val;
    setData({ itemList });
  };
 */
  const handleCheckboxChange = (selectedValues) => {
    setSelectedOptions(selectedValues);
    const updatedItemList = [
      ...selectedValues.map((item) => {
        const opt = options.find((option) => option.title === item);
        return opt;
      }),
      ...formData.itemList.filter((item) => item.type === 1),
    ];
    setData({ itemList: [...updatedItemList] });
  };
  /*
  // 删除自定义输入框
  const deleteCustomInput = (index) => () => {
    if (activityEditDisabled()) {
      Message.notice('进行中的活动不允许修改信息条目');
      return;
    }
    // 从formData.itemList中删除
    const itemList = JSON.parse(JSON.stringify(formData.itemList));
    itemList.splice(index, 1);
    let num = 7;
    itemList.forEach((item) => {
      if (item.type === 1) {
        item.num = (num++).toString();
      }
    });
    setData({ itemList });
  };
  */
  // 通过Ref触发表单校验
  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: any = null;

      field.validate((errors): void => {
        err = errors;
      });
      return validateActivityThreshold(formData, err, field);
    },
  }));
  useEffect(() => {
    const newSelectedOptions: any[] = [];
    const newUserInputs: any[] = [];
    formData.itemList.forEach((item) => {
      if (item.type === 0) {
        newSelectedOptions.push(item.title);
      } else {
        newUserInputs.push(item.title);
      }
    });
    setSelectedOptions(newSelectedOptions);
  }, []);
  useEffect(() => {
    setData(
      {
        itemList: [
          {
            num: '1',
            title: '姓名',
            type: 0,
          },
          {
            num: '3',
            title: '电话',
            type: 0,
          },
          {
            num: '4',
            title: '性别',
            type: 0,
          },
          {
            num: '2',
            title: '生日',
            type: 0,
          },
          {
            num: '6',
            title: '地址',
            type: 0,
          },
          {
            num: '7',
            title: '详细地址',
            type: 0,
            disabled: true,
          },
        ],
      },
    )
  }, []);
  return (
    <div>
      <LzPanel title="完善信息设置">
        <Form {...formItemLayout} field={field} disabled={activityEditDisabled()}>
          <FormItem name="options">
            <Row>
              <Col span="2" />
              <Col span="20">
                <Checkbox.Group onChange={handleCheckboxChange} value={selectedOptions}>
                  {options.map((option) => (
                    <Checkbox
                      key={option.num}
                      value={option.title}
                      disabled={activityEditDisabled() || option.disabled}
                    >
                      {option.title}
                    </Checkbox>
                  ))}
                </Checkbox.Group>
                {/*<Button type="primary" onClick={handleAddCustomInput}>*/}
                {/*  +自定义*/}
                {/*</Button>*/}
              </Col>
            </Row>
            {/*<div className={styles.red}>说明：最多添加20个完善信息条目</div>*/}
          </FormItem>
          {/*{formData.itemList.map((item, index) => (*/}
          {/*  <div key={index} className={styles.itemRow}>*/}
          {/*    <div className={styles.num}>{index + 1}：</div>*/}
          {/*    <div>*/}
          {/*      <Form.Item*/}
          {/*        disabled={!item.type}*/}
          {/*        label={item.type ? `自定义` : item.title}*/}
          {/*        required*/}
          {/*        requiredMessage="请输入搜集信息标题"*/}
          {/*        labelCol={{ span: 4 }}*/}
          {/*        wrapperCol={{ span: 13 }}*/}
          {/*      >*/}
          {/*        <Input*/}
          {/*          value={item.title}*/}
          {/*          label="题目标题"*/}
          {/*          placeholder="请输入搜集信息标题"*/}
          {/*          maxLength={8}*/}
          {/*          showLimitHint*/}
          {/*          style={{ width: '300px' }}*/}
          {/*          disabled={activityEditDisabled()}*/}
          {/*          name={`allInfo${index}`}*/}
          {/*          onChange={(val) => changeUserInput(val, index)}*/}
          {/*        />*/}
          {/*      </Form.Item>*/}
          {/*    </div>*/}
          {/*    <div>*/}
          {/*      {!!item.type && <Icon className={styles.delete} type="ashbin" onClick={deleteCustomInput(index)} />}*/}
          {/*    </div>*/}
          {/*  </div>*/}
          {/*))}*/}
        </Form>
      </LzPanel>
    </div>
  );
};
