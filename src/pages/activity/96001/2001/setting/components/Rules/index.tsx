import React, { useImperativeHandle, useReducer, useEffect } from 'react';
import { Form, Field, Input } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import { FormLayout, PageData } from '@/pages/activity/96001/2001/util';

const FormItem = Form.Item;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}

export default ({ onChange, defaultValue, value, sRef }: Props) => {
  const field: Field = Field.useField();
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: object | null = null;
      field.validate((errors): void => {
        err = errors;
      });
      return err;
    },
  }));
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  return (
    <div>
      <LzPanel title="活动规则">
        <Form {...formItemLayout} field={field}>
          <FormItem label="规则内容" required requiredMessage="请输入规则内容">
            <Input.TextArea
              value={formData.rules}
              name="rules"
              onChange={(rules) => setData({ rules })}
              autoHeight={{ minRows: 8, maxRows: 40 }}
              placeholder="请输入活动规则说明"
              maxLength={5000}
              showLimitHint
              className="form-input-ctrl"
            />
          </FormItem>
          <FormItem label=" " colon={false}>
            <div style={{ color: 'red', fontSize: '12px', display: 'flex' }}>
              <div>提示：</div>
              <div>
                <div>
                  （1）请商家务必输入活动规则，并检查活动规则信息内容与活动实际情况是否相符，若存在不符情况请修改后再发布活动；
                </div>
                <div>
                  （2）建议规则内显示奖品名称、奖品发放总量及奖品金额/价值等奖品相关信息的明确性，以免引起客诉；
                </div>
                <div>（3）若活动进行中修改了活动参数，请同步修改活动规则，以免引起客诉。</div>
              </div>
            </div>
          </FormItem>
        </Form>
      </LzPanel>
    </div>
  );
};
