import { Form, Checkbox, Field } from '@alifd/next';
import { FormLayout, PageData } from '../../../util';
import React, { useEffect, useImperativeHandle, useReducer } from 'react';
import { activityEditDisabled, validateActivityThreshold } from '@/utils';
import LzPanel from '@/components/LzPanel';
import styles from '@/pages/activity/96001/2001/complete/index.module.scss';

const FormItem = Form.Item;

const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}

export default ({ onChange, defaultValue, value, sRef }: Props) => {
  // 用于 Fusion Form 表单校验
  const field: Field = Field.useField();
  // 同装修收集数据
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  // 同步活动设置数据
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  const handleCheckboxChange = (isChecked) => {
    const selectedValue = isChecked ? 1 : 0;
    setData({ endActivity: selectedValue });
  };
  // const options = [{ 0: '全部奖品发完强制结束活动' }];
  // 通过Ref触发表单校验
  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: any = null;
      field.validate((errors): void => {
        err = errors;
      });
      return validateActivityThreshold(formData, err, field);
    },
  }));
  return (
    <div>
      <LzPanel title="活动监控">
        <Form {...formItemLayout} field={field} disabled={activityEditDisabled()}>
          <FormItem name="options" label={'活动强制结束'}>
            <Checkbox
              key={1}
              checked={formData.endActivity === 1}
              label={'全部奖品发完强制结束活动'}
              onChange={(isChecked) => handleCheckboxChange(isChecked)}
            />
            <div className={styles.grey}>注：勾选后，所有奖品发放完毕后，活动会自动结束</div>
          </FormItem>
        </Form>
      </LzPanel>
    </div>
  );
};
