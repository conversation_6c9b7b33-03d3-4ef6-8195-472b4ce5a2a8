import React, { useState } from 'react';
import { Tab } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import WinRecord from '@/pages/activity/96001/record/components/WinRecord';
import FilledRecord from '@/pages/activity/96001/record/components/FilledRecord';
import LzDocGuide from "@/components/LzDocGuide";

export default () => {
  const [activeKey, setActiveKey] = useState('1');
  return (
    <div className="crm-container">
      <LzPanel title="完善信息有礼数据报表" actions={<LzDocGuide />}>
        <Tab defaultActiveKey="1" activeKey={activeKey} onChange={(key) => setActiveKey(key)} unmountInactiveTabs>
          <Tab.Item title="填写记录" key="1">
            <FilledRecord />
          </Tab.Item>
          <Tab.Item title="获奖记录" key="2">
            <WinRecord />
          </Tab.Item>
        </Tab>
      </LzPanel>
    </div>
  );
};
