import React, { useEffect, useState } from 'react';
import { Form, Input, DatePicker2, Field, Table, Button, Dialog, Message } from '@alifd/next';
import constant from '@/utils/constant';
import LzPagination from '@/components/LzPagination';
import { dataWritingLog, dataWritingLogExport } from '@/api/v96001';
import Utils, { deepCopy, downloadExcel, getParams } from '@/utils';
import dayJs from 'dayjs';
import format from '@/utils/format';
import styles from '../style.module.scss';

const FormItem = Form.Item;
const { RangePicker } = DatePicker2;

const defaultPage = {
  pageNum: 1,
  pageSize: 10,
  total: 0,
};
interface Records {
  address: string;
  allInfo: string[];
  birthday: string;
  details: string;
  email: string;
  name: string;
  nickName: string;
  num: number;
  phoneNum: string;
  sex: string;
  submitTime: number;
}

const columnsDefault = [
  {
    key: 'name',
    title: '姓名',
  },
  {
    key: 'birthday',
    title: '生日',
  },
  {
    key: 'phoneNum',
    title: '电话',
  },
  {
    key: 'sex',
    title: '性别',
  },
  {
    key: 'email',
    title: '邮箱',
  },
  {
    key: 'address',
    title: '住址',
  },
];

export default (props) => {
  const field = Field.useField();
  const [pageInfo, setPage] = useState(deepCopy(defaultPage));
  const [tableData, setTableData] = useState<Records[]>([]);
  const [loading, setLoading] = useState(false);
  const [visible, setVisible] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<any[]>([]);
  const [columns, setColumns] = useState<any[]>([]);

  const defaultRangeVal = [
    dayJs(new Date(getParams('startTime'))).format('YYYY-MM-DD 00:00:00'),
    dayJs(new Date(getParams('endTime'))).format('YYYY-MM-DD 23:59:59'),
  ];
  const templateCode = getParams('templateCode');
  const handleSubmit = (e) => {
    e.preventDefault();
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  };

  // const onRowSelectionChange = (selectKey: string[]): void => {
  //   console.log(selectKey);
  // };
  // const rowSelection: {
  //   mode: 'single' | 'multiple' | undefined;
  //   onChange: (selectKey: string[]) => void;
  // } = {
  //   mode: 'multiple',
  //   onChange: (selectKey: string[]) => onRowSelectionChange(selectKey),
  // };
  const loadData = (query: any): void => {
    setLoading(true);
    query.activityId = getParams('id');
    dataWritingLog(query)
      .then((res: any): void => {
        if (res.records.length) {
          const columnsNew: any[] = [];
          columnsDefault.forEach((item) => {
            if (res.records[0][item.key]) {
              columnsNew.push(item);
            }
          });
          setColumns(columnsNew);
        }
        setTableData(res.records as Records[]);
        pageInfo.total = +res.total!;
        pageInfo.pageSize = +res.size!;
        pageInfo.pageNum = +res.current!;
        setPage(pageInfo);
        setLoading(false);
      })
      .catch((e) => {
        setLoading(false);
      });
  };
  const handlePage = ({ pageSize, pageNum }) => {
    setPage({
      ...pageInfo,
      pageSize,
      pageNum,
    });
    const formValue: any = field.getValues();
    loadData({ ...formValue, pageSize, pageNum });
  };

  const exportData = () => {
    const formValue: any = field.getValues();
    formValue.activityId = getParams('id');
    dataWritingLogExport(formValue).then((data: any) => downloadExcel(data, '填写记录'));
  };

  useEffect(() => {
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  }, []);

  const dialogData = (allInfo: string[]) => {
    const showDialog = (record) => {
      console.log('record', record);
      console.log('allInfo', allInfo);
      if (allInfo.length === 0) {
        record = ['暂无数据'];
        setSelectedRecord(record);
      } else {
        record = allInfo;
        setSelectedRecord(record);
      }
      setVisible(true);
    };
    return <Button onClick={showDialog}>查看</Button>;
  };
  const closeDialog = () => {
    setVisible(false);
  };

  return (
    <div style={{ minHeight: '350px' }}>
      <Form className="lz-query-criteria" field={field} colon labelAlign={'top'} onSubmit={handleSubmit}>
        <Form.Item name="nickName" label="用户昵称">
          <Input maxLength={20} placeholder="请输入用户昵称" />
        </Form.Item>
        <FormItem name="dateRange" label="提交时间">
          <RangePicker
            showTime
            hasClear={false}
            defaultValue={defaultRangeVal}
            format={constant.DATE_FORMAT_TEMPLATE}
          />
        </FormItem>
        <Form.Item name="pin" label="用户pin">
          <Input placeholder="请输入用户pin" />
        </Form.Item>
        <FormItem colon={false}>
          <Form.Submit type="primary" htmlType="submit">
            查询
          </Form.Submit>
          <Form.Reset
            toDefault
            onClick={() => {
              const formValue: any = field.getValues();
              loadData({ ...formValue, ...defaultPage });
            }}
          >
            重置
          </Form.Reset>
        </FormItem>
      </Form>
      <div style={{ margin: '10px 0', textAlign: 'right' }}>
        <Button onClick={exportData}>导出</Button>
      </div>
      <Table dataSource={tableData} loading={loading}>
        <Table.Column width={80} title="序号" dataIndex="num" />
        <Table.Column
          title="用户昵称"
          cell={(_, index, row) => (
            <div>
              {
                <div className="lz-copy-text">
                  {row.nickName ? Utils.mask(row.nickName) : '-'}
                  {row.nickName && (
                    <span
                      className={'iconfont icon-fuzhi'}
                      style={{ marginLeft: 5 }}
                      onClick={() => {
                        Utils.copyText(row.nickName).then(() => {
                          Message.success('用户昵称已复制到剪切板');
                        });
                      }}
                    />
                  )}
                </div>
              }
            </div>
          )}
        />
        <Table.Column
          title="用户pin"
          cell={(_, index, row) => (
            <div>
              {
                <div className="lz-copy-text">
                  {row.encryptPin ? Utils.mask(row.encryptPin) : '-'}
                  {row.encryptPin && (
                    <span
                      className={'iconfont icon-fuzhi'}
                      style={{ marginLeft: 5 }}
                      onClick={() => {
                        Utils.copyText(row.encryptPin).then(() => {
                          Message.success('用户pin已复制到剪切板');
                        });
                      }}
                    />
                  )}
                </div>
              }
            </div>
          )}
        />
        {columns.map((item) => {
          return <Table.Column key={item.key} title={item.title} dataIndex={item.key} />;
        })}
        {templateCode !== '2001' && (
          <Table.Column
            key="allInfo"
            title="自定义"
            cell={(value, index, row) => {
              return dialogData(row.allInfo);
            }}
          />
        )}
        <Table.Column
          title="提交时间"
          dataIndex="submitTime"
          cell={(value, index, data) => <div>{format.formatDateTimeDayjs(data.submitTime)}</div>}
        />
      </Table>
      {visible && (
        <Dialog
          title="自定义"
          visible={visible}
          v2
          width="640px"
          onClose={closeDialog}
          overflowScroll
          footer={
            <Button type="primary" onClick={closeDialog}>
              关闭
            </Button>
          }
        >
          <div className={styles.dialog}>
            {selectedRecord[0] !== '暂无数据' &&
              selectedRecord.map((item, index) => (
                <div className={styles.customizeItem}>
                  <div className={styles.number}>自定义{index + 1}</div>
                  <div className={styles.item} key={index}>
                    {item}
                  </div>
                </div>
              ))}
            {selectedRecord[0] === '暂无数据' && (
              <div className={styles.customizeItem}>
                <div className={styles.noData}>{selectedRecord[0]}</div>
              </div>
            )}
          </div>
        </Dialog>
      )}
      <LzPagination
        pageNum={pageInfo.pageNum}
        pageSize={pageInfo.pageSize}
        total={pageInfo.total}
        onChange={handlePage}
      />
    </div>
  );
};
