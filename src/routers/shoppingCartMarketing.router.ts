/**
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * Date: 2023-05-30 10:58
 * Description:
 */
import { lazy } from 'ice';

// 购物车营销
// 系统内置模板
export default [
  // 购物车营销
  {
    path: '/shoppingCartMarketing',
    exact: true,
    name: '购物车营销',
    component: lazy(() => import('@/pages/shoppingCartMarketing/view/index')),
  },
  {
    path: '/shoppingCartMarketingManage',
    exact: true,
    name: '购物车营销管理',
    component: lazy(() => import('@/pages/shoppingCartMarketing/manage')),
  },
];
