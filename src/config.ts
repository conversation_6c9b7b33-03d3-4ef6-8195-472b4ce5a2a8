/**
 * 工程配置
 */
export default {
  // 默认配置
  dev: {
    baseURL: '/apps/custom/api',
    v2BaseURL: 'https://crmbsaas-test.dianpusoft.cn/apps/interaction/v2/api',
    loglevel: 'trace',
    // 预览地址
    // 开发时应替换为自己的域名 <name>.isvjcloud.com
    // previewUrl: 'http://xuyanming.isvjcloud.com:8081//test/cc/custom',
    previewUrl: 'http://localhost:8081/test/cc/custom',
    // previewUrl: 'http://zhangyue.isvjcloud.com/test/cc/custom',
    // previewUrl: 'https://lzkjdz-isv.isvjcloud.com/test/cc/custom',
    v2PreviewUrl: 'https://crmcjyy-isv.isvjcloud.com/test/cc/interaction/v2'
  },
  // 默认配置
  local: {
    baseURL: '/apps/custom/api',
    loglevel: 'trace',
    previewUrl: 'https://lzkjdz-isv.isvjcloud.com/test/cc/custom',
    v2PreviewUrl: 'https://crmcjyy-isv.isvjcloud.com/test/cc/interaction/v2'
  },
  test: {
    baseURL: '/apps/custom/api',
    v2BaseURL: 'https://crmbsaas-test.dianpusoft.cn/apps/interaction/v2/api',
    loglevel: 'warn',
    previewUrl: 'https://lzkjdz-isv.isvjcloud.com/test/cc/custom',
    v2PreviewUrl: 'https://crmcjyy-isv.isvjcloud.com/test/cc/interaction/v2',
    smartCardUrl:
      'https://open-oauth.jd.com/oauth2/to_login?app_key=32E50FF565175FDDA315E959EA9E6254&response_type=code&redirect_uri=https://crmbsaas-test.dianpusoft.cn&state=20180416&scope=snsapi_base',
  },
  prod: {
    baseURL: '/apps/custom/api',
    v2BaseURL: 'https://crmbsaas.dianpusoft.cn/apps/interaction/v2/api',
    loglevel: 'error',
    previewUrl: 'https://lzkjdz-isv.isvjcloud.com/prod/cc/custom',
    v2PreviewUrl: 'https://crmcjyy-isv.isvjcloud.com/prod/cc/interaction/v2',
    smartCardUrl:
      'https://open-oauth.jd.com/oauth2/to_login?app_key=32E50FF565175FDDA315E959EA9E6254&response_type=code&redirect_uri=https://crmbsaas.dianpusoft.cn&state=20180416&scope=snsapi_base',
  },
};
