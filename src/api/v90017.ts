import {
  Activity90017ActivityDataRequest,
  Activity90017CreateOrUpdateRequest,
  Activity90017CreateOrUpdateResponse,
  Activity90017JoinLogRequest,
  BaseGetActivityRequest,
  BaseGetActivityResponse,
  CollectingReportRequest,
  IPageActivity90017JoinLogResponse,
  IPageActivity90017RankLogResponse,
  IPageActivity90017ReportResponse,
  IPageActivity90017UserWinningLogResponse,
} from './types';

import { request as httpRequest } from 'ice';

/**
 * @tags 爱他美老带新
 * @summary 创建活动
 * @request POST:/90017/createActivity
 */
export const createActivity = (
  request: Activity90017CreateOrUpdateRequest,
): Promise<Activity90017CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/90017/createActivity',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 爱他美老带新数据
 * @summary 助力记录
 * @request POST:/90017/data/joinLog
 */
export const dataJoinLog = (request: Activity90017JoinLogRequest): Promise<IPageActivity90017JoinLogResponse> => {
  return httpRequest({
    url: '/90017/data/joinLog',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 爱他美老带新数据
 * @summary 助力记录导出
 * @request POST:/90017/data/joinLog/export
 */
export const dataJoinLogExport = (request: Activity90017JoinLogRequest): Promise<void> => {
  return httpRequest({
    url: '/90017/data/joinLog/export',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 爱他美老带新数据
 * @summary 上传中奖人群包
 * @request POST:/90017/data/joinLog/uploadPin
 */
export const dataJoinLogUploadPin = (
  activity90017ActivityDataRequest: Activity90017ActivityDataRequest,
): Promise<void> => {
  return httpRequest({
    url: '/90017/data/joinLog/uploadPin',
    method: 'post',
    data: activity90017ActivityDataRequest,
  });
};

/**
 * @tags 爱他美老带新数据
 * @summary 排行榜记录
 * @request POST:/90017/data/rankLog
 */
export const dataRankLog = (request: Activity90017JoinLogRequest): Promise<IPageActivity90017RankLogResponse> => {
  return httpRequest({
    url: '/90017/data/rankLog',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 爱他美老带新数据
 * @summary 排行榜导出
 * @request POST:/90017/data/rankLog/export
 */
export const dataRankLogExport = (request: Activity90017ActivityDataRequest): Promise<void> => {
  return httpRequest({
    url: '/90017/data/rankLog/export',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 爱他美老带新数据
 * @summary 活动数据
 * @request POST:/90017/data/report
 */
export const dataReport = (request: CollectingReportRequest): Promise<IPageActivity90017ReportResponse> => {
  return httpRequest({
    url: '/90017/data/report',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 爱他美老带新数据
 * @summary 活动数据导出
 * @request POST:/90017/data/report/export
 */
export const dataReportExport = (request: CollectingReportRequest): Promise<void> => {
  return httpRequest({
    url: '/90017/data/report/export',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 爱他美老带新数据
 * @summary 上传中奖人群包
 * @request POST:/90017/data/winning/uploadPin
 */
export const dataWinningUploadPin = (
  activity90017ActivityDataRequest: Activity90017ActivityDataRequest,
): Promise<void> => {
  return httpRequest({
    url: '/90017/data/winning/uploadPin',
    method: 'post',
    data: activity90017ActivityDataRequest,
  });
};

/**
 * @tags 爱他美老带新数据
 * @summary 中奖记录
 * @request POST:/90017/data/winningLog
 */
export const dataWinningLog = (
  activity90017ActivityDataRequest: Activity90017ActivityDataRequest,
): Promise<IPageActivity90017UserWinningLogResponse> => {
  return httpRequest({
    url: '/90017/data/winningLog',
    method: 'post',
    data: activity90017ActivityDataRequest,
  });
};

/**
 * @tags 爱他美老带新数据
 * @summary 中奖记录导出
 * @request POST:/90017/data/winningLog/export
 */
export const dataWinningLogExport = (
  activity90017ActivityDataRequest: Activity90017ActivityDataRequest,
): Promise<void> => {
  return httpRequest({
    url: '/90017/data/winningLog/export',
    method: 'post',
    data: activity90017ActivityDataRequest,
  });
};

/**
 * @tags 爱他美老带新
 * @summary 查询活动信息
 * @request POST:/90017/getActivityInfo
 */
export const getActivityInfo = (request: BaseGetActivityRequest): Promise<BaseGetActivityResponse> => {
  return httpRequest({
    url: '/90017/getActivityInfo',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 爱他美老带新
 * @summary 修改活动
 * @request POST:/90017/updateActivity
 */
export const updateActivity = (
  request: Activity90017CreateOrUpdateRequest,
): Promise<Activity90017CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/90017/updateActivity',
    method: 'post',
    data: request,
  });
};
