import { Activity98725CreateOrUpdateRequest, Activity98725CreateOrUpdateResponse } from './types';

import { request as httpRequest } from 'ice';

/**
 * @tags 润百颜积分锁权B端控制器
 * @summary 创建活动
 * @request POST:/98725/createActivity
 */
export const createActivity = (
  request: Activity98725CreateOrUpdateRequest,
): Promise<Activity98725CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/98725/createActivity',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 润百颜积分锁权B端控制器
 * @summary 修改活动
 * @request POST:/98725/updateActivity
 */
export const updateActivity = (
  request: Activity98725CreateOrUpdateRequest,
): Promise<Activity98725CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/98725/updateActivity',
    method: 'post',
    data: request,
  });
};
