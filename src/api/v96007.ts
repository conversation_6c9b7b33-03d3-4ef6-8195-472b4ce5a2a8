import {
  Activity96007ActivityDataRequest,
  Activity96007CreateOrUpdateRequest,
  Activity96007CreateOrUpdateResponse,
  Activity96007SkuImportResponse,
  BaseGetActivityRequest,
  BaseGetActivityResponse,
  IPageActivity96007UserGiveLogResponse,
  IPageActivity96007UserWinningLogResponse,
} from './types';

import { request as httpRequest } from 'ice';

/**
 * @tags QQ星晒单礼
 * @summary 创建活动
 * @request POST:/96007/createActivity
 */
export const createActivity = (
  request: Activity96007CreateOrUpdateRequest,
): Promise<Activity96007CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/96007/createActivity',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 评价有礼数据
 * @summary 领奖机会赠送记录
 * @request POST:/96007/data/giveLog
 */
export const dataGiveLog = (
  activity96007ActivityDataRequest: Activity96007ActivityDataRequest,
): Promise<IPageActivity96007UserGiveLogResponse> => {
  return httpRequest({
    url: '/96007/data/giveLog',
    method: 'post',
    data: activity96007ActivityDataRequest,
  });
};

/**
 * @tags 评价有礼数据
 * @summary  领奖机会赠送记录导出
 * @request POST:/96007/data/giveLog/export
 */
export const dataGiveLogExport = (userDrawLogRequest96007: Activity96007ActivityDataRequest): Promise<void> => {
  return httpRequest({
    url: '/96007/data/giveLog/export',
    method: 'post',
    data: userDrawLogRequest96007,
  });
};

/**
 * @tags 评价有礼数据
 * @summary 上传人群包
 * @request POST:/96007/data/winning/uploadPin
 */
export const dataWinningUploadPin = (
  activity96007ActivityDataRequest: Activity96007ActivityDataRequest,
): Promise<void> => {
  return httpRequest({
    url: '/96007/data/winning/uploadPin',
    method: 'post',
    data: activity96007ActivityDataRequest,
  });
};

/**
 * @tags 评价有礼数据
 * @summary 中奖记录
 * @request POST:/96007/data/winningLog
 */
export const dataWinningLog = (
  activity96007ActivityDataRequest: Activity96007ActivityDataRequest,
): Promise<IPageActivity96007UserWinningLogResponse> => {
  return httpRequest({
    url: '/96007/data/winningLog',
    method: 'post',
    data: activity96007ActivityDataRequest,
  });
};

/**
 * @tags 评价有礼数据
 * @summary 中奖记录导出
 * @request POST:/96007/data/winningLog/export
 */
export const dataWinningLogExport = (
  activity96007ActivityDataRequest: Activity96007ActivityDataRequest,
): Promise<void> => {
  return httpRequest({
    url: '/96007/data/winningLog/export',
    method: 'post',
    data: activity96007ActivityDataRequest,
  });
};

/**
 * @tags QQ星晒单礼
 * @summary 查询活动信息
 * @request POST:/96007/getActivityInfo
 */
export const getActivityInfo = (request: BaseGetActivityRequest): Promise<BaseGetActivityResponse> => {
  return httpRequest({
    url: '/96007/getActivityInfo',
    method: 'post',
    data: request,
  });
};

/**
 * @tags QQ星晒单礼
 * @summary 导入系列sku信息excel
 * @request POST:/96007/importSkuExcel
 */
export const importSkuExcel = (file: any): Promise<Activity96007SkuImportResponse[]> => {
  return httpRequest({
    url: '/96007/importSkuExcel',
    method: 'post',
    data: file,
  });
};

/**
 * @tags QQ星晒单礼
 * @summary 下载系列sku模板
 * @request POST:/96007/skuTemplate/export
 */
export const skuTemplateExport = (): Promise<void> => {
  return httpRequest({
    url: '/96007/skuTemplate/export',
    method: 'post',
  });
};

/**
 * @tags QQ星晒单礼
 * @summary 修改活动
 * @request POST:/96007/updateActivity
 */
export const updateActivity = (
  request: Activity96007CreateOrUpdateRequest,
): Promise<Activity96007CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/96007/updateActivity',
    method: 'post',
    data: request,
  });
};
